import React, { useState, useMemo, useEffect } from 'react';
import { ArmyCardData, CustomCharacterClass } from '../types';
import { ARMY_RARITIES, CHARACTER_CLASSES } from '../services/geminiService';

interface ArmyGalleryProps {
  armyCards: ArmyCardData[];
  onCardClick: (card: ArmyCardData) => void;
  onCardDelete?: (cardId: string) => void;
  onCardUpdate?: (card: ArmyCardData) => void;
  selectedCard?: ArmyCardData | null;
  refreshTrigger?: number; // 新增：用于触发刷新的计数器
}

export const ArmyGallery: React.FC<ArmyGalleryProps> = ({
  armyCards,
  onCardClick,
  onCardDelete,
  onCardUpdate,
  selectedCard,
  refreshTrigger
}) => {
  const [rarityFilter, setRarityFilter] = useState<string>('all');
  const [classFilter, setClassFilter] = useState<string>('all');
  const [styleFilter, setStyleFilter] = useState<string>('all');
  const [editingCard, setEditingCard] = useState<string | null>(null);
  const [editingStyle, setEditingStyle] = useState<string>('');

  // 排序状态
  const [sortBy, setSortBy] = useState<string>('default');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  // 批量编辑状态
  const [isBatchMode, setIsBatchMode] = useState(false);
  const [selectedCards, setSelectedCards] = useState<Set<string>>(new Set());
  const [showBatchEditModal, setShowBatchEditModal] = useState(false);
  const [batchEditStyle, setBatchEditStyle] = useState('');

  // 分页状态
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 40; // 4行 × 10列 = 40个卡牌每页

  // 自定义职业状态
  const [customClasses, setCustomClasses] = useState<CustomCharacterClass[]>([]);

  // 加载自定义职业
  useEffect(() => {
    const loadCustomClasses = async () => {
      try {
        if (window.electronAPI?.database) {
          const classes = await window.electronAPI.database.getCustomClasses();
          setCustomClasses(classes);
        }
      } catch (error) {
        console.error('Failed to load custom classes:', error);
      }
    };
    loadCustomClasses();
  }, [refreshTrigger]); // 依赖refreshTrigger，当它变化时重新加载

  // 获取所有阵营标签
  const allFactions = useMemo(() => {
    const factions = new Set<string>();
    armyCards.forEach(card => {
      const faction = card.stylePrompt || '未知阵营';
      factions.add(faction);
    });
    return Array.from(factions).sort();
  }, [armyCards]);

  const filteredCards = useMemo(() => {
    let filtered = armyCards
      .filter(card => rarityFilter === 'all' || card.rarity.level === rarityFilter)
      .filter(card => {
        if (classFilter === 'all') return true;
        if (classFilter === 'custom') {
          // 检查是否是自定义职业
          return customClasses.some(cc => cc.name === card.characterClass);
        }
        return card.characterClass === classFilter;
      })
      .filter(card => {
        if (styleFilter === 'all') return true;
        const cardFaction = card.stylePrompt || '未知阵营';
        return cardFaction === styleFilter;
      });

    // 排序逻辑
    filtered.sort((a, b) => {
      let comparison = 0;

      switch (sortBy) {
        case 'totalPower':
          const aPower = a.attributes?.totalPower || 0;
          const bPower = b.attributes?.totalPower || 0;
          comparison = aPower - bPower;
          break;
        case 'attack':
          const aAttack = a.attributes?.attack || 0;
          const bAttack = b.attributes?.attack || 0;
          comparison = aAttack - bAttack;
          break;
        case 'defense':
          const aDefense = a.attributes?.defense || 0;
          const bDefense = b.attributes?.defense || 0;
          comparison = aDefense - bDefense;
          break;
        case 'health':
          const aHealth = a.attributes?.health || 0;
          const bHealth = b.attributes?.health || 0;
          comparison = aHealth - bHealth;
          break;
        case 'speed':
          const aSpeed = a.attributes?.speed || 0;
          const bSpeed = b.attributes?.speed || 0;
          comparison = aSpeed - bSpeed;
          break;
        case 'intelligence':
          const aIntelligence = a.attributes?.intelligence || 0;
          const bIntelligence = b.attributes?.intelligence || 0;
          comparison = aIntelligence - bIntelligence;
          break;
        case 'leadership':
          const aLeadership = a.attributes?.leadership || 0;
          const bLeadership = b.attributes?.leadership || 0;
          comparison = aLeadership - bLeadership;
          break;
        case 'rarity':
          const rarityOrder = { '传说': 4, '史诗': 3, '稀有': 2, '不常见': 1, '普通': 0 };
          const aOrder = rarityOrder[a.rarity.level as keyof typeof rarityOrder] || 0;
          const bOrder = rarityOrder[b.rarity.level as keyof typeof rarityOrder] || 0;
          comparison = aOrder - bOrder;
          break;
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        default: // 'default'
          // 默认按稀有度排序（传说 > 史诗 > 稀有 > 普通）
          const defaultRarityOrder = { '传说': 4, '史诗': 3, '稀有': 2, '不常见': 1, '普通': 0 };
          const aDefaultOrder = defaultRarityOrder[a.rarity.level as keyof typeof defaultRarityOrder] || 0;
          const bDefaultOrder = defaultRarityOrder[b.rarity.level as keyof typeof defaultRarityOrder] || 0;
          if (aDefaultOrder !== bDefaultOrder) {
            comparison = aDefaultOrder - bDefaultOrder;
          } else {
            // 相同稀有度按名称排序
            comparison = a.name.localeCompare(b.name);
          }
          break;
      }

      return sortOrder === 'desc' ? -comparison : comparison;
    });

    return filtered;
  }, [armyCards, rarityFilter, classFilter, styleFilter, customClasses, sortBy, sortOrder]);

  // 分页计算
  const totalPages = Math.ceil(filteredCards.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentPageCards = filteredCards.slice(startIndex, endIndex);

  // 当筛选条件改变时重置到第一页
  React.useEffect(() => {
    setCurrentPage(1);
  }, [rarityFilter, classFilter, styleFilter]);

  // 开始编辑风格
  const startEditingStyle = (card: ArmyCardData) => {
    setEditingCard(card.id);
    setEditingStyle(card.stylePrompt || '');
  };

  // 保存风格编辑
  const saveStyleEdit = (card: ArmyCardData) => {
    if (onCardUpdate) {
      const updatedCard = {
        ...card,
        stylePrompt: editingStyle.trim() || '未知阵营'
      };
      onCardUpdate(updatedCard);
    }
    setEditingCard(null);
    setEditingStyle('');
  };

  // 取消编辑
  const cancelEdit = () => {
    setEditingCard(null);
    setEditingStyle('');
  };

  // 批量编辑相关函数
  const toggleBatchMode = () => {
    setIsBatchMode(!isBatchMode);
    setSelectedCards(new Set());
    setEditingCard(null);
  };

  const toggleCardSelection = (cardId: string) => {
    const newSelected = new Set(selectedCards);
    if (newSelected.has(cardId)) {
      newSelected.delete(cardId);
    } else {
      newSelected.add(cardId);
    }
    setSelectedCards(newSelected);
  };

  const confirmBatchEdit = () => {
    if (selectedCards.size === 0) {
      alert('请先选择要修改的卡牌');
      return;
    }
    setShowBatchEditModal(true);
  };

  const executeBatchEdit = () => {
    if (!onCardUpdate || !batchEditStyle.trim()) {
      return;
    }

    const cardsToUpdate = armyCards.filter(card => selectedCards.has(card.id));
    cardsToUpdate.forEach(card => {
      const updatedCard = {
        ...card,
        stylePrompt: batchEditStyle.trim()
      };
      onCardUpdate(updatedCard);
    });

    // 重置状态
    setShowBatchEditModal(false);
    setBatchEditStyle('');
    setSelectedCards(new Set());
    setIsBatchMode(false);
  };

  const cancelBatchEdit = () => {
    setShowBatchEditModal(false);
    setBatchEditStyle('');
  };

  const handleCardDelete = (e: React.MouseEvent, cardId: string) => {
    e.stopPropagation();
    if (onCardDelete) {
      onCardDelete(cardId);
    }
  };

  return (
    <div className="w-full">
      <div className="mb-6">
        <div className="flex justify-between items-center mb-2">
          <h2 className="text-2xl font-bold text-gray-300">我的军队收藏</h2>

          {/* Batch Edit Controls */}
          <div className="flex items-center space-x-2">
            {isBatchMode && selectedCards.size > 0 && (
              <span className="text-sm text-amber-400">
                已选择 {selectedCards.size} 张卡牌
              </span>
            )}
            <button
              onClick={isBatchMode ? confirmBatchEdit : toggleBatchMode}
              className={`px-4 py-2 rounded-lg font-medium text-white transition-all duration-200 text-sm ${
                isBatchMode
                  ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700'
                  : 'bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700'
              } shadow-md hover:scale-105`}
            >
              {isBatchMode ? '确认批量编辑' : '批量编辑'}
            </button>
            {isBatchMode && (
              <button
                onClick={toggleBatchMode}
                className="px-3 py-2 rounded-lg font-medium text-white bg-gray-600 hover:bg-gray-700 transition-all duration-200 text-sm"
              >
                取消
              </button>
            )}
          </div>
        </div>
        
        {/* Rarity Probabilities Display */}
        <div className="flex justify-center flex-wrap items-center gap-x-4 gap-y-1 mb-4 text-sm text-gray-400">
          <span className="font-bold mr-2">稀有度概率:</span>
          {ARMY_RARITIES.map(r => (
            <span key={r.level} className="whitespace-nowrap">
              <span className={`${r.color} font-semibold`}>{r.level}</span>: {(r.probability * 100).toFixed(0)}%
            </span>
          ))}
        </div>
      </div>

      <div className="p-4 bg-black/20 rounded-lg">
        {/* Filters */}
        <div className="flex flex-wrap justify-center items-center gap-4 mb-6">
          <div>
            <label htmlFor="rarity-filter" className="mr-2 text-gray-400">稀有度:</label>
            <select 
              id="rarity-filter" 
              value={rarityFilter} 
              onChange={e => setRarityFilter(e.target.value)} 
              className="bg-gray-700 text-white p-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="all">全部</option>
              {ARMY_RARITIES.map(rarity => (
                <option key={rarity.level} value={rarity.level}>{rarity.level}</option>
              ))}
            </select>
          </div>
          
          <div>
            <label htmlFor="class-filter" className="mr-2 text-gray-400">职业:</label>
            <select
              id="class-filter"
              value={classFilter}
              onChange={e => setClassFilter(e.target.value)}
              className="bg-gray-700 text-white p-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="all">全部</option>
              {Object.keys(CHARACTER_CLASSES).map(className => (
                <option key={className} value={className}>{className}</option>
              ))}
              {customClasses.length > 0 && (
                <option value="custom" className="text-amber-300">⭐ 自定义职业</option>
              )}
            </select>
          </div>

          <div>
            <label htmlFor="style-filter" className="mr-2 text-gray-400">阵营:</label>
            <select
              id="style-filter"
              value={styleFilter}
              onChange={e => setStyleFilter(e.target.value)}
              className="bg-gray-700 text-white p-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="all">全部</option>
              {allFactions.map(faction => (
                <option key={faction} value={faction}>{faction}</option>
              ))}
            </select>
          </div>

          <div>
            <label htmlFor="sort-filter" className="mr-2 text-gray-400">排序:</label>
            <select
              id="sort-filter"
              value={sortBy}
              onChange={e => setSortBy(e.target.value)}
              className="bg-gray-700 text-white p-2 rounded-md border border-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500"
            >
              <option value="default">默认</option>
              <option value="totalPower">综合战力</option>
              <option value="attack">攻击力</option>
              <option value="defense">防御力</option>
              <option value="health">生命值</option>
              <option value="speed">速度</option>
              <option value="intelligence">智力</option>
              <option value="leadership">领导力</option>
              <option value="rarity">稀有度</option>
              <option value="name">名称</option>
            </select>
          </div>

          <div>
            <button
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="bg-gray-700 text-white p-2 rounded-md border border-gray-600 hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500 transition-colors"
              title={`当前: ${sortOrder === 'desc' ? '降序' : '升序'}`}
            >
              {sortOrder === 'desc' ? '↓' : '↑'}
            </button>
          </div>
        </div>

        {/* Pagination Info */}
        {filteredCards.length > 0 && (
          <div className="text-center text-sm text-gray-400 mb-4">
            显示第 {startIndex + 1}-{Math.min(endIndex, filteredCards.length)} 项，共 {filteredCards.length} 项
            {totalPages > 1 && ` | 第 ${currentPage} 页，共 ${totalPages} 页`}
          </div>
        )}

        {/* Cards Grid */}
        <div className="flex justify-center gap-3 md:gap-4 flex-wrap min-h-[10rem]">
          {currentPageCards.length > 0 ? (
            currentPageCards.map(card => (
              <div key={card.id} className="relative group">
                {/* Batch Selection Checkbox */}
                {isBatchMode && (
                  <div className="absolute -top-2 -left-2 z-10">
                    <input
                      type="checkbox"
                      checked={selectedCards.has(card.id)}
                      onChange={() => toggleCardSelection(card.id)}
                      className="w-5 h-5 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      onClick={(e) => e.stopPropagation()}
                    />
                  </div>
                )}

                {/* Delete Button */}
                {onCardDelete && !isBatchMode && (
                  <button
                    className="absolute -top-2 -right-2 w-6 h-6 bg-red-600 hover:bg-red-700 text-white rounded-full text-xs font-bold z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-200 flex items-center justify-center"
                    onClick={(e) => handleCardDelete(e, card.id)}
                    title="删除卡片"
                    aria-label={`删除卡片 ${card.name}`}
                  >
                    ×
                  </button>
                )}
                
                {/* Card Image */}
                <img 
                  src={card.imageUrl} 
                  alt={card.name}
                  className={`w-24 h-36 object-cover rounded-lg border-2 transition-all duration-200 transform hover:scale-105 cursor-pointer ${
                    selectedCard?.id === card.id 
                      ? 'border-yellow-400 scale-105 shadow-lg shadow-yellow-400/30' 
                      : 'border-gray-700 hover:border-purple-400'
                  }`}
                  style={{
                    boxShadow: selectedCard?.id === card.id 
                      ? `0 0 20px ${card.rarity.glowColor}60` 
                      : `0 0 10px ${card.rarity.glowColor}30`
                  }}
                  title={`${card.name} (${card.rarity.level}) - ${card.characterClass}`}
                  onClick={() => isBatchMode ? toggleCardSelection(card.id) : onCardClick(card)}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter') {
                      isBatchMode ? toggleCardSelection(card.id) : onCardClick(card);
                    }
                  }}
                  tabIndex={0}
                />
                
                {/* Card Info Overlay */}
                <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/90 to-transparent p-2 rounded-b-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200">
                  <p className="text-white text-xs font-semibold truncate">{card.name}</p>
                  <p className={`text-xs ${card.rarity.color}`}>{card.rarity.level}</p>

                  {/* Style Tag and Edit */}
                  <div className="mt-1">
                    {editingCard === card.id ? (
                      <div className="flex items-center space-x-1">
                        <input
                          type="text"
                          value={editingStyle}
                          onChange={(e) => setEditingStyle(e.target.value)}
                          className="flex-1 text-xs bg-gray-800 text-white px-1 py-0.5 rounded border border-gray-600 focus:outline-none focus:border-blue-400"
                          placeholder="输入阵营标签"
                          onKeyDown={(e) => {
                            e.stopPropagation();
                            if (e.key === 'Enter') saveStyleEdit(card);
                            if (e.key === 'Escape') cancelEdit();
                          }}
                          autoFocus
                        />
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            saveStyleEdit(card);
                          }}
                          className="text-green-400 hover:text-green-300 text-xs"
                          title="保存"
                        >
                          ✓
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            cancelEdit();
                          }}
                          className="text-red-400 hover:text-red-300 text-xs"
                          title="取消"
                        >
                          ✕
                        </button>
                      </div>
                    ) : (
                      <div className="flex items-center justify-between">
                        <span className="text-xs text-gray-300 truncate flex-1">
                          ⚔️ {card.stylePrompt || '未知阵营'}
                        </span>
                        {onCardUpdate && (
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              startEditingStyle(card);
                            }}
                            className="text-blue-400 hover:text-blue-300 text-xs ml-1"
                            title="编辑阵营标签"
                          >
                            ✏️
                          </button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            ))
          ) : (
            <div className="text-gray-500 text-center self-center col-span-full py-8">
              {armyCards.length === 0
                ? "你的军队收藏是空的。完成习惯，招募你的第一名士兵！"
                : "没有符合筛选条件的卡片。"}
            </div>
          )}
        </div>

        {/* Pagination Controls */}
        {totalPages > 1 && (
          <div className="flex justify-center items-center space-x-2 mt-6">
            <button
              onClick={() => setCurrentPage(1)}
              disabled={currentPage === 1}
              className="px-3 py-2 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-800 disabled:cursor-not-allowed text-white rounded text-sm transition-colors"
            >
              首页
            </button>
            <button
              onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
              disabled={currentPage === 1}
              className="px-3 py-2 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-800 disabled:cursor-not-allowed text-white rounded text-sm transition-colors"
            >
              上一页
            </button>

            {/* Page Numbers */}
            <div className="flex space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                let pageNum;
                if (totalPages <= 5) {
                  pageNum = i + 1;
                } else if (currentPage <= 3) {
                  pageNum = i + 1;
                } else if (currentPage >= totalPages - 2) {
                  pageNum = totalPages - 4 + i;
                } else {
                  pageNum = currentPage - 2 + i;
                }

                return (
                  <button
                    key={pageNum}
                    onClick={() => setCurrentPage(pageNum)}
                    className={`px-3 py-2 text-sm rounded transition-colors ${
                      currentPage === pageNum
                        ? 'bg-blue-600 text-white'
                        : 'bg-gray-600 hover:bg-gray-500 text-white'
                    }`}
                  >
                    {pageNum}
                  </button>
                );
              })}
            </div>

            <button
              onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
              disabled={currentPage === totalPages}
              className="px-3 py-2 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-800 disabled:cursor-not-allowed text-white rounded text-sm transition-colors"
            >
              下一页
            </button>
            <button
              onClick={() => setCurrentPage(totalPages)}
              disabled={currentPage === totalPages}
              className="px-3 py-2 bg-gray-600 hover:bg-gray-500 disabled:bg-gray-800 disabled:cursor-not-allowed text-white rounded text-sm transition-colors"
            >
              末页
            </button>
          </div>
        )}

        {/* Collection Stats */}
        {armyCards.length > 0 && (
          <div className="mt-6 text-center">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
              <div className="bg-gray-700/50 p-3 rounded-lg">
                <div className="text-gray-400">总收藏</div>
                <div className="text-xl font-bold text-white">{armyCards.length}</div>
              </div>
              
              {ARMY_RARITIES.map(rarity => {
                const count = armyCards.filter(card => card.rarity.level === rarity.level).length;
                return (
                  <div key={rarity.level} className="bg-gray-700/50 p-3 rounded-lg">
                    <div className={`${rarity.color} font-semibold`}>{rarity.level}</div>
                    <div className="text-xl font-bold text-white">{count}</div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>

      {/* Batch Edit Modal */}
      {showBatchEditModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-gray-800 rounded-lg p-6 w-96 max-w-[90vw]">
            <h3 className="text-xl font-bold text-white mb-4">批量修改阵营标签</h3>
            <p className="text-gray-300 mb-4">
              将为 {selectedCards.size} 张卡牌设置相同的阵营标签
            </p>

            <div className="mb-4">
              <label className="block text-sm font-medium text-gray-300 mb-2">
                新的阵营标签:
              </label>
              <input
                type="text"
                value={batchEditStyle}
                onChange={(e) => setBatchEditStyle(e.target.value)}
                placeholder="例如：光明圣殿、暗影军团"
                className="w-full p-3 bg-gray-700 text-white rounded border border-gray-600 focus:outline-none focus:ring-2 focus:ring-blue-500"
                autoFocus
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={executeBatchEdit}
                disabled={!batchEditStyle.trim()}
                className="flex-1 py-2 px-4 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded transition-colors"
              >
                确认修改
              </button>
              <button
                onClick={cancelBatchEdit}
                className="flex-1 py-2 px-4 bg-gray-600 hover:bg-gray-700 text-white rounded transition-colors"
              >
                取消
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
