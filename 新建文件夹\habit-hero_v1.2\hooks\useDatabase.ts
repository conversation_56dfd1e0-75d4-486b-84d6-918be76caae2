import { useState, useEffect, useCallback } from 'react';
import { UserData, ArmyCardData, Task, JournalEntry, WeightRecord, AttributeSchema } from '../types';

/**
 * Database hook that works in both browser and Electron environments
 * Falls back to localStorage in browser, uses SQLite in Electron
 */

interface DatabaseAPI {
  getUserData: () => Promise<UserData>;
  saveArmyCard: (card: ArmyCardData) => Promise<void>;

  updateUserPoints: (points: number) => Promise<void>;
  addTotalPoints: (pointsToAdd: number) => Promise<void>;
  setTotalPoints: (totalPoints: number) => Promise<void>;
  updateArmyStylePrompt: (prompt: string) => Promise<void>;
  deleteArmyCard: (cardId: string) => Promise<void>;

  saveTask: (task: Task) => Promise<void>;
  deleteTask: (taskId: string) => Promise<void>;
  saveJournalEntry: (entry: JournalEntry) => Promise<void>;
  deleteJournalEntry: (entryId: string) => Promise<void>;
  saveWeightRecord: (record: WeightRecord) => Promise<void>;
  deleteWeightRecord: (id: string) => Promise<void>;

  saveAttributeSchema: (schema: AttributeSchema) => Promise<void>;
  getStats: () => Promise<any>;
}

// Check if we're in Electron environment
const isElectron = typeof window !== 'undefined' && window.electronAPI;

// Fallback localStorage implementation
class LocalStorageDB implements DatabaseAPI {
  private readonly STORAGE_KEY = 'habitHeroData_v4_army_system';

  async getUserData(): Promise<UserData> {
    try {
      const data = localStorage.getItem(this.STORAGE_KEY);
      if (data) {
        return JSON.parse(data);
      }
    } catch (error) {
      // Error reading from localStorage
    }
    
    // Return default data
    return {
      points: 100,
      totalPointsEarned: 0,
      tasks: [],
      journalEntries: [],
      weightRecords: [],
      armyCards: [],

      armyStylePrompt: '',
    };
  }

  async saveUserData(userData: UserData): Promise<void> {
    try {
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(userData));
    } catch (error) {
      throw error;
    }
  }

  async saveArmyCard(card: ArmyCardData): Promise<void> {
    const userData = await this.getUserData();
    const existingIndex = userData.armyCards.findIndex(c => c.id === card.id);
    
    if (existingIndex >= 0) {
      userData.armyCards[existingIndex] = card;
    } else {
      userData.armyCards.unshift(card);
    }
    
    await this.saveUserData(userData);
  }



  async updateUserPoints(points: number): Promise<void> {
    const userData = await this.getUserData();
    userData.points = points;
    await this.saveUserData(userData);
  }

  async addTotalPoints(pointsToAdd: number): Promise<void> {
    const userData = await this.getUserData();
    userData.totalPointsEarned = (userData.totalPointsEarned || 0) + pointsToAdd;
    await this.saveUserData(userData);
  }

  async setTotalPoints(totalPoints: number): Promise<void> {
    const userData = await this.getUserData();
    userData.totalPointsEarned = totalPoints;
    await this.saveUserData(userData);
  }

  async updateArmyStylePrompt(prompt: string): Promise<void> {
    const userData = await this.getUserData();
    userData.armyStylePrompt = prompt;
    await this.saveUserData(userData);
  }

  async deleteArmyCard(cardId: string): Promise<void> {
    const userData = await this.getUserData();
    userData.armyCards = userData.armyCards.filter(card => card.id !== cardId);
    await this.saveUserData(userData);
  }

  async saveTask(task: Task): Promise<void> {
    const userData = await this.getUserData();
    const existingIndex = userData.tasks.findIndex(t => t.id === task.id);

    if (existingIndex >= 0) {
      userData.tasks[existingIndex] = task;
    } else {
      userData.tasks.push(task);
    }

    await this.saveUserData(userData);
  }

  async deleteTask(taskId: string): Promise<void> {
    const userData = await this.getUserData();
    userData.tasks = userData.tasks.filter(task => task.id !== taskId);
    await this.saveUserData(userData);
  }

  async saveJournalEntry(entry: JournalEntry): Promise<void> {
    const userData = await this.getUserData();
    const existingIndex = userData.journalEntries.findIndex(e => e.id === entry.id);

    if (existingIndex >= 0) {
      userData.journalEntries[existingIndex] = entry;
    } else {
      userData.journalEntries.push(entry);
    }

    await this.saveUserData(userData);
  }

  async deleteJournalEntry(entryId: string): Promise<void> {
    const userData = await this.getUserData();
    userData.journalEntries = userData.journalEntries.filter(entry => entry.id !== entryId);
    await this.saveUserData(userData);
  }

  async saveWeightRecord(record: WeightRecord): Promise<void> {
    const userData = await this.getUserData();

    // Remove existing record for the same date if it exists
    userData.weightRecords = userData.weightRecords.filter(r => r.date !== record.date);

    // Add the new record
    userData.weightRecords.push(record);

    await this.saveUserData(userData);
  }

  async deleteWeightRecord(id: string): Promise<void> {
    const userData = await this.getUserData();
    userData.weightRecords = userData.weightRecords.filter(record => record.id !== id);
    await this.saveUserData(userData);
  }



  async getStats(): Promise<any> {
    const userData = await this.getUserData();
    return {
      armyCards: userData.armyCards.length,
      tasks: userData.tasks.length,
      journalEntries: userData.journalEntries.length,
      dbSize: new Blob([JSON.stringify(userData)]).size,
    };
  }

  async saveCampInfo(campName: string, flagUrl: string): Promise<void> {
    const userData = await this.getUserData();
    userData.campName = campName;
    userData.flagUrl = flagUrl;
    await this.saveUserData(userData);
  }

  async saveAttributeSchema(schema: AttributeSchema): Promise<void> {
    const userData = await this.getUserData();
    userData.attributeSchema = schema;
    await this.saveUserData(userData);
  }
}

// Electron SQLite implementation
class ElectronDB implements DatabaseAPI {
  async getUserData(): Promise<UserData> {
    try {
      const data = await window.electronAPI.database.getUserData();
      return data;
    } catch (error) {
      throw error;
    }
  }

  async saveArmyCard(card: ArmyCardData): Promise<void> {
    return window.electronAPI.database.saveArmyCard(card);
  }



  async updateUserPoints(points: number): Promise<void> {
    return window.electronAPI.database.updateUserPoints(points);
  }

  async addTotalPoints(pointsToAdd: number): Promise<void> {
    return window.electronAPI.database.addTotalPoints(pointsToAdd);
  }

  async setTotalPoints(totalPoints: number): Promise<void> {
    return window.electronAPI.database.setTotalPoints(totalPoints);
  }

  async updateArmyStylePrompt(prompt: string): Promise<void> {
    return window.electronAPI.database.updateArmyStylePrompt(prompt);
  }

  async deleteArmyCard(cardId: string): Promise<void> {
    return window.electronAPI.database.deleteArmyCard(cardId);
  }

  async saveTask(task: Task): Promise<void> {
    try {
      const result = await window.electronAPI.database.saveTask(task);
      return result;
    } catch (error) {
      throw error;
    }
  }

  async deleteTask(taskId: string): Promise<void> {
    return window.electronAPI.database.deleteTask(taskId);
  }

  async saveJournalEntry(entry: JournalEntry): Promise<void> {
    try {
      const result = await window.electronAPI.database.saveJournalEntry(entry);
      return result;
    } catch (error) {
      throw error;
    }
  }

  async deleteJournalEntry(entryId: string): Promise<void> {
    return window.electronAPI.database.deleteJournalEntry(entryId);
  }

  async saveWeightRecord(record: WeightRecord): Promise<void> {
    return window.electronAPI.database.saveWeightRecord(record);
  }

  async deleteWeightRecord(id: string): Promise<void> {
    return window.electronAPI.database.deleteWeightRecord(id);
  }



  async getStats(): Promise<any> {
    return window.electronAPI.database.getStats();
  }

  async saveCampInfo(campName: string, flagUrl: string): Promise<void> {
    try {
      if (typeof window.electronAPI.database.saveCampInfo !== 'function') {
        throw new Error('saveCampInfo method is not available in electronAPI.database');
      }

      const result = await window.electronAPI.database.saveCampInfo(campName, flagUrl);
      return result;
    } catch (error) {
      throw error;
    }
  }

  async saveAttributeSchema(schema: AttributeSchema): Promise<void> {
    return window.electronAPI.database.saveAttributeSchema(schema);
  }
}

// Create database instance based on environment
const createDatabaseAPI = (): DatabaseAPI => {
  if (isElectron) {
    return new ElectronDB();
  } else {
    return new LocalStorageDB();
  }
};

export function useDatabase() {
  const [userData, setUserData] = useState<UserData>({
    points: 100,
    totalPointsEarned: 0,
    tasks: [],
    journalEntries: [],
    armyCards: [],

    armyStylePrompt: '',
    campName: '',
    flagUrl: '',
  });
  const [isLoaded, setIsLoaded] = useState(false);
  const [dbAPI] = useState(() => createDatabaseAPI());



  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        const data = await dbAPI.getUserData();
        setUserData(data);
      } catch (error) {
        // 如果加载失败，使用默认数据
        setUserData({
          points: 100,
          totalPointsEarned: 0,
          tasks: [],
          journalEntries: [],
          armyCards: [],

          armyStylePrompt: '',
          campName: '',
          flagUrl: '',
        });
      } finally {
        setIsLoaded(true);
      }
    };

    // 添加延迟确保数据库已经初始化
    const timer = setTimeout(loadData, 100);
    return () => clearTimeout(timer);
  }, [dbAPI]);

  // Database operations
  const saveArmyCard = useCallback(async (card: ArmyCardData) => {
    await dbAPI.saveArmyCard(card);
    setUserData(prev => ({
      ...prev,
      armyCards: [card, ...prev.armyCards.filter(c => c.id !== card.id)]
    }));
  }, [dbAPI]);



  const updateUserPoints = useCallback(async (points: number) => {
    await dbAPI.updateUserPoints(points);
    setUserData(prev => ({ ...prev, points }));
  }, [dbAPI]);

  const addTotalPoints = useCallback(async (pointsToAdd: number) => {
    await dbAPI.addTotalPoints(pointsToAdd);
    setUserData(prev => ({
      ...prev,
      totalPointsEarned: (prev.totalPointsEarned || 0) + pointsToAdd
    }));
  }, [dbAPI]);

  const setTotalPoints = useCallback(async (totalPoints: number) => {
    await dbAPI.setTotalPoints(totalPoints);
    setUserData(prev => ({
      ...prev,
      totalPointsEarned: totalPoints
    }));
  }, [dbAPI]);

  const updateArmyStylePrompt = useCallback(async (prompt: string) => {
    await dbAPI.updateArmyStylePrompt(prompt);
    setUserData(prev => ({ ...prev, armyStylePrompt: prompt }));
  }, [dbAPI]);

  const deleteArmyCard = useCallback(async (cardId: string) => {
    await dbAPI.deleteArmyCard(cardId);
    setUserData(prev => ({
      ...prev,
      armyCards: prev.armyCards.filter(card => card.id !== cardId)
    }));
  }, [dbAPI]);

  const saveTask = useCallback(async (task: Task) => {
    await dbAPI.saveTask(task);
    setUserData(prev => ({
      ...prev,
      tasks: [...prev.tasks.filter(t => t.id !== task.id), task]
    }));
  }, [dbAPI]);

  const deleteTask = useCallback(async (taskId: string) => {
    await dbAPI.deleteTask(taskId);
    setUserData(prev => ({
      ...prev,
      tasks: prev.tasks.filter(t => t.id !== taskId)
    }));
  }, [dbAPI]);

  const saveJournalEntry = useCallback(async (entry: JournalEntry) => {
    await dbAPI.saveJournalEntry(entry);
    setUserData(prev => ({
      ...prev,
      journalEntries: [...prev.journalEntries.filter(e => e.id !== entry.id), entry]
    }));
  }, [dbAPI]);

  const deleteJournalEntry = useCallback(async (entryId: string) => {
    await dbAPI.deleteJournalEntry(entryId);
    setUserData(prev => ({
      ...prev,
      journalEntries: prev.journalEntries.filter(e => e.id !== entryId)
    }));
  }, [dbAPI]);

  const saveWeightRecord = useCallback(async (record: WeightRecord) => {
    await dbAPI.saveWeightRecord(record);
    setUserData(prev => ({
      ...prev,
      weightRecords: [...prev.weightRecords.filter(r => r.id !== record.id), record]
    }));
  }, [dbAPI]);

  const deleteWeightRecord = useCallback(async (id: string) => {
    await dbAPI.deleteWeightRecord(id);
    setUserData(prev => ({
      ...prev,
      weightRecords: prev.weightRecords.filter(r => r.id !== id)
    }));
  }, [dbAPI]);



  const getStats = useCallback(async () => {
    return await dbAPI.getStats();
  }, [dbAPI]);

  const saveCampInfo = useCallback(async (campName: string, flagUrl: string) => {
    await dbAPI.saveCampInfo(campName, flagUrl);
    setUserData(prev => ({
      ...prev,
      campName,
      flagUrl
    }));
  }, [dbAPI]);

  const saveAttributeSchema = useCallback(async (schema: AttributeSchema) => {
    await dbAPI.saveAttributeSchema(schema);
    setUserData(prev => ({
      ...prev,
      attributeSchema: schema
    }));
  }, [dbAPI]);



  return {
    userData,
    isLoaded,
    saveArmyCard,

    updateUserPoints,
    addTotalPoints,
    setTotalPoints,
    updateArmyStylePrompt,
    deleteArmyCard,

    saveTask,
    deleteTask,
    saveJournalEntry,
    deleteJournalEntry,
    saveWeightRecord,
    deleteWeightRecord,
    getStats,
    saveCampInfo,
    saveAttributeSchema,
    isUsingDatabase: isElectron,
  };
}
