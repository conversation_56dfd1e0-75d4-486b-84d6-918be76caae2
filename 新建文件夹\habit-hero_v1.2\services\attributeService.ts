import { ArmyCardData, CardAttributes, AttributeSchema } from '../types';
import { generateContent } from './geminiService';

/**
 * 鲁棒的JSON解析函数
 */
const parseAIResponse = (response: string, context: string = ''): any => {
  console.log(`${context} - AI响应:`, response);

  try {
    // 尝试直接解析
    const result = JSON.parse(response);
    console.log(`${context} - 直接解析成功:`, result);
    return result;
  } catch (parseError) {
    console.log(`${context} - 直接解析失败，尝试提取JSON...`);

    // 尝试提取JSON代码块
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      try {
        const result = JSON.parse(jsonMatch[1]);
        console.log(`${context} - 从代码块提取JSON成功:`, result);
        return result;
      } catch (blockError) {
        console.error(`${context} - 代码块JSON解析失败:`, blockError);
      }
    }

    // 尝试提取大括号内容
    const braceMatch = response.match(/\{[\s\S]*\}/);
    if (braceMatch) {
      try {
        const result = JSON.parse(braceMatch[0]);
        console.log(`${context} - 从大括号提取JSON成功:`, result);
        return result;
      } catch (braceError) {
        console.error(`${context} - 大括号JSON解析失败:`, braceError);
      }
    }

    console.error(`${context} - 未找到有效的JSON内容`);
    throw new Error(`无法解析AI返回的JSON格式: ${parseError.message}`);
  }
};

/**
 * 为新创建的角色自动分配属性
 */
export const generateAttributesForNewCard = async (
  newCard: ArmyCardData,
  existingCards: ArmyCardData[],
  attributeSchema?: AttributeSchema
): Promise<CardAttributes> => {
  // 如果没有属性模式，返回默认属性
  if (!attributeSchema) {
    return generateDefaultAttributes(newCard);
  }

  // 找到同职业同阵营的参考角色
  const referenceCards = existingCards.filter(card => 
    card.characterClass === newCard.characterClass &&
    card.stylePrompt === newCard.stylePrompt &&
    card.attributes // 只考虑已有属性的角色
  );

  // 如果没有参考角色，使用同职业的角色
  const fallbackCards = referenceCards.length === 0 
    ? existingCards.filter(card => 
        card.characterClass === newCard.characterClass && 
        card.attributes
      )
    : referenceCards;

  if (fallbackCards.length === 0) {
    // 如果完全没有参考，返回基于稀有度的默认属性
    return generateDefaultAttributes(newCard);
  }

  try {
    // 获取适用的属性字段
    const applicableFields = [
      ...attributeSchema.baseAttributes,
      ...attributeSchema.specialAttributes.filter(field => 
        field.applicableClasses.length === 0 || 
        field.applicableClasses.includes(newCard.characterClass)
      )
    ];

    // 准备参考角色信息
    const referenceInfo = fallbackCards.slice(0, 5).map(card => ({
      name: card.name,
      rarity: card.rarity.level,
      description: card.description.substring(0, 100),
      skills: card.skills.map(s => s.name).join('、'),
      attributes: card.attributes
    }));

    const prompt = `
你是游戏数值策划师。为新角色"${newCard.name}"分配属性值。只返回JSON，不要解释。

角色信息：
- 名称：${newCard.name}
- 职业：${newCard.characterClass}
- 稀有度：${newCard.rarity.level}
- 描述：${newCard.description}
- 技能：${newCard.skills.map(s => s.name).join('、')}

参考角色（同职业）：
${referenceInfo.slice(0, 3).map(ref => `
${ref.name}(${ref.rarity}): 攻击${ref.attributes?.attack} 防御${ref.attributes?.defense} 战力${ref.attributes?.totalPower}
`).join('\n')}

分配规则：
- 传说(8-10)、史诗(7-9)、稀有(6-8)、不常见(5-7)、普通(4-6)
- 根据描述和技能匹配属性
- 综合战力 = (攻击+防御+生命+速度+智力+领导)*100 + 特殊属性总和*10

只返回JSON：
{
  "attack": 数值,
  "defense": 数值,
  "health": 数值,
  "speed": 数值,
  "intelligence": 数值,
  "leadership": 数值,
  "specialAttributes": {},
  "totalPower": 数值
}`;

    const response = await generateContent(prompt);
    const attributesData = parseAIResponse(response, `新角色属性生成 - ${newCard.name}`);

    return {
      attack: attributesData.attack,
      defense: attributesData.defense,
      health: attributesData.health,
      speed: attributesData.speed,
      intelligence: attributesData.intelligence,
      leadership: attributesData.leadership,
      specialAttributes: attributesData.specialAttributes || {},
      totalPower: attributesData.totalPower,
      generatedBy: 'ai',
      balanceVersion: new Date().toISOString().split('T')[0],
      schemaVersion: attributeSchema.version,
      generatedAt: new Date().toISOString()
    };

  } catch (error) {
    console.error('Failed to generate attributes for new card:', error);
    return generateDefaultAttributes(newCard);
  }
};

/**
 * 生成基于稀有度的默认属性
 */
const generateDefaultAttributes = (card: ArmyCardData): CardAttributes => {
  // 基于稀有度的属性范围
  const rarityRanges: Record<string, { min: number, max: number }> = {
    '传说': { min: 8, max: 10 },
    '史诗': { min: 7, max: 9 },
    '稀有': { min: 6, max: 8 },
    '不常见': { min: 5, max: 7 },
    '普通': { min: 4, max: 6 }
  };

  const range = rarityRanges[card.rarity.level] || { min: 4, max: 6 };
  
  const randomInRange = () => 
    Math.floor(Math.random() * (range.max - range.min + 1)) + range.min;

  const baseAttributes = {
    attack: randomInRange(),
    defense: randomInRange(),
    health: randomInRange(),
    speed: randomInRange(),
    intelligence: randomInRange(),
    leadership: randomInRange()
  };

  // 计算简单的综合战力
  const totalPower = Object.values(baseAttributes).reduce((sum, val) => sum + val, 0) * 100;

  return {
    ...baseAttributes,
    specialAttributes: {},
    totalPower,
    generatedBy: 'ai',
    balanceVersion: new Date().toISOString().split('T')[0],
    generatedAt: new Date().toISOString()
  };
};

/**
 * 检查是否需要为新职业创建新的特殊属性
 */
export const checkForNewAttributes = async (
  newCard: ArmyCardData,
  currentSchema: AttributeSchema,
  existingCards: ArmyCardData[]
): Promise<{ needsNewAttributes: boolean; suggestedAttributes?: string[] }> => {
  // 检查是否是全新的职业
  const existingClasses = new Set(existingCards.map(card => card.characterClass));
  const isNewClass = !existingClasses.has(newCard.characterClass);

  if (!isNewClass) {
    return { needsNewAttributes: false };
  }

  try {
    const prompt = `
分析新职业是否需要创建新的特殊属性：

新职业：${newCard.characterClass}
代表角色：${newCard.name}
技能：${newCard.skills.map(s => s.name).join('、')}
描述：${newCard.description}

现有特殊属性：
${currentSchema.specialAttributes.map(attr => 
  `- ${attr.name}: ${attr.description} (适用职业: ${attr.applicableClasses.join('、')})`
).join('\n')}

现有职业：${Array.from(existingClasses).join('、')}

请分析：
1. 这个新职业是否有独特的能力特征？
2. 现有特殊属性是否足够描述这个职业？
3. 如果需要新属性，建议创建哪些？

请以JSON格式返回：
{
  "needsNewAttributes": true/false,
  "suggestedAttributes": ["属性名1", "属性名2"],
  "reasoning": "分析原因"
}`;

    const response = await generateContent(prompt);
    const result = parseAIResponse(response, `新属性检查 - ${newCard.characterClass}`);
    
    return {
      needsNewAttributes: result.needsNewAttributes,
      suggestedAttributes: result.suggestedAttributes
    };

  } catch (error) {
    console.error('Failed to check for new attributes:', error);
    return { needsNewAttributes: false };
  }
};

/**
 * 为现有角色批量生成缺失的属性
 */
export const generateMissingAttributes = async (
  cardsWithoutAttributes: ArmyCardData[],
  cardsWithAttributes: ArmyCardData[],
  attributeSchema: AttributeSchema,
  batchSize: number = 10
): Promise<Record<string, CardAttributes>> => {
  const results: Record<string, CardAttributes> = {};
  
  // 分批处理
  for (let i = 0; i < cardsWithoutAttributes.length; i += batchSize) {
    const batch = cardsWithoutAttributes.slice(i, i + batchSize);
    
    for (const card of batch) {
      try {
        const attributes = await generateAttributesForNewCard(
          card,
          cardsWithAttributes,
          attributeSchema
        );
        results[card.id] = attributes;
      } catch (error) {
        console.error(`Failed to generate attributes for card ${card.name}:`, error);
        // 使用默认属性作为后备
        results[card.id] = generateDefaultAttributes(card);
      }
    }
    
    // 添加延迟避免API限制
    if (i + batchSize < cardsWithoutAttributes.length) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }
  
  return results;
};
