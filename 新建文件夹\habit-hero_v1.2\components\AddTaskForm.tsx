
import React, { useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Task, TaskType } from '../types';
import { DEFAULT_TASK_POINTS, DAYS_OF_WEEK_OPTIONS, TASK_CATEGORIES_SUGGESTIONS, EMOJI_SUGGESTIONS } from '../constants';
import { PlusIcon } from './icons';
import { getFormattedDate } from '../utils/dateUtils';


interface AddTaskFormProps {
  onAddTask: (task: Task) => void;
  onClose: () => void; // To close the modal
}

const AddTaskForm: React.FC<AddTaskFormProps> = ({ onAddTask, onClose }) => {
  const [text, setText] = useState('');
  const [type, setType] = useState<TaskType>(TaskType.DAILY);
  const [dueDate, setDueDate] = useState<string>(getFormattedDate(new Date()));
  const [daysOfWeek, setDaysOfWeek] = useState<number[]>([]);
  const [timesPerWeek, setTimesPerWeek] = useState<number>(1);
  const [points, setPoints] = useState<number>(DEFAULT_TASK_POINTS);
  const [icon, setIcon] = useState<string>('');
  const [category, setCategory] = useState<string>('');
  const [isHighlighted, setIsHighlighted] = useState<boolean>(false);


  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!text.trim()) return;

    const newTask: Task = {
      id: uuidv4(),
      title: text.trim(), // Changed from text to title
      type,
      points: points > 0 ? points : DEFAULT_TASK_POINTS, // Ensure points are positive
      createdAt: new Date().toISOString(),
      completedDates: [],
      emoji: icon || undefined, // Changed from icon to emoji
      category: category.trim() || undefined,
      isHighlighted, // 添加高亮标记
    };

    if (type === TaskType.ONCE) {
      newTask.dueDate = new Date(dueDate).toISOString();
      newTask.isCompleted = false;
    } else if (type === TaskType.WEEKLY) {
      newTask.daysOfWeek = daysOfWeek.sort((a,b) => a-b);
      newTask.timesPerWeek = Math.max(1, timesPerWeek);
      if(daysOfWeek.length === 0) { // Default to all days if none selected for weekly
        newTask.daysOfWeek = DAYS_OF_WEEK_OPTIONS.map(d => d.value);
      }
    }
    
    onAddTask(newTask);
    // Reset form or close modal - onClose will handle this
    onClose(); 
  };

  const handleDayOfWeekChange = (dayValue: number) => {
    setDaysOfWeek(prev => 
      prev.includes(dayValue) ? prev.filter(d => d !== dayValue) : [...prev, dayValue]
    );
  };

  return (
    // Removed outer form container class, modal will provide it.
    <form onSubmit={handleSubmit} className="space-y-5">
      {/* Title is now part of Modal component */}
      <div className="group">
        <label htmlFor="taskText" className="block text-sm font-semibold text-slate-300 mb-2 group-focus-within:text-sky-400 transition-colors duration-300">
          任务描述 <span className="text-red-400">*</span>
        </label>
        <div className="relative">
          <input
            id="taskText"
            type="text"
            value={text}
            onChange={(e) => setText(e.target.value)}
            placeholder="例如：晨跑30分钟"
            className="w-full p-4 bg-gradient-to-r from-slate-700 to-slate-600 border-2 border-slate-600 rounded-xl focus:ring-4 focus:ring-sky-500/20 focus:border-sky-500 outline-none transition-all duration-300 text-slate-100 placeholder-slate-400 hover:border-slate-500"
            required
          />
          <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-sky-500/0 to-purple-500/0 group-focus-within:from-sky-500/10 group-focus-within:to-purple-500/10 transition-all duration-300 pointer-events-none" />
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="group">
          <label htmlFor="taskIcon" className="block text-sm font-semibold text-slate-300 mb-2 group-focus-within:text-yellow-400 transition-colors duration-300">
            图标 (Emoji)
          </label>
          <div className="relative">
            <input
              id="taskIcon"
              type="text"
              value={icon}
              onChange={(e) => setIcon(e.target.value)}
              placeholder={`例如: ${EMOJI_SUGGESTIONS[Math.floor(Math.random() * EMOJI_SUGGESTIONS.length)]}`}
              className="w-full p-4 bg-gradient-to-r from-slate-700 to-slate-600 border-2 border-slate-600 rounded-xl focus:ring-4 focus:ring-yellow-500/20 focus:border-yellow-500 outline-none transition-all duration-300 text-slate-100 placeholder-slate-400 hover:border-slate-500 text-center text-xl"
              maxLength={2}
            />
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-yellow-500/0 to-orange-500/0 group-focus-within:from-yellow-500/10 group-focus-within:to-orange-500/10 transition-all duration-300 pointer-events-none" />
          </div>
        </div>
        <div className="group">
          <label htmlFor="taskCategory" className="block text-sm font-semibold text-slate-300 mb-2 group-focus-within:text-purple-400 transition-colors duration-300">
            分类
          </label>
          <div className="relative">
            <input
              id="taskCategory"
              type="text"
              value={category}
              onChange={(e) => setCategory(e.target.value)}
              placeholder={`例如: ${TASK_CATEGORIES_SUGGESTIONS[Math.floor(Math.random() * TASK_CATEGORIES_SUGGESTIONS.length)]}`}
              className="w-full p-4 bg-gradient-to-r from-slate-700 to-slate-600 border-2 border-slate-600 rounded-xl focus:ring-4 focus:ring-purple-500/20 focus:border-purple-500 outline-none transition-all duration-300 text-slate-100 placeholder-slate-400 hover:border-slate-500"
              list="category-suggestions"
            />
            <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-purple-500/0 to-pink-500/0 group-focus-within:from-purple-500/10 group-focus-within:to-pink-500/10 transition-all duration-300 pointer-events-none" />
            <datalist id="category-suggestions">
              {TASK_CATEGORIES_SUGGESTIONS.map(cat => <option key={cat} value={cat} />)}
            </datalist>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
            <label htmlFor="taskType" className="block text-sm font-medium text-slate-300 mb-1">任务类型</label>
            <select
            id="taskType"
            value={type}
            onChange={(e) => setType(e.target.value as TaskType)}
            className="w-full p-3 bg-slate-700 border border-slate-600 rounded-md focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100"
            >
            <option value={TaskType.DAILY}>每日任务</option>
            <option value={TaskType.WEEKLY}>每周任务</option>
            <option value={TaskType.ONCE}>一次性任务</option>
            </select>
        </div>
        <div>
            <label htmlFor="taskPoints" className="block text-sm font-medium text-slate-300 mb-1">获得积分</label>
            <input
            id="taskPoints"
            type="number"
            value={points}
            onChange={(e) => setPoints(parseInt(e.target.value, 10))}
            min="1"
            className="w-full p-3 bg-slate-700 border border-slate-600 rounded-md focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100"
            />
        </div>
      </div>

      {/* 重要任务标记 */}
      <div>
        <label className="flex items-center space-x-3 cursor-pointer group">
          <div className="relative">
            <input
              type="checkbox"
              checked={isHighlighted}
              onChange={(e) => setIsHighlighted(e.target.checked)}
              className="sr-only"
            />
            <div className={`w-6 h-6 rounded-md border-2 transition-all duration-200 flex items-center justify-center ${
              isHighlighted
                ? 'bg-red-500 border-red-500 shadow-lg shadow-red-500/30'
                : 'border-slate-500 group-hover:border-red-400'
            }`}>
              {isHighlighted && (
                <svg className="w-4 h-4 text-white" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                </svg>
              )}
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm font-medium text-slate-300 group-hover:text-white transition-colors">
              ⭐ 标记为重要任务
            </span>
          </div>
        </label>
        <p className="text-xs text-slate-400 mt-1 ml-9">
          重要任务会在任务列表中优先显示，并带有特殊标识
        </p>
      </div>

      {type === TaskType.ONCE && (
        <div>
          <label htmlFor="dueDate" className="block text-sm font-medium text-slate-300 mb-1">截止日期</label>
          <input
            id="dueDate"
            type="date"
            value={dueDate}
            onChange={(e) => setDueDate(e.target.value)}
            className="w-full p-3 bg-slate-700 border border-slate-600 rounded-md focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100"
            min={getFormattedDate(new Date())}
          />
        </div>
      )}

      {type === TaskType.WEEKLY && (
        <>
          <div>
            <label className="block text-sm font-medium text-slate-300 mb-2">选择周几 (默认为每天)</label>
            <div className="grid grid-cols-3 sm:grid-cols-4 gap-2">
              {DAYS_OF_WEEK_OPTIONS.map(day => (
                <button
                  type="button"
                  key={day.value}
                  onClick={() => handleDayOfWeekChange(day.value)}
                  className={`p-2 rounded-md text-sm transition-colors ${
                    daysOfWeek.includes(day.value) 
                      ? 'bg-sky-500 text-white font-semibold' 
                      : 'bg-slate-700 hover:bg-slate-600 text-slate-300'
                  }`}
                >
                  {day.label}
                </button>
              ))}
            </div>
          </div>
          <div>
            <label htmlFor="timesPerWeek" className="block text-sm font-medium text-slate-300 mb-1">每周次数</label>
            <input
              id="timesPerWeek"
              type="number"
              value={timesPerWeek}
              onChange={(e) => setTimesPerWeek(Math.max(1, parseInt(e.target.value)))}
              min="1"
              className="w-full p-3 bg-slate-700 border border-slate-600 rounded-md focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100"
            />
          </div>
        </>
      )}

      <div className="pt-4">
        <button
          type="submit"
          className="group relative w-full flex items-center justify-center p-4 bg-gradient-to-r from-sky-500 via-blue-500 to-purple-500 text-white font-bold rounded-xl hover:from-sky-600 hover:via-blue-600 hover:to-purple-600 focus:outline-none focus:ring-4 focus:ring-sky-500/50 transition-all duration-300 transform hover:scale-105 active:scale-95 shadow-lg hover:shadow-xl hover:shadow-sky-500/25 overflow-hidden"
        >
          {/* 按钮背景动画 */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/10 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />

          {/* 按钮内容 */}
          <div className="relative flex items-center space-x-2">
            <PlusIcon className="w-5 h-5 transition-transform duration-300 group-hover:rotate-90" />
            <span className="text-lg">添加任务</span>
          </div>

          {/* 成功提示动画 */}
          <div className="absolute inset-0 bg-green-500 opacity-0 group-active:opacity-20 transition-opacity duration-150 rounded-xl" />
        </button>
      </div>
    </form>
  );
};

export default AddTaskForm;
