import React, { useState } from 'react';
import { ArmyCardData } from '../types';

interface FormationPosition {
  units: (number | string)[];
  position: [number, number];
  role: string;
}

interface Formation {
  name: string;
  description: string;
  positions: FormationPosition[];
}

interface BattlefieldGridProps {
  formation: Formation;
  armyCards: ArmyCardData[];
}

// 解析单位索引（支持 "9-12" 这样的范围）
const parseUnitIndices = (units: (number | string)[]): number[] => {
  const result: number[] = [];
  
  units.forEach(unit => {
    if (typeof unit === 'number') {
      result.push(unit);
    } else if (typeof unit === 'string' && unit.includes('-')) {
      const [start, end] = unit.split('-').map(Number);
      for (let i = start; i <= end; i++) {
        result.push(i);
      }
    } else if (typeof unit === 'string') {
      const num = parseInt(unit);
      if (!isNaN(num)) {
        result.push(num);
      }
    }
  });
  
  return result;
};

export const BattlefieldGrid: React.FC<BattlefieldGridProps> = ({
  formation,
  armyCards
}) => {
  const [hoveredCell, setHoveredCell] = useState<[number, number] | null>(null);

  // 按职业排序，与阵型生成时保持一致
  const sortedCards = [...armyCards].sort((a, b) => {
    // 首先按职业排序
    if (a.characterClass !== b.characterClass) {
      return a.characterClass.localeCompare(b.characterClass);
    }
    // 相同职业内按稀有度排序（传说 > 史诗 > 稀有 > 普通）
    const rarityOrder = { '传说': 4, '史诗': 3, '稀有': 2, '普通': 1 };
    const aOrder = rarityOrder[a.rarity.level as keyof typeof rarityOrder] || 0;
    const bOrder = rarityOrder[b.rarity.level as keyof typeof rarityOrder] || 0;
    return bOrder - aOrder;
  });

  // 创建20x20网格
  const gridSize = 20;
  
  // 创建位置映射
  const positionMap = new Map<string, FormationPosition>();
  formation.positions.forEach(pos => {
    const key = `${pos.position[0]},${pos.position[1]}`;
    positionMap.set(key, pos);
  });
  
  // 获取指定位置的角色
  const getUnitsAtPosition = (x: number, y: number): ArmyCardData[] => {
    const key = `${x},${y}`;
    const position = positionMap.get(key);
    if (!position) return [];

    const unitIndices = parseUnitIndices(position.units);
    return unitIndices
      .map(index => sortedCards[index - 1]) // 转换为0基索引，使用排序后的卡牌
      .filter(card => card); // 过滤掉undefined
  };
  
  const renderCell = (x: number, y: number) => {
    const units = getUnitsAtPosition(x, y);
    const hasUnits = units.length > 0;
    const key = `${x},${y}`;
    const position = positionMap.get(key);
    
    return (
      <div
        key={key}
        className={`
          w-6 h-6 border border-gray-600 relative cursor-pointer transition-all duration-200
          ${hasUnits 
            ? 'bg-blue-500 hover:bg-blue-400 border-blue-300' 
            : 'bg-gray-800 hover:bg-gray-700'
          }
          ${hoveredCell && hoveredCell[0] === x && hoveredCell[1] === y 
            ? 'ring-2 ring-yellow-400' 
            : ''
          }
        `}
        onMouseEnter={() => setHoveredCell([x, y])}
        onMouseLeave={() => setHoveredCell(null)}
        title={hasUnits ? `${units.length}个单位` : ''}
      >
        {hasUnits && (
          <div className="absolute inset-0 flex items-center justify-center">
            <span className="text-white text-xs font-bold">
              {units.length}
            </span>
          </div>
        )}
        
        {/* 悬浮显示角色信息 */}
        {hoveredCell && hoveredCell[0] === x && hoveredCell[1] === y && hasUnits && (
          <div className="absolute z-50 bottom-full left-1/2 transform -translate-x-1/2 mb-2 p-3 bg-gray-900 border border-gray-600 rounded-lg shadow-xl min-w-64 max-w-80">
            <div className="text-yellow-400 font-semibold mb-2">
              位置 ({x}, {y}) - {position?.role}
            </div>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {units.map((unit, index) => (
                <div key={index} className="flex items-center space-x-2 p-2 bg-gray-800 rounded">
                  <img
                    src={unit.imageUrl}
                    alt={unit.name}
                    className="w-8 h-8 rounded border"
                    style={{ borderColor: unit.rarity.glowColor }}
                  />
                  <div className="flex-1 min-w-0">
                    <div className="text-white text-sm font-medium truncate">
                      {unit.name}
                    </div>
                    <div className="text-gray-400 text-xs">
                      {unit.characterClass} - {unit.rarity.level}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className="bg-slate-800 rounded-lg p-4 border border-slate-600">
      <div className="mb-4">
        <h3 className="text-lg font-bold text-white mb-2">
          🏛️ 推荐阵型：{formation.name}
        </h3>
        <p className="text-gray-300 text-sm mb-3">
          {formation.description}
        </p>
        <div className="text-xs text-gray-400 mb-3">
          💡 战场前方在右边，后方在左边。鼠标悬浮查看位置详情
        </div>
      </div>
      
      {/* 坐标轴标签 */}
      <div className="flex items-start space-x-2">
        {/* Y轴标签 */}
        <div className="flex flex-col-reverse items-center space-y-reverse space-y-1 pt-4">
          {Array.from({ length: gridSize }, (_, i) => (
            <div key={i} className="text-xs text-gray-400 w-4 text-center">
              {i + 1}
            </div>
          ))}
        </div>
        
        {/* 主网格 */}
        <div className="flex flex-col">
          {/* X轴标签 */}
          <div className="flex space-x-0 mb-1">
            {Array.from({ length: gridSize }, (_, i) => (
              <div key={i} className="text-xs text-gray-400 w-6 text-center">
                {i + 1}
              </div>
            ))}
          </div>
          
          {/* 网格 */}
          <div
            className="grid gap-0 border border-gray-500"
            style={{ gridTemplateColumns: 'repeat(20, 1fr)' }}
          >
            {Array.from({ length: gridSize }, (_, y) =>
              Array.from({ length: gridSize }, (_, x) =>
                renderCell(x + 1, gridSize - y) // Y轴翻转，使(1,1)在左下角
              )
            )}
          </div>
        </div>
      </div>
      
      {/* 图例 */}
      <div className="mt-4 flex items-center space-x-4 text-sm">
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-blue-500 border border-blue-300 rounded"></div>
          <span className="text-gray-300">有单位</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-4 h-4 bg-gray-800 border border-gray-600 rounded"></div>
          <span className="text-gray-300">空位置</span>
        </div>
      </div>
      
      {/* 位置详情列表 */}
      <div className="mt-4 space-y-2">
        <h4 className="text-md font-semibold text-white">阵型详情：</h4>
        {formation.positions.map((pos, index) => {
          const units = parseUnitIndices(pos.units);
          const unitNames = units
            .map(i => sortedCards[i - 1]?.name)
            .filter(name => name)
            .join('、');
          
          return (
            <div key={index} className="bg-gray-700 p-3 rounded">
              <div className="text-yellow-400 font-medium">
                位置 ({pos.position[0]}, {pos.position[1]}) - {pos.role}
              </div>
              <div className="text-gray-300 text-sm mt-1">
                单位索引: {pos.units.join(', ')}
              </div>
              {unitNames && (
                <div className="text-gray-300 text-sm mt-1">
                  单位名称: {unitNames}
                </div>
              )}
            </div>
          );
        })}
      </div>
    </div>
  );
};
