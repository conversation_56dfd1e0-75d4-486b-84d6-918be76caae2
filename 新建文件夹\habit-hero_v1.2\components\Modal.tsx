
import React, { useEffect, useState } from 'react';
import { XMarkIcon } from './icons';

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title?: string;
  children: React.ReactNode;
  size?: 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

const Modal: React.FC<ModalProps> = ({ isOpen, onClose, title, children, size = 'md' }) => {
  const [isVisible, setIsVisible] = useState(false);
  const [isAnimating, setIsAnimating] = useState(false);

  useEffect(() => {
    if (isOpen) {
      setIsVisible(true);
      setTimeout(() => setIsAnimating(true), 10);
    } else {
      setIsAnimating(false);
      setTimeout(() => setIsVisible(false), 300);
    }
  }, [isOpen]);

  const handleClose = () => {
    setIsAnimating(false);
    setTimeout(() => {
      onClose();
    }, 300);
  };

  const handleBackdropClick = (e: React.MouseEvent) => {
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isVisible) return null;

  const sizeClasses: Record<string, string> = {
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-4xl',
    'full': 'max-w-[95vw] max-h-[95vh]',
  };

  return (
    <div
      className={`fixed inset-0 z-50 flex items-center justify-center p-4 transition-all duration-300 ${
        isAnimating
          ? 'bg-black/80 backdrop-blur-sm'
          : 'bg-black/0'
      }`}
      onClick={handleBackdropClick}
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-slate-900/50 via-slate-800/30 to-slate-900/50" />

      {/* 模态框内容 */}
      <div
        className={`relative w-full ${sizeClasses[size]} ${size === 'full' ? 'h-full' : ''} transform transition-all duration-300 ${
          isAnimating
            ? 'scale-100 opacity-100 translate-y-0'
            : 'scale-95 opacity-0 translate-y-4'
        }`}
        onClick={(e) => e.stopPropagation()}
      >
        {/* 模态框主体 */}
        <div className={`relative bg-gradient-to-br from-slate-800 to-slate-900 rounded-2xl shadow-2xl border border-slate-700/50 overflow-hidden ${size === 'full' ? 'h-full flex flex-col' : ''}`}>
          {/* 顶部装饰条 */}
          <div className="absolute top-0 left-0 right-0 h-1 bg-gradient-to-r from-sky-500 via-purple-500 to-pink-500" />

          {/* 关闭按钮 */}
          <button
            onClick={handleClose}
            className="absolute top-4 right-4 z-10 group p-2 rounded-xl bg-slate-700/50 hover:bg-slate-600/70 text-slate-400 hover:text-white transition-all duration-300 hover:scale-110 active:scale-95"
            aria-label="关闭弹窗"
          >
            <XMarkIcon className="w-5 h-5 transition-transform duration-300 group-hover:rotate-90" />
          </button>

          {/* 标题区域 */}
          {title && (
            <div className="relative pt-8 pb-6 px-8">
              <div className="text-center">
                <h2 className="text-2xl font-bold bg-gradient-to-r from-sky-400 via-purple-400 to-pink-400 bg-clip-text text-transparent">
                  {title}
                </h2>
                <div className="mt-2 w-16 h-0.5 bg-gradient-to-r from-sky-500 to-purple-500 mx-auto rounded-full" />
              </div>
            </div>
          )}

          {/* 内容区域 */}
          <div className={`px-8 ${title ? 'pb-8' : 'py-8'} ${size === 'full' ? 'flex-1 overflow-auto' : ''}`}>
            <div className="relative">
              {children}
            </div>
          </div>

          {/* 底部装饰 */}
          <div className="absolute bottom-0 left-0 right-0 h-px bg-gradient-to-r from-transparent via-slate-600 to-transparent" />
        </div>

        {/* 外发光效果 */}
        <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-sky-500/10 via-purple-500/10 to-pink-500/10 blur-xl -z-10" />
      </div>
    </div>
  );
};

export default Modal;
