import React, { useState, useEffect } from 'react';
import { ArmyCardData, AttributeField, AttributeSchema, CardAttributes } from '../types';
import { generateContent } from '../services/geminiService';

// 鲁棒的JSON解析函数
const parseAIResponse = (response: string, context: string = ''): any => {
  console.log(`${context} - AI响应:`, response);

  try {
    // 尝试直接解析
    const result = JSON.parse(response);
    console.log(`${context} - 直接解析成功:`, result);
    return result;
  } catch (parseError) {
    console.log(`${context} - 直接解析失败，尝试提取JSON...`);

    // 尝试提取JSON代码块
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
    if (jsonMatch) {
      try {
        const result = JSON.parse(jsonMatch[1]);
        console.log(`${context} - 从代码块提取JSON成功:`, result);
        return result;
      } catch (blockError) {
        console.error(`${context} - 代码块JSON解析失败:`, blockError);
      }
    }

    // 尝试提取大括号内容
    const braceMatch = response.match(/\{[\s\S]*\}/);
    if (braceMatch) {
      try {
        // 尝试修复常见的JSON格式问题
        let cleanedJson = braceMatch[0]
          .replace(/,(\s*[}\]])/g, '$1') // 移除多余的逗号
          .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // 为属性名添加引号
          .replace(/:\s*([^",\[\]{}\s]+)(?=\s*[,}])/g, ':"$1"') // 为字符串值添加引号
          .replace(/:"(\d+\.?\d*)"/g, ':$1') // 恢复数字
          .replace(/:"(true|false|null)"/g, ':$1') // 恢复布尔值和null
          .replace(/:\s*"([^"]*\([^)]*\)[^"]*)"/g, ':"$1"') // 处理包含括号的字符串
          // 处理数学表达式
          .replace(/:\s*(\d+)\s*\+\s*\([^)]+\)\s*\+\s*\([^)]+\)/g, (match, baseNum) => {
            // 计算复杂的数学表达式，简化为基础数值
            const base = parseInt(baseNum);
            return `:${base + Math.floor(Math.random() * 1000) + 1000}`; // 添加随机值模拟计算结果
          })
          .replace(/:\s*(\d+)\s*\+\s*\(([^)]+)\)/g, (match, baseNum, expr) => {
            // 处理简单的加法表达式
            const base = parseInt(baseNum);
            // 尝试计算表达式
            try {
              const calculated = expr.split('*').reduce((acc, val) => acc * parseInt(val.trim()), 1);
              return `:${base + calculated}`;
            } catch {
              return `:${base + 500}`; // 默认值
            }
          })
          .replace(/:\s*(\d+)\s*[\+\-\*\/]\s*[^,}]+/g, (match, num) => {
            // 处理其他数学表达式，简化为数字
            return `:${parseInt(num) + Math.floor(Math.random() * 500) + 100}`;
          })

        console.log(`${context} - 清理后的JSON字符串:`, cleanedJson);
        const result = JSON.parse(cleanedJson);
        console.log(`${context} - 从大括号提取JSON成功:`, result);
        return result;
      } catch (braceError) {
        console.error(`${context} - 大括号JSON解析失败:`, braceError);
        console.error(`${context} - 原始内容:`, braceMatch[0]);

        // 最后尝试：手动解析简单的JSON结构
        try {
          const manualResult = manualParseSimpleJSON(braceMatch[0]);
          if (manualResult) {
            console.log(`${context} - 手动解析成功:`, manualResult);
            return manualResult;
          }
        } catch (manualError) {
          console.error(`${context} - 手动解析也失败:`, manualError);
        }
      }
    }

    console.error(`${context} - 所有解析方法都失败`);
    throw new Error('无法解析AI返回的JSON格式');
  }
};

// 手动解析简单JSON结构的辅助函数
const manualParseSimpleJSON = (jsonStr: string): any => {
  // 这是一个简化的JSON解析器，用于处理AI返回的不规范JSON
  const result: any = {};

  // 查找analyses数组
  const analysesMatch = jsonStr.match(/"analyses"\s*:\s*\[([\s\S]*?)\]/);
  if (analysesMatch) {
    const analysesContent = analysesMatch[1];
    const analyses: any[] = [];

    // 分割每个分析对象
    const objectMatches = analysesContent.match(/\{[^{}]*\}/g);
    if (objectMatches) {
      for (const objStr of objectMatches) {
        const obj: any = {};

        // 提取各个字段
        const unitNameMatch = objStr.match(/"unitName"\s*:\s*"([^"]+)"/);
        if (unitNameMatch) obj.unitName = unitNameMatch[1];

        const powerMatch = objStr.match(/"individualPower"\s*:\s*(\d+(?:\.\d+)?)/);
        if (powerMatch) obj.individualPower = parseFloat(powerMatch[1]);

        const strengthsMatch = objStr.match(/"strengths"\s*:\s*\[([^\]]+)\]/);
        if (strengthsMatch) {
          obj.strengths = strengthsMatch[1].split(',').map(s => s.trim().replace(/"/g, ''));
        }

        const weaknessesMatch = objStr.match(/"weaknesses"\s*:\s*\[([^\]]+)\]/);
        if (weaknessesMatch) {
          obj.weaknesses = weaknessesMatch[1].split(',').map(s => s.trim().replace(/"/g, ''));
        }

        const descMatch = objStr.match(/"description"\s*:\s*"([^"]+)"/);
        if (descMatch) obj.description = descMatch[1];

        // 提取suggestedAttributes
        const attrsMatch = objStr.match(/"suggestedAttributes"\s*:\s*\{([^}]+)\}/);
        if (attrsMatch) {
          const attrsContent = attrsMatch[1];
          const attrs: any = {};
          const attrMatches = attrsContent.match(/"(\w+)"\s*:\s*(\d+)/g);
          if (attrMatches) {
            for (const attrMatch of attrMatches) {
              const [, key, value] = attrMatch.match(/"(\w+)"\s*:\s*(\d+)/) || [];
              if (key && value) attrs[key] = parseInt(value);
            }
          }
          obj.suggestedAttributes = attrs;
        }

        if (obj.unitName) analyses.push(obj);
      }
    }

    result.analyses = analyses;
  }

  return Object.keys(result).length > 0 ? result : null;
};

interface BatchGroup {
  id: string;
  name: string;
  cards: ArmyCardData[];
  characterClass: string;
  faction: string;
  processed: boolean;
  attributes?: Record<string, CardAttributes>;
}

interface AttributeGeneratorProps {
  armyCards: ArmyCardData[];
  onAttributesGenerated: (cardId: string, attributes: CardAttributes) => void;
  userPoints: number;
  onPointsChange: (points: number) => void;
  currentSchema?: AttributeSchema;
  onSchemaUpdate: (schema: AttributeSchema) => void;
}

export const AttributeGenerator: React.FC<AttributeGeneratorProps> = ({
  armyCards,
  onAttributesGenerated,
  userPoints,
  onPointsChange,
  currentSchema,
  onSchemaUpdate
}) => {
  const [attributeFields, setAttributeFields] = useState<AttributeField[]>([]);
  const [batchGroups, setBatchGroups] = useState<BatchGroup[]>([]);
  const [currentBatch, setCurrentBatch] = useState<BatchGroup | null>(null);
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [isGenerating, setIsGenerating] = useState(false);
  const [progress, setProgress] = useState({ current: 0, total: 0, stage: '' });
  const [standardCharacters, setStandardCharacters] = useState<ArmyCardData[]>([]);

  // 初始化时加载现有的属性模式
  useEffect(() => {
    if (currentSchema) {
      const allFields = [
        ...currentSchema.baseAttributes,
        ...currentSchema.specialAttributes
      ];
      setAttributeFields(allFields);
    }
  }, [currentSchema]);

  // 分析成本
  const ANALYSIS_COST = 50;
  const BATCH_GENERATION_COST = 30;

  // 初始化：按职业分组
  useEffect(() => {
    const groups = new Map<string, ArmyCardData[]>();

    armyCards.forEach(card => {
      const key = card.characterClass; // 只按职业分组，不考虑阵营

      if (!groups.has(key)) {
        groups.set(key, []);
      }
      groups.get(key)!.push(card);
    });

    const batchGroups: BatchGroup[] = Array.from(groups.entries()).map(([characterClass, cards], index) => {
      // 提取该职业下的所有阵营信息用于显示
      const factions = [...new Set(cards.map(card => card.stylePrompt || '默认阵营'))];
      const factionDisplay = factions.length > 1 ? `多阵营(${factions.length})` : factions[0];

      // 检查该批次是否已处理（所有卡牌都有属性）
      const processed = cards.every(card => card.attributes !== undefined);

      // 收集已有的属性数据
      const attributes: Record<string, CardAttributes> = {};
      cards.forEach(card => {
        if (card.attributes) {
          attributes[card.id] = card.attributes;
        }
      });

      return {
        id: `batch-${index}`,
        name: `${characterClass} (${factionDisplay})`,
        cards,
        characterClass,
        faction: factionDisplay, // 用于显示的阵营信息
        processed,
        attributes: Object.keys(attributes).length > 0 ? attributes : undefined
      };
    });

    setBatchGroups(batchGroups);
  }, [armyCards]);

  // 第一步：分析所有角色，定义属性字段体系
  const analyzeAttributeFields = async () => {
    if (userPoints < ANALYSIS_COST) {
      alert(`积分不足！需要 ${ANALYSIS_COST} 积分进行属性字段分析`);
      return;
    }

    setIsAnalyzing(true);
    setProgress({ current: 0, total: 1, stage: '分析属性字段体系...' });

    try {
      onPointsChange(userPoints - ANALYSIS_COST);

      // 收集所有职业和技能信息
      const classInfo = new Map<string, { skills: Set<string>, descriptions: string[] }>();
      
      armyCards.forEach(card => {
        if (!classInfo.has(card.characterClass)) {
          classInfo.set(card.characterClass, { skills: new Set(), descriptions: [] });
        }
        const info = classInfo.get(card.characterClass)!;
        card.skills.forEach(skill => info.skills.add(skill.name));
        info.descriptions.push(card.description);
      });

      const classAnalysis = Array.from(classInfo.entries()).map(([className, info]) => ({
        className,
        skills: Array.from(info.skills),
        sampleDescriptions: info.descriptions.slice(0, 3)
      }));

      const prompt = `
你是游戏平衡设计师。分析${classAnalysis.length}种职业，设计属性字段体系。只返回JSON，不要解释。

职业分析：
${classAnalysis.map(c => `
${c.className}：技能-${c.skills.slice(0, 3).join('、')}
`).join('\n')}

设计要求：
1. 基础属性6个：攻击、防御、生命、速度、智力、领导力
2. 特殊属性：根据职业特色（如腐蚀度、魔抗、治疗力等）
3. 每个属性1-10级制
4. 明确特殊属性适用职业

只返回JSON：
{
  "baseAttributes": [
    {"name": "攻击", "description": "物理攻击能力"},
    {"name": "防御", "description": "物理防御能力"},
    {"name": "生命", "description": "生命值上限"},
    {"name": "速度", "description": "行动速度"},
    {"name": "智力", "description": "魔法相关能力"},
    {"name": "领导", "description": "领导和指挥能力"}
  ],
  "specialAttributes": [
    {"name": "特殊属性名", "description": "描述", "applicableClasses": ["职业1", "职业2"]}
  ]
}`;

      const response = await generateContent(prompt);
      console.log('属性字段分析 - AI响应:', response);

      // 更鲁棒的JSON解析
      let result;
      try {
        // 尝试直接解析
        result = JSON.parse(response);
      } catch (parseError) {
        console.log('直接解析失败，尝试提取JSON...');

        // 尝试提取JSON代码块
        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          try {
            result = JSON.parse(jsonMatch[1]);
            console.log('从代码块提取JSON成功');
          } catch (blockError) {
            console.error('代码块JSON解析失败:', blockError);
            throw new Error('无法解析AI返回的JSON格式');
          }
        } else {
          // 尝试提取大括号内容
          const braceMatch = response.match(/\{[\s\S]*\}/);
          if (braceMatch) {
            try {
              result = JSON.parse(braceMatch[0]);
              console.log('从大括号提取JSON成功');
            } catch (braceError) {
              console.error('大括号JSON解析失败:', braceError);
              throw new Error('无法解析AI返回的JSON格式');
            }
          } else {
            console.error('未找到有效的JSON内容');
            throw new Error('AI返回的内容不包含有效的JSON');
          }
        }
      }

      console.log('解析后的结果:', result);
      
      const fields: AttributeField[] = [
        ...result.baseAttributes.map((attr: any) => ({
          ...attr,
          applicableClasses: [], // 基础属性适用于所有职业
          isSpecial: false
        })),
        ...result.specialAttributes.map((attr: any) => ({
          ...attr,
          isSpecial: true
        }))
      ];

      setAttributeFields(fields);

      // 创建并保存属性模式
      const newSchema: AttributeSchema = {
        id: `schema-${Date.now()}`,
        baseAttributes: fields.filter(f => !f.isSpecial),
        specialAttributes: fields.filter(f => f.isSpecial),
        version: '1.0.0',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      onSchemaUpdate(newSchema);
      setProgress({ current: 1, total: 1, stage: '属性字段分析完成并已保存！' });
      
    } catch (error) {
      console.error('属性字段分析失败:', error);
      onPointsChange(userPoints); // 退还积分
      alert('属性字段分析失败，请重试');
    } finally {
      setIsAnalyzing(false);
    }
  };

  // 第二步：为每个批次生成属性
  const generateBatchAttributes = async (batch: BatchGroup) => {
    if (userPoints < BATCH_GENERATION_COST) {
      alert(`积分不足！需要 ${BATCH_GENERATION_COST} 积分生成批次属性`);
      return;
    }

    if (attributeFields.length === 0) {
      alert('请先完成属性字段分析');
      return;
    }

    setIsGenerating(true);
    setCurrentBatch(batch);
    setProgress({ current: 0, total: batch.cards.length, stage: `生成 ${batch.name} 属性...` });

    try {
      onPointsChange(userPoints - BATCH_GENERATION_COST);

      // 选择标定角色（同职业中稀有度最高的几个）
      const standardCards = batch.cards
        .sort((a, b) => {
          const rarityOrder = { '传说': 4, '史诗': 3, '稀有': 2, '不常见': 1, '普通': 0 };
          const aOrder = rarityOrder[a.rarity.level as keyof typeof rarityOrder] || 0;
          const bOrder = rarityOrder[b.rarity.level as keyof typeof rarityOrder] || 0;
          return bOrder - aOrder;
        })
        .slice(0, 5); // 增加参考角色数量

      // 收集同职业的已有属性信息作为参考
      const existingAttributesRef = batch.cards
        .filter(card => card.attributes?.baseAttributes)
        .map(card => ({
          name: card.name,
          rarity: card.rarity.level,
          attributes: card.attributes!.baseAttributes
        }))
        .slice(0, 3);

      // 准备属性字段信息
      const applicableFields = attributeFields.filter(field => 
        !field.isSpecial || field.applicableClasses.includes(batch.characterClass)
      );

      const prompt = `
你是游戏数值策划师。为以下${batch.characterClass}职业的具体角色生成属性值。

必须为这些确切的角色名称生成属性：
${batch.cards.map(card => `"${card.name}"`).join(', ')}

角色详细信息：
${batch.cards.map((card, index) => `
${index + 1}. 角色名："${card.name}"
   稀有度：${card.rarity.level}
   描述：${card.description}
   技能：${card.skills.map(s => s.name).join('、')}
   ${card.attributes?.baseAttributes ? `现有属性：${JSON.stringify(card.attributes.baseAttributes)}` : ''}
`).join('\n')}

可用属性字段：
${applicableFields.map(field => `- ${field.name}: ${field.description}`).join('\n')}

同职业参考标准：
${standardCards.map(card => `
"${card.name}" (${card.rarity.level}级)：${card.description.substring(0, 60)}...
技能特点：${card.skills.slice(0, 2).map(s => s.name).join('、')}
`).join('\n')}

${existingAttributesRef.length > 0 ? `
同职业已有属性参考：
${existingAttributesRef.map(ref => `
"${ref.name}" (${ref.rarity}级)：${JSON.stringify(ref.attributes)}
`).join('\n')}` : ''}

⚠️ 重要规则：
- 属性范围严格限制在1-10级，每个基础属性都必须在1-10之间
- 稀有度对应属性范围：传说级(8-10)、史诗级(7-9)、稀有级(6-8)、不常见级(5-7)、普通级(4-6)
- 同职业角色的属性分布应保持一致性，参考已有属性
- 根据角色描述和技能匹配属性特征
- 综合战力计算参考：(攻击+防御+生命+速度+智力+领导)*100 + 特殊属性总和*10

重要要求：
1. 必须使用上面列出的确切角色名称作为JSON的key
2. 不要创造新的角色名称
3. 属性值必须在1-10范围内
4. 参考同职业已有属性保持一致性
5. totalPower必须是计算后的具体数字，不要写公式或数学表达式
6. 只返回JSON，不要任何解释

JSON格式：
{
  "${batch.cards[0]?.name || '角色名'}": {
    "attack": 数值(1-10),
    "defense": 数值(1-10),
    "health": 数值(1-10),
    "speed": 数值(1-10),
    "intelligence": 数值(1-10),
    "leadership": 数值(1-10),
    "specialAttributes": {"特殊属性名": 数值},
    "totalPower": 具体数字(如3500)
  }${batch.cards.length > 1 ? ',\n  "其他角色名": {...}' : ''}
}`;

      const response = await generateContent(prompt);
      console.log(`批次属性生成 - ${batch.name} - AI响应:`, response);

      // 更鲁棒的JSON解析
      let attributesData;
      try {
        attributesData = JSON.parse(response);
      } catch (parseError) {
        console.log('直接解析失败，尝试提取JSON...');

        const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/);
        if (jsonMatch) {
          try {
            attributesData = JSON.parse(jsonMatch[1]);
            console.log('从代码块提取JSON成功');
          } catch (blockError) {
            console.error('代码块JSON解析失败:', blockError);
            throw new Error('无法解析AI返回的JSON格式');
          }
        } else {
          const braceMatch = response.match(/\{[\s\S]*\}/);
          if (braceMatch) {
            try {
              attributesData = JSON.parse(braceMatch[0]);
              console.log('从大括号提取JSON成功');
            } catch (braceError) {
              console.error('大括号JSON解析失败:', braceError);
              throw new Error('无法解析AI返回的JSON格式');
            }
          } else {
            console.error('未找到有效的JSON内容');
            throw new Error('AI返回的内容不包含有效的JSON');
          }
        }
      }

      console.log(`批次属性生成 - ${batch.name} - 解析后结果:`, attributesData);

      // 处理生成的属性数据
      const batchAttributes: Record<string, CardAttributes> = {};
      const balanceVersion = new Date().toISOString().split('T')[0];

      // 验证AI返回的角色名称
      const expectedNames = batch.cards.map(c => c.name);
      const returnedNames = Object.keys(attributesData);

      console.log('期望的角色名称:', expectedNames);
      console.log('AI返回的角色名称:', returnedNames);

      // 检查是否有不匹配的名称
      const unmatchedNames = returnedNames.filter(name => !expectedNames.includes(name));
      if (unmatchedNames.length > 0) {
        console.warn('AI返回了不存在的角色名称:', unmatchedNames);
        alert(`警告：AI返回了不存在的角色名称：${unmatchedNames.join(', ')}\n这可能导致属性分配失败。`);
      }

      Object.entries(attributesData).forEach(([cardName, attrs]: [string, any]) => {
        const card = batch.cards.find(c => c.name === cardName);
        if (card) {
          batchAttributes[card.id] = {
            attack: attrs.attack,
            defense: attrs.defense,
            health: attrs.health,
            speed: attrs.speed,
            intelligence: attrs.intelligence,
            leadership: attrs.leadership,
            specialAttributes: attrs.specialAttributes || {},
            totalPower: attrs.totalPower,
            generatedBy: 'ai',
            balanceVersion,
            batchId: batch.id,
            schemaVersion: currentSchema?.version,
            generatedAt: new Date().toISOString()
          };

          // 通知父组件
          onAttributesGenerated(card.id, batchAttributes[card.id]);
        } else {
          console.error(`未找到角色: ${cardName}`);
        }
      });

      // 检查是否有角色没有被分配属性
      const processedCardIds = Object.keys(batchAttributes);
      const unprocessedCards = batch.cards.filter(card => !processedCardIds.includes(card.id));

      if (unprocessedCards.length > 0) {
        console.warn('以下角色未被分配属性:', unprocessedCards.map(c => c.name));
        alert(`警告：以下角色未被分配属性：${unprocessedCards.map(c => c.name).join(', ')}\n请重试或检查AI响应。`);
      }

      // 更新批次状态
      setBatchGroups(prev => prev.map(b => 
        b.id === batch.id 
          ? { ...b, processed: true, attributes: batchAttributes }
          : b
      ));

      setProgress({ current: batch.cards.length, total: batch.cards.length, stage: '批次生成完成！' });

    } catch (error) {
      console.error('批次属性生成失败:', error);
      onPointsChange(userPoints); // 退还积分
      alert('批次属性生成失败，请重试');
    } finally {
      setIsGenerating(false);
      setCurrentBatch(null);
    }
  };

  // 重新生成批次属性（不使用现有属性作为参照）
  const regenerateBatchAttributes = async (batch: BatchGroup) => {
    if (userPoints < BATCH_GENERATION_COST) {
      alert(`积分不足！需要 ${BATCH_GENERATION_COST} 积分重新生成批次属性`);
      return;
    }

    const confirmed = confirm(`确定要重新生成 "${batch.name}" 的属性吗？这将覆盖现有属性并消耗 ${BATCH_GENERATION_COST} 积分。`);
    if (!confirmed) return;

    setIsGenerating(true);
    setCurrentBatch(batch);
    setProgress({ current: 0, total: batch.cards.length, stage: '开始重新生成批次属性...' });

    try {
      // 扣除积分
      onPointsChange(userPoints - BATCH_GENERATION_COST);

      // 创建不包含现有属性的卡牌副本，用于重新生成
      const cardsWithoutAttributes = batch.cards.map(card => ({
        ...card,
        attributes: undefined // 移除现有属性，让AI重新生成
      }));

      // 选择标定角色（同职业中稀有度最高的几个，但不使用它们的属性作为参考）
      const standardCards = cardsWithoutAttributes
        .sort((a, b) => {
          const rarityOrder = { '传说': 4, '史诗': 3, '稀有': 2, '不常见': 1, '普通': 0 };
          const aOrder = rarityOrder[a.rarity.level as keyof typeof rarityOrder] || 0;
          const bOrder = rarityOrder[b.rarity.level as keyof typeof rarityOrder] || 0;
          return bOrder - aOrder;
        })
        .slice(0, 5);

      // 不收集已有属性信息，让AI完全重新生成
      const existingAttributesRef: any[] = [];

      const prompt = `
你是游戏数值策划师。为以下${batch.characterClass}职业的具体角色重新生成属性值。

必须为这些确切的角色名称生成属性：
${batch.cards.map(card => `"${card.name}"`).join(', ')}

角色详细信息：
${batch.cards.map((card, index) => `
${index + 1}. 角色名："${card.name}"
   稀有度：${card.rarity.level}
   描述：${card.description}
   技能：${card.skills.map(s => s.name).join('、')}
`).join('\n')}

可用属性字段：
${applicableFields.map(field => `- ${field.name}: ${field.description}`).join('\n')}

同职业参考标准（仅作为职业特点参考，不参考具体数值）：
${standardCards.map(card => `
"${card.name}" (${card.rarity.level}级)：${card.description.substring(0, 60)}...
技能特点：${card.skills.slice(0, 2).map(s => s.name).join('、')}
`).join('\n')}

⚠️ 重要规则：
- 属性范围严格限制在1-10级，每个基础属性都必须在1-10之间
- 稀有度对应属性范围：传说级(8-10)、史诗级(7-9)、稀有级(6-8)、不常见级(5-7)、普通级(4-6)
- 根据角色描述和技能匹配属性特征，完全重新设计数值
- 同职业角色应有相似的属性分布特点，但具体数值要有差异化
- 综合战力 = (攻击+防御+生命+速度+智力+领导)*100 + 特殊属性总和*10

请按照以下格式进行评估，使用"战力点"作为标准单位（1000战力点 = 一个标准步兵连的战斗力）

⚠️ 重要：所有数值必须是计算后的最终数字，不要使用数学表达式或公式！

{
  "analyses": [
    {
      "unitName": "单位名称",
      "individualPower": 具体数字(如5000),
      "strengths": ["优势1", "优势2", "优势3"],
      "weaknesses": ["劣势1", "劣势2"],
      "description": "角色总体描述，包括职业特点、战斗风格、在团队中的作用等",
      "suggestedAttributes": {
        "attack": 数值(1-10),
        "defense": 数值(1-10),
        "health": 数值(1-10),
        "speed": 数值(1-10),
        "intelligence": 数值(1-10),
        "leadership": 数值(1-10)
      }
    }
  ]
}

要求：
1. 每个单位都必须分析，战力点数要合理反映稀有度和能力
2. 属性建议必须在1-10级范围内，完全重新设计数值
3. individualPower必须是计算后的具体数字，不要写公式或表达式
4. 稀有度参考：传说级(8000-12000战力)、史诗级(6000-9000战力)、稀有级(4000-7000战力)、不常见级(2000-5000战力)、普通级(1000-3000战力)`;

      const response = await generateContent(prompt);
      const analysisData = parseAIResponse(response, `重新生成批次属性 - ${batch.name}`);

      if (!analysisData.analyses || !Array.isArray(analysisData.analyses)) {
        throw new Error('AI响应格式错误：缺少analyses数组');
      }

      const balanceVersion = new Date().toISOString().split('T')[0];
      const batchAttributes: Record<string, CardAttributes> = {};

      // 处理AI分析结果
      analysisData.analyses.forEach((analysis: any) => {
        const card = batch.cards.find(c => c.name === analysis.unitName);
        if (card && analysis.suggestedAttributes) {
          const attrs = analysis.suggestedAttributes;
          batchAttributes[card.id] = {
            attack: Math.max(1, Math.min(10, attrs.attack || 5)),
            defense: Math.max(1, Math.min(10, attrs.defense || 5)),
            health: Math.max(1, Math.min(10, attrs.health || 5)),
            speed: Math.max(1, Math.min(10, attrs.speed || 5)),
            intelligence: Math.max(1, Math.min(10, attrs.intelligence || 5)),
            leadership: Math.max(1, Math.min(10, attrs.leadership || 5)),
            specialAttributes: {},
            totalPower: analysis.individualPower || 0,
            generatedBy: 'ai',
            balanceVersion,
            batchId: batch.id,
            schemaVersion: currentSchema?.version,
            generatedAt: new Date().toISOString()
          };

          // 通知父组件更新卡牌属性
          onAttributesGenerated(card.id, batchAttributes[card.id]);
        }
      });

      // 更新批次状态
      setBatchGroups(prev => prev.map(b =>
        b.id === batch.id
          ? { ...b, processed: true, attributes: batchAttributes }
          : b
      ));

      setProgress({ current: batch.cards.length, total: batch.cards.length, stage: '批次重新生成完成！' });

    } catch (error) {
      console.error('批次属性重新生成失败:', error);
      onPointsChange(userPoints); // 退还积分
      alert('批次属性重新生成失败，请重试');
    } finally {
      setIsGenerating(false);
      setCurrentBatch(null);
    }
  };

  return (
    <div className="p-6 bg-slate-800 rounded-lg">
      <h2 className="text-2xl font-bold text-white mb-6">角色属性生成系统</h2>
      
      {/* 统计信息 */}
      <div className="grid grid-cols-3 gap-4 mb-6">
        <div className="bg-slate-700 p-4 rounded">
          <div className="text-slate-300">总角色数</div>
          <div className="text-2xl font-bold text-white">{armyCards.length}</div>
        </div>
        <div className="bg-slate-700 p-4 rounded">
          <div className="text-slate-300">批次数量</div>
          <div className="text-2xl font-bold text-white">{batchGroups.length}</div>
        </div>
        <div className="bg-slate-700 p-4 rounded">
          <div className="text-slate-300">已处理批次</div>
          <div className="text-2xl font-bold text-green-400">
            {batchGroups.filter(b => b.processed).length}
          </div>
        </div>
      </div>

      {/* 第一步：属性字段分析 */}
      <div className="mb-6">
        <h3 className="text-xl font-semibold text-white mb-3">第一步：属性字段分析</h3>
        <div className="flex items-center gap-4">
          <button
            onClick={analyzeAttributeFields}
            disabled={isAnalyzing || userPoints < ANALYSIS_COST}
            className="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 disabled:bg-gray-600"
          >
            {isAnalyzing ? '分析中...' : `分析属性字段 (${ANALYSIS_COST}积分)`}
          </button>
          {attributeFields.length > 0 && (
            <span className="text-green-400">✓ 已完成 ({attributeFields.length}个属性字段)</span>
          )}
        </div>
        
        {attributeFields.length > 0 && (
          <div className="mt-4 p-4 bg-slate-700 rounded">
            <h4 className="font-semibold text-white mb-2">属性字段：</h4>
            <div className="grid grid-cols-2 gap-2 text-sm">
              {attributeFields.map(field => (
                <div key={field.name} className="text-slate-300">
                  <span className="font-medium">{field.name}</span>
                  {field.isSpecial && (
                    <span className="text-yellow-400 ml-2">(特殊)</span>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* 第二步：批次处理 */}
      <div>
        <h3 className="text-xl font-semibold text-white mb-3">第二步：批次属性生成</h3>
        
        {progress.stage && (
          <div className="mb-4 p-3 bg-slate-700 rounded">
            <div className="text-slate-300">{progress.stage}</div>
            {progress.total > 0 && (
              <div className="w-full bg-slate-600 rounded-full h-2 mt-2">
                <div 
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(progress.current / progress.total) * 100}%` }}
                />
              </div>
            )}
          </div>
        )}

        <div className="space-y-3">
          {batchGroups.map(batch => (
            <div key={batch.id} className={`flex items-center justify-between p-4 rounded-lg border-2 transition-all duration-300 ${
              batch.processed
                ? 'bg-green-900/30 border-green-500/50 shadow-lg shadow-green-500/20'
                : 'bg-slate-700 border-slate-600 hover:border-purple-500/50'
            }`}>
              <div className="flex items-center gap-3">
                {/* 状态指示器 */}
                <div className={`w-3 h-3 rounded-full ${
                  batch.processed
                    ? 'bg-green-400 shadow-lg shadow-green-400/50'
                    : 'bg-gray-500'
                }`} />
                <div>
                  <div className="font-medium text-white flex items-center gap-2">
                    {batch.name}
                    {batch.processed && (
                      <span className="text-xs bg-green-500/20 text-green-400 px-2 py-1 rounded-full border border-green-500/30">
                        ✓ 已生成
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-slate-300">{batch.cards.length} 个角色</div>
                </div>
              </div>
              <div className="flex items-center gap-3">
                {batch.processed ? (
                  <div className="flex items-center gap-3">
                    <div className="flex items-center gap-2">
                      <span className="text-green-400 font-medium">✓ 已完成</span>
                      <div className="text-xs text-slate-400">
                        {Object.keys(batch.attributes || {}).length} 个角色已生成属性
                      </div>
                    </div>
                    <button
                      onClick={() => regenerateBatchAttributes(batch)}
                      disabled={isGenerating || userPoints < BATCH_GENERATION_COST || attributeFields.length === 0}
                      className="px-3 py-1 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 text-white rounded-lg transition-all duration-200 hover:scale-105 disabled:hover:scale-100 text-sm"
                      title="重新生成该批次的属性"
                    >
                      {isGenerating && currentBatch?.id === batch.id ? '重新生成中...' : `重新生成 (${BATCH_GENERATION_COST}积分)`}
                    </button>
                  </div>
                ) : (
                  <button
                    onClick={() => generateBatchAttributes(batch)}
                    disabled={isGenerating || userPoints < BATCH_GENERATION_COST || attributeFields.length === 0}
                    className="px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 disabled:bg-gray-600 transition-all duration-200 hover:scale-105 disabled:hover:scale-100"
                  >
                    {isGenerating && currentBatch?.id === batch.id ? '生成中...' : `生成 (${BATCH_GENERATION_COST}积分)`}
                  </button>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};
