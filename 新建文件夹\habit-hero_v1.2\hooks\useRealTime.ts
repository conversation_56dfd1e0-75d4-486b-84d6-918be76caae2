
import { useState, useEffect } from 'react';

const useRealTime = (updateInterval: number = 1000) => {
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const timerId = setInterval(() => {
      setCurrentTime(new Date());
    }, updateInterval);

    return () => {
      clearInterval(timerId);
    };
  }, [updateInterval]);

  return currentTime;
};

export default useRealTime;
