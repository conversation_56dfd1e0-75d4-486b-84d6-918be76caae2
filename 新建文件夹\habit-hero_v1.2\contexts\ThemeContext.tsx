import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

export type Theme = 'light' | 'dark';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
  setTheme: (theme: Theme) => void;
}

const ThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useTheme = () => {
  const context = useContext(ThemeContext);
  if (context === undefined) {
    throw new Error('useTheme must be used within a ThemeProvider');
  }
  return context;
};

interface ThemeProviderProps {
  children: ReactNode;
}

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  const [theme, setThemeState] = useState<Theme>(() => {
    // 从localStorage读取保存的主题，默认为dark
    if (typeof window !== 'undefined') {
      const savedTheme = localStorage.getItem('habit-hero-theme') as Theme;
      return savedTheme || 'dark';
    }
    return 'dark';
  });

  const setTheme = (newTheme: Theme) => {
    setThemeState(newTheme);
    localStorage.setItem('habit-hero-theme', newTheme);
    
    // 更新HTML根元素的class
    if (typeof window !== 'undefined') {
      const root = window.document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(newTheme);
    }
  };

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  useEffect(() => {
    // 初始化时设置HTML根元素的class
    if (typeof window !== 'undefined') {
      const root = window.document.documentElement;
      root.classList.remove('light', 'dark');
      root.classList.add(theme);
    }
  }, [theme]);

  const value = {
    theme,
    toggleTheme,
    setTheme,
  };

  return (
    <ThemeContext.Provider value={value}>
      {children}
    </ThemeContext.Provider>
  );
};

// 主题相关的CSS类名工具函数
export const getThemeClasses = (theme: Theme) => {
  const baseClasses = {
    // 背景色
    background: theme === 'dark' 
      ? 'bg-slate-900' 
      : 'bg-gray-50',
    
    // 卡片背景
    cardBackground: theme === 'dark' 
      ? 'bg-slate-800' 
      : 'bg-white',
    
    // 侧边栏背景
    sidebarBackground: theme === 'dark' 
      ? 'bg-gradient-to-b from-slate-800 via-slate-850 to-slate-900' 
      : 'bg-gradient-to-b from-white via-gray-50 to-gray-100',
    
    // 文本颜色
    textPrimary: theme === 'dark' 
      ? 'text-slate-100' 
      : 'text-gray-900',
    
    textSecondary: theme === 'dark' 
      ? 'text-slate-400' 
      : 'text-gray-600',
    
    // 边框颜色
    border: theme === 'dark' 
      ? 'border-slate-700' 
      : 'border-gray-200',
    
    // 悬停效果
    hover: theme === 'dark' 
      ? 'hover:bg-slate-700' 
      : 'hover:bg-gray-100',
    
    // 输入框
    input: theme === 'dark' 
      ? 'bg-slate-700 border-slate-600 text-white placeholder-slate-400' 
      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500',
    
    // 按钮
    buttonPrimary: theme === 'dark' 
      ? 'bg-sky-600 hover:bg-sky-700 text-white' 
      : 'bg-sky-500 hover:bg-sky-600 text-white',
    
    buttonSecondary: theme === 'dark' 
      ? 'bg-slate-700 hover:bg-slate-600 text-slate-200' 
      : 'bg-gray-200 hover:bg-gray-300 text-gray-800',
  };

  return baseClasses;
};
