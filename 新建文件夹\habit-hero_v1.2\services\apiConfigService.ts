// API配置服务 - 支持多供应商
export interface ApiProvider {
  id: string;
  name: string;
  type: 'gemini' | 'openai';
  baseUrl?: string;
  apiKey: string;
  models: {
    text: string[];
    image: string[];
  };
}

export interface ApiConfig {
  providers: ApiProvider[];
  textProvider: string; // provider id
  imageProvider: string; // provider id
  selectedTextModel: string;
  selectedImageModel: string;
}

// 默认配置
const DEFAULT_CONFIG: ApiConfig = {
  providers: [
    {
      id: 'gemini-default',
      name: 'Google Gemini',
      type: 'gemini',
      apiKey: 'AIzaSyCu5MqxX5CW2ZvYtWqr9san9ZyI5cE3kLY',
      models: {
        text: [
          'gemini-2.5-flash',
          'gemini-1.5-flash',
          'gemini-1.5-pro',
          'gemini-pro',
          'gemini-2.0-flash',
          'gemini-2.5-flash-lite-preview-06-17'
        ],
        image: ['imagen-3.0-generate-002']
      }
    }
  ],
  textProvider: 'gemini-default',
  imageProvider: 'gemini-default',
  selectedTextModel: 'gemini-2.5-flash',
  selectedImageModel: 'imagen-3.0-generate-002'
};

let currentConfig: ApiConfig = { ...DEFAULT_CONFIG };

// 从localStorage加载配置
export const loadApiConfig = (): ApiConfig => {
  try {
    const saved = localStorage.getItem('api-config');
    if (saved) {
      const parsed = JSON.parse(saved);
      // 合并默认配置以确保向后兼容
      currentConfig = {
        ...DEFAULT_CONFIG,
        ...parsed,
        providers: [
          ...DEFAULT_CONFIG.providers,
          ...(parsed.providers || []).filter((p: ApiProvider) => 
            !DEFAULT_CONFIG.providers.some(dp => dp.id === p.id)
          )
        ]
      };
    }
  } catch (error) {
    console.error('Failed to load API config:', error);
    currentConfig = { ...DEFAULT_CONFIG };
  }
  return currentConfig;
};

// 保存配置到localStorage
export const saveApiConfig = (config: ApiConfig): void => {
  try {
    currentConfig = config;
    localStorage.setItem('api-config', JSON.stringify(config));
  } catch (error) {
    console.error('Failed to save API config:', error);
  }
};

// 获取当前配置
export const getCurrentConfig = (): ApiConfig => {
  return currentConfig;
};

// 获取指定供应商
export const getProvider = (providerId: string): ApiProvider | null => {
  return currentConfig.providers.find(p => p.id === providerId) || null;
};

// 获取当前文本生成供应商
export const getTextProvider = (): ApiProvider | null => {
  return getProvider(currentConfig.textProvider);
};

// 获取当前图片生成供应商
export const getImageProvider = (): ApiProvider | null => {
  return getProvider(currentConfig.imageProvider);
};

// 添加新供应商
export const addProvider = (provider: Omit<ApiProvider, 'id'>): string => {
  const id = `${provider.type}-${Date.now()}`;
  const newProvider: ApiProvider = { ...provider, id };
  
  currentConfig.providers.push(newProvider);
  saveApiConfig(currentConfig);
  
  return id;
};

// 更新供应商
export const updateProvider = (providerId: string, updates: Partial<Omit<ApiProvider, 'id'>>): boolean => {
  const index = currentConfig.providers.findIndex(p => p.id === providerId);
  if (index === -1) return false;
  
  currentConfig.providers[index] = { ...currentConfig.providers[index], ...updates };
  saveApiConfig(currentConfig);
  
  return true;
};

// 删除供应商
export const removeProvider = (providerId: string): boolean => {
  const index = currentConfig.providers.findIndex(p => p.id === providerId);
  if (index === -1) return false;
  
  // 不能删除默认供应商
  if (providerId === 'gemini-default') return false;
  
  currentConfig.providers.splice(index, 1);
  
  // 如果删除的是当前使用的供应商，切换到默认供应商
  if (currentConfig.textProvider === providerId) {
    currentConfig.textProvider = 'gemini-default';
  }
  if (currentConfig.imageProvider === providerId) {
    currentConfig.imageProvider = 'gemini-default';
  }
  
  saveApiConfig(currentConfig);
  return true;
};

// 设置文本生成供应商
export const setTextProvider = (providerId: string, modelName?: string): boolean => {
  const provider = getProvider(providerId);
  if (!provider) return false;
  
  currentConfig.textProvider = providerId;
  if (modelName && provider.models.text.includes(modelName)) {
    currentConfig.selectedTextModel = modelName;
  } else if (provider.models.text.length > 0) {
    currentConfig.selectedTextModel = provider.models.text[0];
  }
  
  saveApiConfig(currentConfig);
  return true;
};

// 设置图片生成供应商
export const setImageProvider = (providerId: string, modelName?: string): boolean => {
  const provider = getProvider(providerId);
  if (!provider) return false;
  
  currentConfig.imageProvider = providerId;
  if (modelName && provider.models.image.includes(modelName)) {
    currentConfig.selectedImageModel = modelName;
  } else if (provider.models.image.length > 0) {
    currentConfig.selectedImageModel = provider.models.image[0];
  }
  
  saveApiConfig(currentConfig);
  return true;
};

// 初始化配置
loadApiConfig();
