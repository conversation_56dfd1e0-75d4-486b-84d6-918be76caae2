// 简化的API配置服务
export interface SimpleApiConfig {
  text: {
    provider: 'gemini' | 'openai';
    apiKey: string;
    baseUrl?: string;
    model: string;
  };
  image: {
    provider: 'gemini' | 'openai';
    apiKey: string;
    baseUrl?: string;
    model: string;
  };
}

// 默认配置
const DEFAULT_CONFIG: SimpleApiConfig = {
  text: {
    provider: 'gemini',
    apiKey: 'AIzaSyCu5MqxX5CW2ZvYtWqr9san9ZyI5cE3kLY',
    model: 'gemini-2.5-flash'
  },
  image: {
    provider: 'gemini',
    apiKey: 'AIzaSyCu5MqxX5CW2ZvYtWqr9san9ZyI5cE3kLY',
    model: 'imagen-3.0-generate-002'
  }
};

let currentConfig: SimpleApiConfig = { ...DEFAULT_CONFIG };

// 从localStorage加载配置
export const loadApiConfig = (): SimpleApiConfig => {
  try {
    const saved = localStorage.getItem('simple-api-config');
    if (saved) {
      const parsed = JSON.parse(saved);
      // 合并默认配置以确保向后兼容
      currentConfig = {
        ...DEFAULT_CONFIG,
        ...parsed,
        text: { ...DEFAULT_CONFIG.text, ...parsed.text },
        image: { ...DEFAULT_CONFIG.image, ...parsed.image }
      };
    }
  } catch (error) {
    console.error('Failed to load API config:', error);
    currentConfig = { ...DEFAULT_CONFIG };
  }
  return currentConfig;
};

// 保存配置到localStorage
export const saveApiConfig = (config: SimpleApiConfig): void => {
  try {
    currentConfig = config;
    localStorage.setItem('simple-api-config', JSON.stringify(config));
  } catch (error) {
    console.error('Failed to save API config:', error);
  }
};

// 获取当前配置
export const getCurrentConfig = (): SimpleApiConfig => {
  return currentConfig;
};

// 获取文本生成配置
export const getTextConfig = () => {
  return currentConfig.text;
};

// 获取图片生成配置
export const getImageConfig = () => {
  return currentConfig.image;
};

// 初始化配置
loadApiConfig();
