import { useState, useEffect, useCallback } from 'react';
import { storageService } from '../utils/storageService';

/**
 * Enhanced storage hook that uses IndexedDB for better performance and larger data support
 * Falls back to localStorage for compatibility
 */
function useEnhancedStorage<T>(key: string, initialValue: T): [T, (value: T | ((val: T) => T)) => void, boolean] {
  const [storedValue, setStoredValue] = useState<T>(initialValue);
  const [isLoaded, setIsLoaded] = useState(false);

  // Load initial value from storage
  useEffect(() => {
    const loadInitialValue = async () => {
      try {
        const item = await storageService.getItem<T>(key);
        if (item !== null) {
          setStoredValue(item);
        }
      } catch (error) {
        console.error('Error reading storage key "' + key + '":', error);
        // Try fallback to localStorage for migration
        try {
          const fallbackItem = window.localStorage.getItem(key);
          if (fallbackItem) {
            const parsedValue = JSON.parse(fallbackItem);
            setStoredValue(parsedValue);
            // Migrate to new storage
            await storageService.setItem(key, parsedValue);
            console.log(`Migrated ${key} from localStorage to enhanced storage`);
          }
        } catch (fallbackError) {
          console.error('Error reading fallback localStorage key "' + key + '":', fallbackError);
        }
      } finally {
        setIsLoaded(true);
      }
    };

    loadInitialValue();
  }, [key]);

  const setValue = useCallback(async (value: T | ((val: T) => T)) => {
    try {
      const valueToStore = value instanceof Function ? value(storedValue) : value;
      setStoredValue(valueToStore);
      await storageService.setItem(key, valueToStore);
    } catch (error) {
      console.error('Error setting storage key "' + key + '":', error);
      // Fallback to localStorage
      try {
        const valueToStore = value instanceof Function ? value(storedValue) : value;
        window.localStorage.setItem(key, JSON.stringify(valueToStore));
      } catch (fallbackError) {
        console.error('Error setting fallback localStorage key "' + key + '":', fallbackError);
      }
    }
  }, [key, storedValue]);

  // Handle storage events (for localStorage compatibility)
  useEffect(() => {
    const handleStorageChange = (event: StorageEvent) => {
      if (event.key === key) {
        try {
          setStoredValue(event.newValue ? JSON.parse(event.newValue) : initialValue);
        } catch (error) {
          console.error('Error parsing storage change for key "' + key + '":', error);
          setStoredValue(initialValue);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => {
      window.removeEventListener('storage', handleStorageChange);
    };
  }, [key, initialValue]);

  // Return [value, setter, isLoaded]
  return [storedValue, setValue, isLoaded];
}

export default useEnhancedStorage;
