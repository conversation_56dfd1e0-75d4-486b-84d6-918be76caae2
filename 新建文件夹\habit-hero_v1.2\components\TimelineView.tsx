
import React, { useMemo, useState } from 'react';
import { Task, JournalEntry, TaskType } from '../types';
import { getFormattedDate } from '../utils/dateUtils';
import { PencilSquareIcon, CheckIcon as TaskCompletedIcon, ChevronDownIcon } from './icons'; // Using CheckIcon as TaskCompletedIcon

interface TimelineItemData {
  id: string;
  date: Date; // Date object for grouping (YYYY-MM-DD at 00:00:00 local time)
  timestamp: number; // Precise timestamp for sorting
  type: 'task' | 'journal';
  title: string;
  icon?: string; // Emoji
  originalIcon: React.ReactNode; // Component icon (Check for task, Pencil for journal)
  contentDetails?: string | React.ReactNode; // e.g., points for task
  data: Task | JournalEntry;
}

interface TimelineViewProps {
  tasks: Task[];
  journalEntries: JournalEntry[];
}

const TimelineView: React.FC<TimelineViewProps> = ({ tasks, journalEntries }) => {
  const [visibleCount, setVisibleCount] = useState(10); // 初始显示10个项目
  const ITEMS_PER_PAGE = 10; // 每次加载10个

  const timelineItems = useMemo(() => {
    const items: TimelineItemData[] = [];

    // Process Journal Entries
    journalEntries.forEach(entry => {
      const entryTimestamp = new Date(entry.createdAt).getTime();
      const entryGroupingDate = new Date(getFormattedDate(new Date(entry.createdAt)) + "T00:00:00"); // Group by creation day

      items.push({
        id: `journal-${entry.id}`,
        date: entryGroupingDate, // Use creation date for grouping
        timestamp: entryTimestamp, // Precise creation timestamp for sorting
        type: 'journal',
        title: entry.content,
        icon: entry.icon,
        originalIcon: <PencilSquareIcon className="w-5 h-5 text-purple-400" />,
        data: entry,
      });
    });

    // Process Task Completions
    tasks.forEach(task => {
      if (task.type === TaskType.ONCE) {
        if (task.isCompleted && task.actualCompletionDate) {
          const completionTimestamp = new Date(task.actualCompletionDate).getTime();
          const completionGroupingDate = new Date(getFormattedDate(new Date(task.actualCompletionDate)) + "T00:00:00");
          items.push({
            id: `task-${task.id}-${task.actualCompletionDate}`,
            date: completionGroupingDate,
            timestamp: completionTimestamp,
            type: 'task',
            title: task.title,
            icon: task.emoji,
            originalIcon: <TaskCompletedIcon className="w-5 h-5 text-green-400" />,
            contentDetails: <span className="text-xs text-yellow-400">+{task.points} 积分</span>,
            data: task,
          });
        }
      } else { // DAILY or WEEKLY
        // completedDates now stores precise ISO strings of completion
        task.completedDates?.forEach(isoCompletionString => {
          const completionTimestamp = new Date(isoCompletionString).getTime();
          const completionGroupingDate = new Date(getFormattedDate(new Date(isoCompletionString)) + "T00:00:00");
          items.push({
            id: `task-${task.id}-${isoCompletionString}`, // Ensure unique ID using the ISO string
            date: completionGroupingDate,
            timestamp: completionTimestamp, // Use the precise timestamp from isoCompletionString
            type: 'task',
            title: task.title,
            icon: task.emoji,
            originalIcon: <TaskCompletedIcon className="w-5 h-5 text-green-400" />,
            contentDetails: <span className="text-xs text-yellow-400">+{task.points} 积分</span>,
            data: task,
          });
        });
      }
    });

    // Sort all items by timestamp, most recent first
    items.sort((a, b) => b.timestamp - a.timestamp);
    return items;
  }, [tasks, journalEntries]);

  const groupedItems = useMemo(() => {
    return timelineItems.reduce<Record<string, TimelineItemData[]>>((acc, item) => {
      const itemDateStr = getFormattedDate(item.date); // item.date is already normalized for grouping
      if (!acc[itemDateStr]) {
        acc[itemDateStr] = [];
      }
      acc[itemDateStr].push(item); 
      return acc;
    }, {});
  }, [timelineItems]);

  const sortedDateKeys = useMemo(() => {
    return Object.keys(groupedItems).sort((a, b) => new Date(b).getTime() - new Date(a).getTime());
  }, [groupedItems]);

  // 计算可见的时间轴项目
  const visibleItems = useMemo(() => {
    const allItems: { dateStr: string; items: TimelineItemData[] }[] = [];
    let totalCount = 0;

    for (const dateStr of sortedDateKeys) {
      const dayItems = groupedItems[dateStr];
      if (totalCount + dayItems.length <= visibleCount) {
        // 整天都可以显示
        allItems.push({ dateStr, items: dayItems });
        totalCount += dayItems.length;
      } else {
        // 只显示部分项目
        const remainingCount = visibleCount - totalCount;
        if (remainingCount > 0) {
          allItems.push({ dateStr, items: dayItems.slice(0, remainingCount) });
        }
        break;
      }
    }

    return allItems;
  }, [sortedDateKeys, groupedItems, visibleCount]);

  const hasMoreItems = timelineItems.length > visibleCount;

  const loadMore = () => {
    setVisibleCount(prev => prev + ITEMS_PER_PAGE);
  };

  if (timelineItems.length === 0) {
    return <p className="text-center text-slate-400 py-12 text-lg">你的时间轴还是空的。完成任务或记录日志来填充吧！🚀</p>;
  }

  return (
    <div className="w-full space-y-8 pb-8">
      {/* 统计信息 */}
      <div className="bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
        <div className="flex items-center justify-between text-sm">
          <span className="text-slate-400">
            显示 {Math.min(visibleCount, timelineItems.length)} / {timelineItems.length} 个时间轴事件
          </span>
          <span className="text-slate-400">
            {sortedDateKeys.length} 天有活动记录
          </span>
        </div>
      </div>

      {/* 时间轴内容 */}
      {visibleItems.map(({ dateStr, items }) => (
        <div key={dateStr} className="relative">
          <h2 className="text-xl font-semibold text-sky-400 mb-4 sticky top-0 bg-slate-900 py-2 z-10">
            {new Date(dateStr + "T00:00:00").toLocaleDateString('zh-CN', { year: 'numeric', month: 'long', day: 'numeric', weekday: 'long' })}
            <span className="text-sm text-slate-400 ml-2">({items.length} 个事件)</span>
          </h2>
          <div className="border-l-2 border-slate-700 pl-6 space-y-4 relative ml-3">
            {items.map((item) => (
              <div key={item.id} className="relative timeline-item">
                <div className={`absolute -left-[31px] top-1.5 w-4 h-4 rounded-full border-2 border-slate-900 z-10 ${item.type === 'task' ? 'bg-green-500' : 'bg-purple-500'}`}></div>

                <div className="bg-slate-800 p-4 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-200">
                  <div className="flex items-start space-x-3">
                    <span className="text-2xl mt-0.5">{item.icon || item.originalIcon}</span>
                    <div className="flex-grow min-w-0">
                      <p className={`font-medium ${item.type === 'task' ? 'text-slate-100' : 'text-slate-200'} whitespace-pre-wrap break-words`}>
                        {item.title}
                      </p>
                      {item.contentDetails && (
                        <p className="text-sm mt-1">{item.contentDetails}</p>
                      )}
                      <p className="text-xs text-slate-400 mt-1">
                        {new Date(item.timestamp).toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}
                        {item.type === 'task' && (item.data as Task).category ? ` - ${(item.data as Task).category}` : ''}
                         {item.type === 'journal' ? ` - 日志于 ${new Date((item.data as JournalEntry).date + "T00:00:00").toLocaleDateString('zh-CN', { month: 'short', day: 'numeric' })}` : ''}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      ))}

      {/* 加载更多按钮 */}
      {hasMoreItems && (
        <div className="text-center pt-6">
          <button
            onClick={loadMore}
            className="px-6 py-3 bg-gradient-to-r from-sky-600 to-blue-600 hover:from-sky-700 hover:to-blue-700 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 shadow-lg flex items-center space-x-2 mx-auto"
          >
            <ChevronDownIcon className="w-5 h-5" />
            <span>显示更多 ({Math.min(ITEMS_PER_PAGE, timelineItems.length - visibleCount)} 个)</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default TimelineView;
