
import React, { useState, useEffect } from 'react';
import { Task, TaskType } from '../types';
import { CheckIcon, TrashIcon } from './icons';
import { getFormattedDate, getCompletionsInCurrentWeek } from '../utils/dateUtils';
import { DAYS_OF_WEEK_OPTIONS } from '../constants'; // Corrected import path

interface TaskItemProps {
  task: Task;
  onToggleComplete: (taskId: string, date: Date) => void;
  onDelete: (taskId: string) => void;
  onToggleHighlight?: (taskId: string) => void; // 新增：切换重要标记
  selectedDate: Date;
  isCompleted: boolean;
}

const TaskItem: React.FC<TaskItemProps> = ({ task, onToggleComplete, onDelete, onToggleHighlight, selectedDate, isCompleted }) => {
  const formattedSelectedDate = getFormattedDate(selectedDate);
  const [isAnimating, setIsAnimating] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const [justCompleted, setJustCompleted] = useState(false);

  let completionText = "";
  let canCompleteToday = true; 

  const taskCreationDate = new Date(task.createdAt);
  taskCreationDate.setHours(0,0,0,0);
  const normalizedSelectedDate = new Date(selectedDate);
  normalizedSelectedDate.setHours(0,0,0,0);

  if (normalizedSelectedDate < taskCreationDate) {
    canCompleteToday = false; 
  }


  if (task.type === TaskType.WEEKLY) {
    const completionsThisWeek = getCompletionsInCurrentWeek(task, selectedDate);
    if (task.timesPerWeek) {
        completionText = ` (本周 ${completionsThisWeek}/${task.timesPerWeek})`;
        // Check if weekly goal met AND this specific day is NOT marked as completed (using .some with ISO strings)
        const completedOnSelectedDate = task.completedDates?.some(iso => getFormattedDate(new Date(iso)) === formattedSelectedDate);
        if (completionsThisWeek >= task.timesPerWeek && !completedOnSelectedDate) {
             canCompleteToday = false; 
        }
    }
    if (!task.daysOfWeek?.includes(selectedDate.getDay())) { 
        canCompleteToday = false; 
    }
  }
  
  if (task.type === TaskType.ONCE && task.isCompleted) {
      // If a ONCE task is completed, it generally cannot be completed again *unless* we are viewing its actual completion date.
      // The `isCompleted` prop (derived from isTaskCompletedOnDate) for a ONCE task is true if task.isCompleted is true.
      // `canCompleteToday` should be false if it's already completed, to prevent re-completing.
      // Exception: if its actualCompletionDate is today, perhaps allow un-checking?
      // For simplicity: if task.isCompleted is true, you can only un-check it. The button will show a check.
      // The onToggleComplete will handle un-completing it.
      // So, `canCompleteToday` should effectively be true for toggling, but the action is "uncomplete".
      // The disabled state handles if further completions are blocked.
      // Let's ensure the disabled state is correct:
      if (task.actualCompletionDate && getFormattedDate(new Date(task.actualCompletionDate)) !== formattedSelectedDate) {
        // If it's completed, but its completion date is not the selected date, you can't interact with it from this selectedDate.
        // This scenario might be rare if getTasksForDate filters correctly.
        // However, if it shows up:
        // canCompleteToday = false; // This seems too restrictive if task.isCompleted is the main driver for the checkmark.
      }
      // If task.isCompleted is true, the main purpose is to allow un-checking.
      // The `isCompleted` prop handles the visual state.
      // The `disabled` attribute handles interactability.
      // If it's completed (isCompleted = true), the button should be enabled for un-checking.
      // Thus, canCompleteToday logic as modified above for weekly should suffice.
      // For ONCE, if task.isCompleted = true, it means it's done. `canCompleteToday` isn't about *further* completion.
      // It's about whether the button is active. If it's done, it should be active to un-check.
      // Let's simplify: if it's already completed (isCompleted = true), the button is active for un-toggling.
      // if it's NOT completed, then canCompleteToday applies.
      // This is handled by `disabled={!canCompleteToday && !isCompleted}`
  }


  const handleComplete = () => {
    if ((canCompleteToday && !isCompleted) || isCompleted) {
        setIsAnimating(true);
        if (!isCompleted) {
          setJustCompleted(true);
          // 完成动画
          setTimeout(() => {
            setJustCompleted(false);
          }, 1000);
        }

        setTimeout(() => {
          onToggleComplete(task.id, selectedDate);
          setIsAnimating(false);
        }, 150);
    }
  };

  const handleDelete = () => {
    setIsDeleting(true);
    setTimeout(() => {
      onDelete(task.id);
    }, 300);
  };

  const dayLabels = DAYS_OF_WEEK_OPTIONS.map(opt => opt.label);

  return (
    <div className={`group relative flex items-center justify-between p-4 rounded-xl shadow-lg transition-all duration-500 transform ${
      isDeleting
        ? 'scale-95 opacity-0 translate-x-4'
        : isAnimating
          ? 'scale-105'
          : 'scale-100'
    } ${
      isCompleted
        ? 'bg-gradient-to-r from-green-500/10 to-emerald-500/10 border border-green-500/20'
        : task.isHighlighted
          ? 'bg-gradient-to-r from-red-500/10 to-orange-500/10 hover:from-red-500/15 hover:to-orange-500/15 border border-red-500/30 hover:border-red-500/40 ring-1 ring-red-500/20'
          : 'bg-gradient-to-r from-slate-800 to-slate-700 hover:from-slate-700 hover:to-slate-600 border border-slate-600/50 hover:border-slate-500/50'
    } hover:shadow-xl hover:shadow-slate-900/20 ${
      task.isHighlighted && !isCompleted ? 'hover:shadow-red-500/10' : ''
    }`}>

      {/* 完成庆祝效果 */}
      {justCompleted && (
        <div className="absolute inset-0 rounded-xl bg-gradient-to-r from-green-400/20 to-emerald-400/20 animate-pulse pointer-events-none" />
      )}

      {/* 完成时的粒子效果 */}
      {justCompleted && (
        <>
          <div className="absolute top-2 left-4 w-2 h-2 bg-yellow-400 rounded-full animate-ping" />
          <div className="absolute top-4 right-8 w-1 h-1 bg-green-400 rounded-full animate-ping animation-delay-200" />
          <div className="absolute bottom-3 left-8 w-1.5 h-1.5 bg-blue-400 rounded-full animate-ping animation-delay-400" />
        </>
      )}

      <div className="relative z-10 flex items-center space-x-4">
        <button
          onClick={handleComplete}
          disabled={!canCompleteToday && !isCompleted}
          className={`group/btn w-8 h-8 flex-shrink-0 rounded-xl border-2 flex items-center justify-center transition-all duration-300 transform relative overflow-hidden ${
            isCompleted
              ? 'bg-gradient-to-r from-green-500 to-emerald-500 border-green-400 shadow-lg shadow-green-500/25 scale-110'
              : 'bg-slate-600 border-slate-500 hover:border-sky-400 hover:bg-slate-500 hover:scale-110 hover:shadow-lg hover:shadow-sky-500/20'
          } ${
            (!canCompleteToday && !isCompleted)
              ? 'cursor-not-allowed opacity-50'
              : 'cursor-pointer active:scale-95'
          }`}
          aria-label={isCompleted ? "标记任务为未完成" : "标记任务为完成"}
        >
          {/* 按钮背景光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/0 group-hover/btn:from-white/10 group-hover/btn:to-transparent rounded-xl transition-all duration-300" />

          {isCompleted && (
            <CheckIcon className={`w-5 h-5 text-white transition-transform duration-300 ${isAnimating ? 'scale-125 rotate-12' : 'scale-100'}`} />
          )}

          {/* 未完成状态的加号提示 */}
          {!isCompleted && canCompleteToday && (
            <div className="w-3 h-3 border border-slate-300 rounded opacity-60 group-hover/btn:opacity-100 transition-opacity duration-300" />
          )}
        </button>
        <div className="flex-grow">
          <div className="flex items-center space-x-2">
            {task.emoji && (
              <span className={`text-xl transition-transform duration-300 ${
                justCompleted ? 'animate-bounce' : isCompleted ? 'grayscale' : ''
              }`}>
                {task.emoji}
              </span>
            )}
            <p className={`text-lg font-medium transition-all duration-500 ${
              isCompleted
                ? 'line-through text-slate-400 opacity-70'
                : 'text-slate-100 group-hover:text-white'
            }`}>
              {task.title}
            </p>

            {/* 重要任务标识 */}
            {task.isHighlighted && (
              <span className="flex items-center space-x-1">
                <span className="text-red-400 text-lg animate-pulse">⭐</span>
                <span className="text-xs text-red-400 font-semibold bg-red-500/20 px-2 py-0.5 rounded-full border border-red-500/30">
                  重要
                </span>
              </span>
            )}

            {/* 完成时的庆祝图标 */}
            {justCompleted && (
              <span className="text-yellow-400 animate-bounce text-lg">🎉</span>
            )}
          </div>

          <div className={`mt-1 transition-all duration-300 ${isCompleted ? 'opacity-60' : 'opacity-80 group-hover:opacity-100'}`}>
            <span className="text-xs text-sky-400 font-medium">
              {task.category && (
                <span className="inline-block bg-sky-500/20 text-sky-300 px-2 py-0.5 rounded-full mr-2 text-xs">
                  {task.category}
                </span>
              )}
              {task.type}
              {task.type === TaskType.WEEKLY && task.daysOfWeek ? ` 每周于 ${task.daysOfWeek.map(d => dayLabels[d]).join(', ')}` : ''}
              {completionText}
            </span>
            <span className="text-xs text-yellow-400 font-semibold ml-2">
              +{task.points} 积分
            </span>
          </div>
        </div>
      </div>

      <div className="flex items-center space-x-2">
        {/* 重要标记按钮 */}
        {onToggleHighlight && (
          <button
            onClick={() => onToggleHighlight(task.id)}
            className={`group/highlight relative z-10 transition-all duration-300 p-2 rounded-xl hover:scale-110 active:scale-95 flex-shrink-0 ${
              task.isHighlighted
                ? 'text-red-400 hover:text-red-300 bg-red-500/10 hover:bg-red-500/20'
                : 'text-slate-500 hover:text-red-400 hover:bg-red-500/10'
            }`}
            aria-label={task.isHighlighted ? "取消重要标记" : "标记为重要"}
          >
            <span className={`text-lg transition-transform duration-300 group-hover/highlight:scale-110 ${
              task.isHighlighted ? 'animate-pulse' : ''
            }`}>
              ⭐
            </span>

            {/* 重要标记按钮的提示 */}
            <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-2 py-1 rounded opacity-0 group-hover/highlight:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
              {task.isHighlighted ? '取消重要' : '标记重要'}
            </div>
          </button>
        )}

        {/* 删除按钮 */}
        <button
          onClick={handleDelete}
          className="group/delete relative z-10 text-slate-500 hover:text-red-400 transition-all duration-300 p-2 rounded-xl hover:bg-red-500/10 hover:scale-110 active:scale-95 flex-shrink-0"
          aria-label="删除任务"
        >
          <TrashIcon className="w-5 h-5 transition-transform duration-300 group-hover/delete:scale-110" />

          {/* 删除按钮的危险提示 */}
          <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-red-500 text-white text-xs px-2 py-1 rounded opacity-0 group-hover/delete:opacity-100 transition-opacity duration-300 pointer-events-none whitespace-nowrap">
            删除任务
          </div>
        </button>
      </div>
    </div>
  );
};

export default TaskItem;
