import React from 'react';
import { CardAttributes } from '../types';

interface AttributeDisplayProps {
  attributes: CardAttributes;
  compact?: boolean;
  showSpecialOnly?: boolean;
}

export const AttributeDisplay: React.FC<AttributeDisplayProps> = ({
  attributes,
  compact = false,
  showSpecialOnly = false
}) => {
  const baseAttributes = [
    { key: 'attack', name: '攻击', icon: '⚔️', color: 'text-red-400' },
    { key: 'defense', name: '防御', icon: '🛡️', color: 'text-blue-400' },
    { key: 'health', name: '生命', icon: '❤️', color: 'text-green-400' },
    { key: 'speed', name: '速度', icon: '⚡', color: 'text-yellow-400' },
    { key: 'intelligence', name: '智力', icon: '🧠', color: 'text-purple-400' },
    { key: 'leadership', name: '领导', icon: '👑', color: 'text-orange-400' }
  ];

  const getAttributeBar = (value: number, maxValue: number = 10) => {
    const percentage = (value / maxValue) * 100;
    return (
      <div className="w-full bg-slate-600 rounded-full h-2">
        <div 
          className="bg-gradient-to-r from-slate-400 to-white h-2 rounded-full transition-all duration-300"
          style={{ width: `${percentage}%` }}
        />
      </div>
    );
  };

  const getStarRating = (value: number, maxValue: number = 10) => {
    const stars = Math.round((value / maxValue) * 5);
    return (
      <div className="flex">
        {[...Array(5)].map((_, i) => (
          <span key={i} className={i < stars ? 'text-yellow-400' : 'text-slate-600'}>
            ⭐
          </span>
        ))}
      </div>
    );
  };

  if (compact) {
    return (
      <div className="space-y-1">
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium text-white">战力</span>
          <span className="text-sm font-bold text-yellow-400">
            {Math.round(attributes.totalPower)}
          </span>
        </div>
        <div className="grid grid-cols-3 gap-1 text-xs">
          {baseAttributes.slice(0, 3).map(attr => (
            <div key={attr.key} className="flex items-center gap-1">
              <span>{attr.icon}</span>
              <span className={attr.color}>
                {attributes[attr.key as keyof CardAttributes] as number}
              </span>
            </div>
          ))}
        </div>
        {Object.keys(attributes.specialAttributes).length > 0 && (
          <div className="text-xs text-slate-300">
            特殊: {Object.keys(attributes.specialAttributes).join('、')}
          </div>
        )}
      </div>
    );
  }

  return (
    <div className="p-4 bg-slate-700 rounded-lg">
      {/* 综合战力 */}
      <div className="mb-4 text-center">
        <div className="text-sm text-slate-300 mb-1">综合战力</div>
        <div className="text-2xl font-bold text-yellow-400">
          {Math.round(attributes.totalPower)}
        </div>
      </div>

      {/* 基础属性 */}
      {!showSpecialOnly && (
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-white mb-3">基础属性</h4>
          <div className="space-y-3">
            {baseAttributes.map(attr => {
              const value = attributes[attr.key as keyof CardAttributes] as number;
              return (
                <div key={attr.key} className="flex items-center gap-3">
                  <div className="flex items-center gap-2 w-16">
                    <span className="text-lg">{attr.icon}</span>
                    <span className={`text-sm font-medium ${attr.color}`}>
                      {attr.name}
                    </span>
                  </div>
                  <div className="flex-1">
                    {getAttributeBar(value)}
                  </div>
                  <div className="w-8 text-right">
                    <span className="text-sm font-bold text-white">{value}</span>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* 特殊属性 */}
      {Object.keys(attributes.specialAttributes).length > 0 && (
        <div className="mb-4">
          <h4 className="text-sm font-semibold text-white mb-3">特殊属性</h4>
          <div className="space-y-2">
            {Object.entries(attributes.specialAttributes).map(([key, value]) => (
              <div key={key} className="flex items-center justify-between">
                <span className="text-sm text-slate-300">{key}</span>
                <div className="flex items-center gap-2">
                  {getStarRating(value)}
                  <span className="text-sm font-bold text-cyan-400 w-6 text-right">
                    {value}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* 属性雷达图 (简化版) */}
      {!showSpecialOnly && (
        <div className="mt-4">
          <h4 className="text-sm font-semibold text-white mb-3">属性分布</h4>
          <div className="grid grid-cols-2 gap-2">
            {baseAttributes.map(attr => {
              const value = attributes[attr.key as keyof CardAttributes] as number;
              return (
                <div key={attr.key} className="flex items-center gap-2">
                  <span className="text-xs">{attr.icon}</span>
                  <div className="flex-1">
                    {getStarRating(value)}
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}

      {/* 元数据 */}
      <div className="mt-4 pt-3 border-t border-slate-600">
        <div className="text-xs text-slate-400 space-y-1">
          <div>生成方式: {attributes.generatedBy === 'ai' ? 'AI生成' : '手动设置'}</div>
          <div>平衡版本: {attributes.balanceVersion}</div>
          {attributes.batchId && <div>批次: {attributes.batchId}</div>}
          {attributes.generatedAt && (
            <div>生成时间: {new Date(attributes.generatedAt).toLocaleString('zh-CN')}</div>
          )}
        </div>
      </div>
    </div>
  );
};

// 属性比较组件
interface AttributeComparisonProps {
  cards: Array<{ name: string; attributes: CardAttributes }>;
  maxCards?: number;
}

export const AttributeComparison: React.FC<AttributeComparisonProps> = ({
  cards,
  maxCards = 3
}) => {
  const displayCards = cards.slice(0, maxCards);
  
  const baseAttributes = [
    { key: 'attack', name: '攻击', icon: '⚔️' },
    { key: 'defense', name: '防御', icon: '🛡️' },
    { key: 'health', name: '生命', icon: '❤️' },
    { key: 'speed', name: '速度', icon: '⚡' },
    { key: 'intelligence', name: '智力', icon: '🧠' },
    { key: 'leadership', name: '领导', icon: '👑' }
  ];

  const getMaxValue = (attrKey: string) => {
    return Math.max(...displayCards.map(card => 
      card.attributes[attrKey as keyof CardAttributes] as number
    ));
  };

  return (
    <div className="p-4 bg-slate-700 rounded-lg">
      <h3 className="text-lg font-semibold text-white mb-4">属性对比</h3>
      
      {/* 角色名称 */}
      <div className="grid gap-2 mb-4" style={{ gridTemplateColumns: `120px repeat(${displayCards.length}, 1fr)` }}>
        <div></div>
        {displayCards.map(card => (
          <div key={card.name} className="text-center">
            <div className="text-sm font-medium text-white truncate">{card.name}</div>
            <div className="text-xs text-yellow-400">
              {Math.round(card.attributes.totalPower)}
            </div>
          </div>
        ))}
      </div>

      {/* 属性对比 */}
      <div className="space-y-3">
        {baseAttributes.map(attr => {
          const maxValue = getMaxValue(attr.key);
          return (
            <div key={attr.key} className="grid gap-2" style={{ gridTemplateColumns: `120px repeat(${displayCards.length}, 1fr)` }}>
              <div className="flex items-center gap-2">
                <span>{attr.icon}</span>
                <span className="text-sm text-slate-300">{attr.name}</span>
              </div>
              {displayCards.map(card => {
                const value = card.attributes[attr.key as keyof CardAttributes] as number;
                const percentage = maxValue > 0 ? (value / maxValue) * 100 : 0;
                return (
                  <div key={card.name} className="flex items-center gap-2">
                    <div className="flex-1 bg-slate-600 rounded-full h-2">
                      <div 
                        className="bg-gradient-to-r from-blue-500 to-purple-500 h-2 rounded-full"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-xs text-white w-6 text-right">{value}</span>
                  </div>
                );
              })}
            </div>
          );
        })}
      </div>
    </div>
  );
};
