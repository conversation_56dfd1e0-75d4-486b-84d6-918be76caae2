
export enum TaskType {
  ONCE = '一次性',
  DAILY = '每日',
  WEEKLY = '每周',
}

export interface Task {
  id: string;
  title: string; // Changed from text to title for consistency
  description?: string; // Optional description
  type: TaskType;
  points: number; // Now user-definable
  createdAt?: string; // ISO string
  emoji?: string; // Changed from icon to emoji for consistency
  category?: string;
  isHighlighted?: boolean; // 是否为重要任务
  // For ONCE type
  dueDate?: string; // ISO string
  isCompleted?: boolean; // For ONCE tasks
  actualCompletionDate?: string; // For ONCE tasks, ISO string of when it was actually completed
  // For DAILY and WEEKLY types
  completedDates?: string[]; // Array of ISO date strings (YYYY-MM-DD)
  // For WEEKLY type
  daysOfWeek?: number[]; // 0 (Sun) to 6 (Sat)
  timesPerWeek?: number;
}

export enum JournalType {
  OUTPUT = 'output', // 输出型 - 记录发生的事情
  INPUT = 'input'    // 输入型 - 记录想法、感受、计划等
}

export interface JournalEntry {
  id: string;
  content: string; // Changed from text to content for consistency
  date: string; // YYYY-MM-DD, the date the journal entry is *for* (changed from entryDate)
  createdAt?: string; // ISO string, when the entry was created
  icon?: string; // Emoji
  type?: JournalType; // 日志类型，默认为输出型
}

export interface WeightRecord {
  id: string;
  weight: number; // 体重值（公斤）
  date: string; // YYYY-MM-DD, 记录日期
  createdAt?: string; // ISO string, 创建时间
  note?: string; // 可选备注
}

// Army card types (imported from geminiService)
export interface ArmySkill {
  name: string;
  description: string;
}

// 属性字段定义
export interface AttributeField {
  name: string;
  description: string;
  applicableClasses: string[];
  isSpecial: boolean; // 是否为特殊属性
}

// 属性字段模式（持久化存储）
export interface AttributeSchema {
  id: string;
  baseAttributes: AttributeField[];
  specialAttributes: AttributeField[];
  version: string;
  createdAt: string;
  updatedAt: string;
}

// 角色属性系统
export interface CardAttributes {
  // 基础属性 (1-10级)
  attack: number;      // 攻击力
  defense: number;     // 防御力
  health: number;      // 生命值
  speed: number;       // 速度
  intelligence: number; // 智力
  leadership: number;   // 领导力

  // 特殊属性 (动态字段，如腐蚀度、魔抗等)
  specialAttributes: Record<string, number>;

  // 综合战力评分
  totalPower: number;

  // 属性生成元数据
  generatedBy: 'ai' | 'manual';
  balanceVersion: string; // 用于追踪平衡性版本
  batchId?: string;       // 批次ID
  generatedAt?: string;   // 生成时间
  schemaVersion?: string; // 使用的属性模式版本
}

export interface ArmyCardData {
  id: string;
  name: string;
  characterClass: string;
  description: string;
  rarity: {
    level: string;
    color: string;
    glowColor: string;
    probability: number;
  };
  imageUrl: string;
  skills: ArmySkill[];
  story: string;
  stylePrompt?: string;  // 生成时使用的艺术风格
  createdAt?: string;    // 创建时间
  attributes?: CardAttributes; // 角色属性
}



export interface UserData {
  points: number;
  totalPointsEarned?: number; // 总共获得的积分总和
  tasks: Task[];
  journalEntries: JournalEntry[]; // Added for journal feature
  weightRecords: WeightRecord[]; // Added for weight management
  // Army system data
  armyCards: ArmyCardData[]; // Collection of army cards
  armyStylePrompt?: string; // User's preferred art style
  // Camp customization data
  campName?: string; // Camp name
  flagUrl?: string; // Flag image URL
  // Attribute system data
  attributeSchema?: AttributeSchema; // Current attribute schema
}

export type ViewMode = 'todo' | 'calendar' | 'timeline' | 'army' | 'camp' | 'weight' | 'statistics' | 'settings' | 'attributes';

export type TaskStatus = 'none' | 'pending' | 'completed';

export interface DateCell {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  isSelected: boolean;
  taskStatus?: TaskStatus;
  // No explicit 'hasJournalEntries' needed on cell based on user clarification
}

export interface CustomCharacterClass {
  id: string;
  name: string;
  description?: string;
  faction: string;
  cost: number;
  createdAt: string;
}

declare global {
  interface Window {
    electronAPI?: {
      exportData: (data: string) => Promise<{success: boolean, path?: string}>;
      importData: () => Promise<{success: boolean, data?: string, error?: string}>;
      hideWindow: () => Promise<void>;
    }
  }
}
