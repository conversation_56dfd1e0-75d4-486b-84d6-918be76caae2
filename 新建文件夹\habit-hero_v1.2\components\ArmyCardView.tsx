import React from 'react';
import { ArmyCardData, ArmySkill } from '../types';

interface ArmyCardViewProps {
  card: ArmyCardData | null;
  isRevealing: boolean;
  isDetailsVisible: boolean;
  onCardClick: () => void;
}

const CardBack = () => (
  <div className="absolute inset-0 w-full h-full bg-gray-800 rounded-2xl flex items-center justify-center p-4 border-2 border-purple-400/50 shadow-lg transform rotate-y-180 backface-hidden">
    <div className="text-center">
      <div className="text-purple-300 text-5xl font-bold">?</div>
    </div>
  </div>
);

const CardFront: React.FC<Omit<ArmyCardViewProps, 'isRevealing'>> = ({ card, isDetailsVisible, onCardClick }) => {
  if (!card) {
    return (
      <div className="absolute inset-0 w-full h-full bg-gray-800/50 rounded-2xl flex items-center justify-center p-4 border-2 border-dashed border-gray-600 backface-hidden">
        <div className="text-center">
          <div className="text-purple-300 text-5xl font-bold">📦</div>
          <p className="mt-4 text-gray-400">选择职业开始召唤！</p>
        </div>
      </div>
    );
  }

  return (
    <div 
      className="absolute inset-0 w-full h-full bg-gray-800 rounded-2xl overflow-hidden shadow-lg backface-hidden flex flex-col cursor-pointer"
      onClick={onCardClick}
      role="button"
      aria-pressed={isDetailsVisible}
      tabIndex={0}
      onKeyDown={(e) => { if (e.key === 'Enter') onCardClick(); }}
    >
      {/* Image Section */}
      <div className="w-full h-2/5 relative flex-shrink-0">
        <img src={card.imageUrl} alt={card.name} className="w-full h-full object-cover" />
        <div className="absolute inset-0 bg-gradient-to-t from-gray-800 to-transparent"></div>
      </div>

      {/* Content Section */}
      <div className="flex-1 p-4 text-white flex flex-col overflow-hidden">
        <header>
          <p className={`font-bold text-sm ${card.rarity.color}`}>{card.rarity.level}</p>
          <h3 className="text-2xl font-bold tracking-tight text-shadow">{card.name}</h3>
          <p className="text-xs text-gray-400 mb-1">{card.characterClass}</p>
          <p className="text-sm text-gray-300 mt-1 mb-3 text-shadow-sm">{card.description}</p>
        </header>
        
        {/* Collapsible Details */}
        <div className={`transition-all duration-500 ease-in-out overflow-y-auto flex-grow flex flex-col ${isDetailsVisible ? 'max-h-full opacity-100' : 'max-h-0 opacity-0'}`}>
          {/* Skills Section */}
          {card.skills && card.skills.length > 0 && (
            <div className="space-y-3 mb-3 pr-2">
              <h4 className="font-bold text-base text-purple-300 border-b border-purple-300/20 pb-1">技能</h4>
              {card.skills.map((skill, index) => (
                <div key={index}>
                  <p className="font-semibold text-sm text-gray-100">{skill.name}</p>
                  <p className="text-xs text-gray-400">{skill.description}</p>
                </div>
              ))}
            </div>
          )}
          {/* Story Section */}
          {card.story && (
            <footer className="mt-auto border-t border-gray-600/50 pt-2">
              <p className="text-xs text-gray-500 italic">{card.story}</p>
            </footer>
          )}
        </div>

        {/* Click hint */}
        {!isDetailsVisible && (
          <div className="text-center mt-auto p-2 text-xs text-gray-400 border-t border-gray-600/50">
            点击查看详情
          </div>
        )}
      </div>
    </div>
  );
};

export const ArmyCardView: React.FC<ArmyCardViewProps> = ({ card, isRevealing, isDetailsVisible, onCardClick }) => {
  return (
    <div className="w-full max-w-sm h-[600px] perspective-1000">
      <div className={`relative w-full h-full transition-transform duration-700 transform-style-preserve-3d ${isRevealing ? '' : 'rotate-y-180'}`}>
        <CardBack />
        <CardFront card={card} isDetailsVisible={isDetailsVisible} onCardClick={onCardClick} />
      </div>
    </div>
  );
};
