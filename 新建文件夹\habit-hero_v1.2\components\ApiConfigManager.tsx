import React, { useState, useEffect } from 'react';
import {
  getCurrentConfig,
  saveApiConfig,
  addProvider,
  updateProvider,
  removeProvider,
  setTextProvider,
  setImageProvider,
  type ApiConfig,
  type ApiProvider
} from '../services/apiConfigService';

interface ApiConfigManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

const ApiConfigManager: React.FC<ApiConfigManagerProps> = ({ isOpen, onClose }) => {
  const [config, setConfig] = useState<ApiConfig>(getCurrentConfig());
  const [showAddProvider, setShowAddProvider] = useState(false);
  const [newProvider, setNewProvider] = useState({
    name: '',
    type: 'openai' as 'gemini' | 'openai',
    baseUrl: '',
    apiKey: '',
    textModels: '',
    imageModels: ''
  });

  useEffect(() => {
    if (isOpen) {
      setConfig(getCurrentConfig());
    }
  }, [isOpen]);

  const handleSave = () => {
    saveApiConfig(config);
    onClose();
  };

  const handleAddProvider = () => {
    if (!newProvider.name || !newProvider.apiKey) {
      alert('请填写供应商名称和API密钥');
      return;
    }

    const textModels = newProvider.textModels.split(',').map(m => m.trim()).filter(m => m);
    const imageModels = newProvider.imageModels.split(',').map(m => m.trim()).filter(m => m);

    if (textModels.length === 0 && imageModels.length === 0) {
      alert('请至少添加一个文本模型或图片模型');
      return;
    }

    const providerId = addProvider({
      name: newProvider.name,
      type: newProvider.type,
      baseUrl: newProvider.baseUrl || undefined,
      apiKey: newProvider.apiKey,
      models: {
        text: textModels,
        image: imageModels
      }
    });

    setConfig(getCurrentConfig());
    setNewProvider({
      name: '',
      type: 'openai',
      baseUrl: '',
      apiKey: '',
      textModels: '',
      imageModels: ''
    });
    setShowAddProvider(false);
  };

  const handleRemoveProvider = (providerId: string) => {
    if (confirm('确定要删除这个供应商吗？')) {
      removeProvider(providerId);
      setConfig(getCurrentConfig());
    }
  };

  const handleTextProviderChange = (providerId: string, modelName?: string) => {
    setTextProvider(providerId, modelName);
    setConfig(getCurrentConfig());
  };

  const handleImageProviderChange = (providerId: string, modelName?: string) => {
    setImageProvider(providerId, modelName);
    setConfig(getCurrentConfig());
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-slate-800 rounded-xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">API配置管理</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        {/* 当前配置概览 */}
        <div className="mb-6 p-4 bg-slate-700 rounded-lg">
          <h3 className="text-lg font-semibold text-white mb-3">当前配置</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm text-gray-300 mb-1">文本生成</label>
              <div className="text-white">
                {config.providers.find(p => p.id === config.textProvider)?.name || '未配置'} 
                <span className="text-gray-400 ml-2">({config.selectedTextModel})</span>
              </div>
            </div>
            <div>
              <label className="block text-sm text-gray-300 mb-1">图片生成</label>
              <div className="text-white">
                {config.providers.find(p => p.id === config.imageProvider)?.name || '未配置'}
                <span className="text-gray-400 ml-2">({config.selectedImageModel})</span>
              </div>
            </div>
          </div>
        </div>

        {/* 供应商列表 */}
        <div className="mb-6">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-semibold text-white">供应商管理</h3>
            <button
              onClick={() => setShowAddProvider(true)}
              className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg"
            >
              添加供应商
            </button>
          </div>

          <div className="space-y-4">
            {config.providers.map(provider => (
              <div key={provider.id} className="bg-slate-700 p-4 rounded-lg">
                <div className="flex justify-between items-start mb-3">
                  <div>
                    <h4 className="text-white font-medium">{provider.name}</h4>
                    <p className="text-gray-400 text-sm">
                      {provider.type.toUpperCase()} 
                      {provider.baseUrl && ` • ${provider.baseUrl}`}
                    </p>
                  </div>
                  {provider.id !== 'gemini-default' && (
                    <button
                      onClick={() => handleRemoveProvider(provider.id)}
                      className="text-red-400 hover:text-red-300"
                    >
                      删除
                    </button>
                  )}
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {/* 文本模型选择 */}
                  {provider.models.text.length > 0 && (
                    <div>
                      <label className="block text-sm text-gray-300 mb-1">文本模型</label>
                      <div className="flex gap-2">
                        <select
                          value={config.textProvider === provider.id ? config.selectedTextModel : ''}
                          onChange={(e) => handleTextProviderChange(provider.id, e.target.value)}
                          className="flex-1 bg-slate-600 text-white p-2 rounded border border-gray-600"
                        >
                          <option value="">选择模型</option>
                          {provider.models.text.map(model => (
                            <option key={model} value={model}>{model}</option>
                          ))}
                        </select>
                        <button
                          onClick={() => handleTextProviderChange(provider.id)}
                          className={`px-3 py-2 rounded text-sm ${
                            config.textProvider === provider.id
                              ? 'bg-green-600 text-white'
                              : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                          }`}
                        >
                          {config.textProvider === provider.id ? '使用中' : '设为文本'}
                        </button>
                      </div>
                    </div>
                  )}

                  {/* 图片模型选择 */}
                  {provider.models.image.length > 0 && (
                    <div>
                      <label className="block text-sm text-gray-300 mb-1">图片模型</label>
                      <div className="flex gap-2">
                        <select
                          value={config.imageProvider === provider.id ? config.selectedImageModel : ''}
                          onChange={(e) => handleImageProviderChange(provider.id, e.target.value)}
                          className="flex-1 bg-slate-600 text-white p-2 rounded border border-gray-600"
                        >
                          <option value="">选择模型</option>
                          {provider.models.image.map(model => (
                            <option key={model} value={model}>{model}</option>
                          ))}
                        </select>
                        <button
                          onClick={() => handleImageProviderChange(provider.id)}
                          className={`px-3 py-2 rounded text-sm ${
                            config.imageProvider === provider.id
                              ? 'bg-green-600 text-white'
                              : 'bg-gray-600 text-gray-300 hover:bg-gray-500'
                          }`}
                        >
                          {config.imageProvider === provider.id ? '使用中' : '设为图片'}
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* 添加供应商表单 */}
        {showAddProvider && (
          <div className="mb-6 p-4 bg-slate-700 rounded-lg border border-blue-500">
            <h4 className="text-white font-medium mb-4">添加新供应商</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm text-gray-300 mb-1">供应商名称</label>
                <input
                  type="text"
                  value={newProvider.name}
                  onChange={(e) => setNewProvider({...newProvider, name: e.target.value})}
                  className="w-full bg-slate-600 text-white p-2 rounded border border-gray-600"
                  placeholder="例如: OpenAI"
                />
              </div>
              <div>
                <label className="block text-sm text-gray-300 mb-1">类型</label>
                <select
                  value={newProvider.type}
                  onChange={(e) => setNewProvider({...newProvider, type: e.target.value as 'gemini' | 'openai'})}
                  className="w-full bg-slate-600 text-white p-2 rounded border border-gray-600"
                >
                  <option value="openai">OpenAI格式</option>
                  <option value="gemini">Gemini格式</option>
                </select>
              </div>
              <div>
                <label className="block text-sm text-gray-300 mb-1">Base URL (可选)</label>
                <input
                  type="text"
                  value={newProvider.baseUrl}
                  onChange={(e) => setNewProvider({...newProvider, baseUrl: e.target.value})}
                  className="w-full bg-slate-600 text-white p-2 rounded border border-gray-600"
                  placeholder="https://api.openai.com/v1"
                />
              </div>
              <div>
                <label className="block text-sm text-gray-300 mb-1">API密钥</label>
                <input
                  type="password"
                  value={newProvider.apiKey}
                  onChange={(e) => setNewProvider({...newProvider, apiKey: e.target.value})}
                  className="w-full bg-slate-600 text-white p-2 rounded border border-gray-600"
                  placeholder="sk-..."
                />
              </div>
              <div>
                <label className="block text-sm text-gray-300 mb-1">文本模型 (逗号分隔)</label>
                <input
                  type="text"
                  value={newProvider.textModels}
                  onChange={(e) => setNewProvider({...newProvider, textModels: e.target.value})}
                  className="w-full bg-slate-600 text-white p-2 rounded border border-gray-600"
                  placeholder="gpt-4, gpt-3.5-turbo"
                />
              </div>
              <div>
                <label className="block text-sm text-gray-300 mb-1">图片模型 (逗号分隔)</label>
                <input
                  type="text"
                  value={newProvider.imageModels}
                  onChange={(e) => setNewProvider({...newProvider, imageModels: e.target.value})}
                  className="w-full bg-slate-600 text-white p-2 rounded border border-gray-600"
                  placeholder="dall-e-3, dall-e-2"
                />
              </div>
            </div>
            <div className="flex gap-2 mt-4">
              <button
                onClick={handleAddProvider}
                className="bg-green-600 hover:bg-green-700 text-white px-4 py-2 rounded"
              >
                添加
              </button>
              <button
                onClick={() => setShowAddProvider(false)}
                className="bg-gray-600 hover:bg-gray-700 text-white px-4 py-2 rounded"
              >
                取消
              </button>
            </div>
          </div>
        )}

        {/* 底部按钮 */}
        <div className="flex justify-end gap-2">
          <button
            onClick={onClose}
            className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg"
          >
            保存配置
          </button>
        </div>
      </div>
    </div>
  );
};

export default ApiConfigManager;
