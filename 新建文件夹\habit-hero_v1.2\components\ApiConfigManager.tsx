import React, { useState, useEffect } from 'react';

interface ApiConfigManagerProps {
  isOpen: boolean;
  onClose: () => void;
}

interface ApiConfig {
  text: {
    provider: 'gemini' | 'openai';
    apiKey: string;
    baseUrl?: string;
    model: string;
  };
  image: {
    provider: 'gemini' | 'openai';
    apiKey: string;
    baseUrl?: string;
    model: string;
  };
}

const DEFAULT_CONFIG: ApiConfig = {
  text: {
    provider: 'gemini',
    apiKey: 'AIzaSyCu5MqxX5CW2ZvYtWqr9san9ZyI5cE3kLY',
    model: 'gemini-2.5-flash'
  },
  image: {
    provider: 'gemini',
    apiKey: 'AIzaSyCu5MqxX5CW2ZvYtWqr9san9ZyI5cE3kLY',
    model: 'imagen-3.0-generate-002'
  }
};

const ApiConfigManager: React.FC<ApiConfigManagerProps> = ({ isOpen, onClose }) => {
  const [config, setConfig] = useState<ApiConfig>(DEFAULT_CONFIG);

  useEffect(() => {
    if (isOpen) {
      // 从localStorage加载配置
      try {
        const saved = localStorage.getItem('simple-api-config');
        if (saved) {
          const parsed = JSON.parse(saved);
          setConfig({ ...DEFAULT_CONFIG, ...parsed });
        }
      } catch (error) {
        console.error('Failed to load API config:', error);
        setConfig(DEFAULT_CONFIG);
      }
    }
  }, [isOpen]);

  const handleSave = () => {
    try {
      localStorage.setItem('simple-api-config', JSON.stringify(config));
      onClose();
    } catch (error) {
      console.error('Failed to save API config:', error);
      alert('保存配置失败');
    }
  };

  const updateTextConfig = (updates: Partial<ApiConfig['text']>) => {
    setConfig(prev => ({
      ...prev,
      text: { ...prev.text, ...updates }
    }));
  };

  const updateImageConfig = (updates: Partial<ApiConfig['image']>) => {
    setConfig(prev => ({
      ...prev,
      image: { ...prev.image, ...updates }
    }));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-slate-800 rounded-xl p-6 max-w-4xl w-full max-h-[90vh] overflow-y-auto">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-white">API配置管理</h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-white text-2xl"
          >
            ×
          </button>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* 文本生成配置 */}
          <div className="bg-slate-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              📝 文本生成配置
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-300 mb-2">供应商</label>
                <select
                  value={config.text.provider}
                  onChange={(e) => updateTextConfig({ provider: e.target.value as 'gemini' | 'openai' })}
                  className="w-full bg-slate-600 text-white p-3 rounded border border-gray-600"
                >
                  <option value="gemini">Google Gemini</option>
                  <option value="openai">OpenAI格式</option>
                </select>
              </div>

              <div>
                <label className="block text-sm text-gray-300 mb-2">API密钥</label>
                <input
                  type="password"
                  value={config.text.apiKey}
                  onChange={(e) => updateTextConfig({ apiKey: e.target.value })}
                  className="w-full bg-slate-600 text-white p-3 rounded border border-gray-600"
                  placeholder="输入API密钥"
                />
              </div>

              {config.text.provider === 'openai' && (
                <div>
                  <label className="block text-sm text-gray-300 mb-2">Base URL</label>
                  <input
                    type="text"
                    value={config.text.baseUrl || ''}
                    onChange={(e) => updateTextConfig({ baseUrl: e.target.value })}
                    className="w-full bg-slate-600 text-white p-3 rounded border border-gray-600"
                    placeholder="https://api.openai.com/v1"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm text-gray-300 mb-2">模型名称</label>
                <input
                  type="text"
                  value={config.text.model}
                  onChange={(e) => updateTextConfig({ model: e.target.value })}
                  className="w-full bg-slate-600 text-white p-3 rounded border border-gray-600"
                  placeholder={config.text.provider === 'gemini' ? 'gemini-2.5-flash' : 'gpt-4'}
                />
              </div>
            </div>
          </div>

          {/* 图片生成配置 */}
          <div className="bg-slate-700 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-4 flex items-center">
              🎨 图片生成配置
            </h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm text-gray-300 mb-2">供应商</label>
                <select
                  value={config.image.provider}
                  onChange={(e) => updateImageConfig({ provider: e.target.value as 'gemini' | 'openai' })}
                  className="w-full bg-slate-600 text-white p-3 rounded border border-gray-600"
                >
                  <option value="gemini">Google Gemini</option>
                  <option value="openai">OpenAI格式</option>
                </select>
              </div>

              <div>
                <label className="block text-sm text-gray-300 mb-2">API密钥</label>
                <input
                  type="password"
                  value={config.image.apiKey}
                  onChange={(e) => updateImageConfig({ apiKey: e.target.value })}
                  className="w-full bg-slate-600 text-white p-3 rounded border border-gray-600"
                  placeholder="输入API密钥"
                />
              </div>

              {config.image.provider === 'openai' && (
                <div>
                  <label className="block text-sm text-gray-300 mb-2">Base URL</label>
                  <input
                    type="text"
                    value={config.image.baseUrl || ''}
                    onChange={(e) => updateImageConfig({ baseUrl: e.target.value })}
                    className="w-full bg-slate-600 text-white p-3 rounded border border-gray-600"
                    placeholder="https://api.openai.com/v1"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm text-gray-300 mb-2">模型名称</label>
                <input
                  type="text"
                  value={config.image.model}
                  onChange={(e) => updateImageConfig({ model: e.target.value })}
                  className="w-full bg-slate-600 text-white p-3 rounded border border-gray-600"
                  placeholder={config.image.provider === 'gemini' ? 'imagen-3.0-generate-002' : 'dall-e-3'}
                />
              </div>
            </div>
          </div>
        </div>

        {/* 使用说明 */}
        <div className="mt-6 p-4 bg-slate-700/50 rounded-lg">
          <h4 className="text-white font-medium mb-2">💡 使用说明</h4>
          <ul className="text-sm text-gray-300 space-y-1">
            <li>• 文本和图片可以使用不同的供应商，实现成本优化</li>
            <li>• Gemini格式：直接使用Google AI Studio的API密钥</li>
            <li>• OpenAI格式：支持OpenAI官方API和第三方兼容服务</li>
            <li>• 模型名称请参考对应供应商的文档</li>
          </ul>
        </div>

        {/* 底部按钮 */}
        <div className="flex justify-end gap-3 mt-6">
          <button
            onClick={onClose}
            className="bg-gray-600 hover:bg-gray-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            取消
          </button>
          <button
            onClick={handleSave}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg transition-colors"
          >
            保存配置
          </button>
        </div>
      </div>
    </div>
  );
};

export default ApiConfigManager;
