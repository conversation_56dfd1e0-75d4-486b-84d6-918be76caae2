import React, { useMemo } from 'react';
import { WeightRecord } from '../types';

interface WeightChartProps {
  weightRecords: WeightRecord[];
  className?: string;
}

const WeightChart: React.FC<WeightChartProps> = ({ weightRecords, className = '' }) => {
  // 处理数据：按日期排序并过滤最近30天
  const chartData = useMemo(() => {
    const sortedRecords = [...weightRecords]
      .filter(record => record.weight > 0)
      .sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

    // 只显示最近30天的数据
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    return sortedRecords.filter(record => 
      new Date(record.date) >= thirtyDaysAgo
    );
  }, [weightRecords]);

  // 计算图表尺寸和比例
  const chartWidth = 600;
  const chartHeight = 300;
  const padding = 50;
  const innerWidth = chartWidth - padding * 2;
  const innerHeight = chartHeight - padding * 2;

  // 计算数据范围 - 使用更合理的Y轴范围
  const weights = chartData.map(record => record.weight);
  let minWeight = weights.length > 0 ? Math.min(...weights) : 0;
  let maxWeight = weights.length > 0 ? Math.max(...weights) : 100;

  // 为了更好的显示效果，在最小值和最大值基础上增加一些边距
  if (weights.length > 0) {
    const dataRange = maxWeight - minWeight;
    const margin = Math.max(dataRange * 0.1, 2); // 至少2kg的边距
    minWeight = Math.max(0, minWeight - margin);
    maxWeight = maxWeight + margin;
  }

  const weightRange = maxWeight - minWeight || 10; // 避免除零

  // 计算日期范围
  const dates = chartData.map(record => new Date(record.date).getTime());
  const minDate = dates.length > 0 ? Math.min(...dates) : Date.now();
  const maxDate = dates.length > 0 ? Math.max(...dates) : Date.now();
  const dateRange = maxDate - minDate || 86400000; // 避免除零，默认1天

  // 生成路径点
  const pathPoints = chartData.map((record, index) => {
    const x = padding + (new Date(record.date).getTime() - minDate) / dateRange * innerWidth;
    const y = padding + (maxWeight - record.weight) / weightRange * innerHeight;
    return { x, y, record, index };
  });

  // 生成SVG路径
  const pathD = pathPoints.length > 0 
    ? `M ${pathPoints.map(point => `${point.x},${point.y}`).join(' L ')}`
    : '';

  // 生成网格线
  const gridLines = [];
  const numHorizontalLines = 5;
  const numVerticalLines = 6;

  // 水平网格线（体重）
  for (let i = 0; i <= numHorizontalLines; i++) {
    const y = padding + (i / numHorizontalLines) * innerHeight;
    const weight = maxWeight - (i / numHorizontalLines) * weightRange;
    gridLines.push(
      <g key={`h-${i}`}>
        <line
          x1={padding}
          y1={y}
          x2={chartWidth - padding}
          y2={y}
          stroke="#374151"
          strokeWidth="1"
          strokeDasharray="2,2"
        />
        <text
          x={padding - 10}
          y={y + 4}
          fill="#9CA3AF"
          fontSize="12"
          textAnchor="end"
        >
          {weight.toFixed(1)}
        </text>
      </g>
    );
  }

  // 垂直网格线（日期）
  for (let i = 0; i <= numVerticalLines; i++) {
    const x = padding + (i / numVerticalLines) * innerWidth;
    const date = new Date(minDate + (i / numVerticalLines) * dateRange);
    gridLines.push(
      <g key={`v-${i}`}>
        <line
          x1={x}
          y1={padding}
          x2={x}
          y2={chartHeight - padding}
          stroke="#374151"
          strokeWidth="1"
          strokeDasharray="2,2"
        />
        <text
          x={x}
          y={chartHeight - padding + 20}
          fill="#9CA3AF"
          fontSize="12"
          textAnchor="middle"
        >
          {date.getMonth() + 1}/{date.getDate()}
        </text>
      </g>
    );
  }

  // 计算统计信息
  const stats = useMemo(() => {
    if (chartData.length === 0) return null;

    const currentWeight = chartData[chartData.length - 1]?.weight || 0;
    const previousWeight = chartData[chartData.length - 2]?.weight || currentWeight;
    const change = currentWeight - previousWeight;
    const avgWeight = weights.reduce((sum, w) => sum + w, 0) / weights.length;
    const actualMinWeight = weights.length > 0 ? Math.min(...weights) : 0; // 实际数据中的最小值

    return {
      current: currentWeight,
      change,
      average: avgWeight,
      min: actualMinWeight, // 显示实际最小值
      max: maxWeight,
      recordCount: chartData.length,
    };
  }, [chartData, weights, maxWeight]);

  if (chartData.length === 0) {
    return (
      <div className={`bg-slate-800 rounded-xl p-6 border border-slate-700 ${className}`}>
        <h3 className="text-xl font-bold text-white mb-4">体重趋势</h3>
        <div className="h-80 bg-slate-700/50 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <p className="text-slate-400 mb-2">暂无体重数据</p>
            <p className="text-slate-500 text-sm">开始记录体重以查看趋势图</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`bg-slate-800 rounded-xl p-6 border border-slate-700 ${className}`}>
      <h3 className="text-xl font-bold text-white mb-4">体重趋势</h3>
      
      {/* 统计信息 */}
      {stats && (
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
          <div className="bg-slate-700/50 rounded-lg p-3 text-center">
            <div className="text-sky-400 font-bold text-lg">{stats.current.toFixed(1)}</div>
            <div className="text-slate-400 text-xs">当前体重</div>
          </div>
          <div className="bg-slate-700/50 rounded-lg p-3 text-center">
            <div className={`font-bold text-lg ${stats.change >= 0 ? 'text-red-400' : 'text-green-400'}`}>
              {stats.change >= 0 ? '+' : ''}{stats.change.toFixed(1)}
            </div>
            <div className="text-slate-400 text-xs">较上次</div>
          </div>
          <div className="bg-slate-700/50 rounded-lg p-3 text-center">
            <div className="text-purple-400 font-bold text-lg">{stats.average.toFixed(1)}</div>
            <div className="text-slate-400 text-xs">平均体重</div>
          </div>
          <div className="bg-slate-700/50 rounded-lg p-3 text-center">
            <div className="text-yellow-400 font-bold text-lg">{stats.recordCount}</div>
            <div className="text-slate-400 text-xs">记录天数</div>
          </div>
        </div>
      )}

      {/* 图表 */}
      <div className="bg-slate-700/30 rounded-lg p-4 min-h-[400px]">
        <svg width={chartWidth} height={chartHeight + 30} className="w-full h-auto max-w-full">
          {/* 网格线 */}
          {gridLines}
          
          {/* 数据线 */}
          {pathD && (
            <path
              d={pathD}
              fill="none"
              stroke="#0EA5E9"
              strokeWidth="3"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          )}
          
          {/* 数据点 */}
          {pathPoints.map((point, index) => (
            <g key={index}>
              <circle
                cx={point.x}
                cy={point.y}
                r="6"
                fill="#0EA5E9"
                stroke="#1E293B"
                strokeWidth="2"
              />
              {/* 悬停提示 */}
              <title>
                {point.record.date}: {point.record.weight.toFixed(1)}kg
                {point.record.note && ` (${point.record.note})`}
              </title>
            </g>
          ))}
          
          {/* 坐标轴 */}
          <line
            x1={padding}
            y1={chartHeight - padding}
            x2={chartWidth - padding}
            y2={chartHeight - padding}
            stroke="#6B7280"
            strokeWidth="2"
          />
          <line
            x1={padding}
            y1={padding}
            x2={padding}
            y2={chartHeight - padding}
            stroke="#6B7280"
            strokeWidth="2"
          />
        </svg>
      </div>
      
      <div className="mt-4 text-center">
        <p className="text-slate-400 text-sm">
          显示最近30天的体重变化趋势 • 点击数据点查看详情
        </p>
      </div>
    </div>
  );
};

export default WeightChart;
