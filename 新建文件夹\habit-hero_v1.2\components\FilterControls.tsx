
import React from 'react';
import { Rarity } from '../types';

interface FilterControlsProps {
  selectedRarity: Rarity | 'All';
  onRarityChange: (rarity: Rarity | 'All') => void;
}

const FilterControls: React.FC<FilterControlsProps> = ({ selectedRarity, onRarityChange }) => {
  const rarities: (Rarity | 'All')[] = ['All', ...Object.values(Rarity)];

  return (
    <div className="mb-6 flex flex-wrap gap-2 justify-center">
      {rarities.map(rarity => (
        <button
          key={rarity}
          onClick={() => onRarityChange(rarity)}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-colors
            ${selectedRarity === rarity 
              ? 'bg-sky-500 text-white shadow-md' 
              : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
            }`}
        >
          {rarity === 'All' ? '全部稀有度' : rarity}
        </button>
      ))}
    </div>
  );
};

export default FilterControls;
