
import React from 'react';
import { Rarity, Card, CollectedCardEntry } from '../types';
import { MAX_UNIQUE_CARDS_PER_RARITY, CARD_POOL, RARITY_COLORS } from '../constants';

interface CollectionProgressProps {
  collectedCards: Record<string, CollectedCardEntry>;
}

const CollectionProgress: React.FC<CollectionProgressProps> = ({ collectedCards }) => {
  const progressData = Object.values(Rarity).map(rarity => {
    const totalInRarity = MAX_UNIQUE_CARDS_PER_RARITY[rarity];
    const collectedInRarity = CARD_POOL.filter(card => 
        card.rarity === rarity && collectedCards[card.id]
    ).length;
    return { rarity, collected: collectedInRarity, total: totalInRarity };
  });

  return (
    <div className="p-4 bg-slate-800 rounded-lg shadow-md mb-6">
      <h3 className="text-xl font-semibold text-sky-400 mb-3 text-center">卡片收集进度</h3>
      <div className="space-y-2">
        {progressData.map(item => (
          <div key={item.rarity}>
            <div className="flex justify-between text-sm mb-0.5">
              <span className={`font-medium ${RARITY_COLORS[item.rarity].split(' ')[0].replace('bg-','text-')}-400`}>{item.rarity}</span>
              <span className="text-slate-300">{item.collected} / {item.total}</span>
            </div>
            <div className={`w-full bg-slate-700 rounded-full h-2.5 overflow-hidden`}>
              <div
                className={`${RARITY_COLORS[item.rarity].split(' ')[0]} h-2.5 rounded-full transition-all duration-500 ease-out`}
                style={{ width: `${item.total > 0 ? (item.collected / item.total) * 100 : 0}%` }}
                role="progressbar"
                aria-valuenow={item.total > 0 ? (item.collected / item.total) * 100 : 0}
                aria-valuemin={0}
                aria-valuemax={100}
                aria-label={`${item.rarity} 卡片收集进度`}
              ></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default CollectionProgress;
