import React, { useState } from 'react';
import { ArmyCardData } from '../types';
import { generateContent } from '../services/geminiService';
import { Loader } from './Loader';
import { BattlefieldGrid } from './BattlefieldGrid';

interface PowerAssessmentProps {
  armyCards: ArmyCardData[];
  userPoints: number;
  onPointsChange: (points: number) => void;
}

interface FormationPosition {
  units: (number | string)[];
  position: [number, number];
  role: string;
}

interface Formation {
  name: string;
  description: string;
  positions: FormationPosition[];
}

interface PowerAssessmentResult {
  totalPower: number;
  powerUnit: string;
  breakdown: {
    unitName: string;
    individualPower: number;
    contribution: string;
    strengths: string[];
    weaknesses: string[];
  }[];
  teamSynergy: number;
  comboSkills: {
    name: string;
    description: string;
    participants: string[];
    powerBonus: number;
  }[];
  recommendations: string[];
  comparison: string;
  formation?: Formation;
  timestamp: string;
}

// 获取战力评估消耗积分的函数
const getAssessmentCost = (): number => {
  const saved = localStorage.getItem('app-settings');
  if (saved) {
    try {
      const settings = JSON.parse(saved);
      return settings.assessmentCost || 50; // 使用设置中的评估费用，默认50积分
    } catch {
      return 50;
    }
  }
  return 50;
};

export const PowerAssessment: React.FC<PowerAssessmentProps> = ({
  armyCards,
  userPoints,
  onPointsChange
}) => {
  const [isLoading, setIsLoading] = useState(false);
  const [assessment, setAssessment] = useState<PowerAssessmentResult | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isRegeneratingAssessment, setIsRegeneratingAssessment] = useState(false);
  const [isRegeneratingFormation, setIsRegeneratingFormation] = useState(false);

  // 分批处理相关状态
  const [useBatchProcessing, setUseBatchProcessing] = useState(false);
  const [batchProgress, setBatchProgress] = useState({ current: 0, total: 0, stage: '' });
  const [tokenUsage, setTokenUsage] = useState({ input: 0, output: 0, total: 0 });
  const [concurrentRequests, setConcurrentRequests] = useState(3); // 默认并发3个请求
  const [isExporting, setIsExporting] = useState(false);


  // 分批分析结果缓存
  const [batchAnalysisResults, setBatchAnalysisResults] = useState<any[] | null>(null);
  const [isGeneratingSummary, setIsGeneratingSummary] = useState(false);
  const [isGeneratingFormation, setIsGeneratingFormation] = useState(false);
  const [showBreakdownCount, setShowBreakdownCount] = useState(10); // 显示的个体分析数量

  const assessmentCost = getAssessmentCost();

  // 重新生成战力评估
  const handleRegenerateAssessment = async () => {
    if (!assessment) return;

    setIsRegeneratingAssessment(true);
    setError(null);

    try {
      const newAssessment = await generatePowerAssessment();
      if (newAssessment) {
        setAssessment(prev => ({
          ...newAssessment,
          formation: prev?.formation // 保留现有的阵型
        }));
      }
    } catch (error) {
      console.error('重新生成战力评估失败:', error);
      setError('重新生成战力评估失败，请稍后重试');
    } finally {
      setIsRegeneratingAssessment(false);
    }
  };

  // 重新生成阵型
  const handleRegenerateFormation = async () => {
    if (!assessment) return;

    setIsRegeneratingFormation(true);
    setError(null);

    try {
      const newFormation = await generateFormation();
      if (newFormation) {
        setAssessment(prev => prev ? {
          ...prev,
          formation: newFormation
        } : null);
      }
    } catch (error) {
      console.error('重新生成阵型失败:', error);
      setError('重新生成阵型失败，请稍后重试');
    } finally {
      setIsRegeneratingFormation(false);
    }
  };

  // 估算token使用量
  const estimateTokens = (text: string): number => {
    // 粗略估算：中文字符约1.5个token，英文单词约1个token
    const chineseChars = (text.match(/[\u4e00-\u9fff]/g) || []).length;
    const englishWords = (text.match(/[a-zA-Z]+/g) || []).length;
    return Math.ceil(chineseChars * 1.5 + englishWords);
  };

  // 提取阵营信息的辅助函数
  const extractFaction = (stylePrompt?: string): string => {
    if (!stylePrompt) return '默认阵营';
    // 提取括号前的部分作为阵营名称
    return stylePrompt.split('（')[0].split('(')[0].trim();
  };

  // 分批个体分析
  const analyzeBatch = async (batchUnits: any[], batchIndex: number, totalBatches: number) => {
    const unitsInfo = batchUnits.map(unit => ({
      name: unit.name,
      class: unit.characterClass,
      faction: extractFaction(unit.stylePrompt),
      rarity: unit.rarity.level,
      description: unit.description,
      skills: unit.skills,
      story: unit.story,
      attributes: unit.attributes
    }));

    // 构建同职业和同阵营的参考信息
    const buildReferenceInfo = () => {
      const sameClassUnits = unitsInfo.filter(u => u.class === unitsInfo[0].class);
      const sameFactionUnits = unitsInfo.filter(u => u.faction === unitsInfo[0].faction);

      let referenceInfo = '';

      if (sameClassUnits.length > 1) {
        referenceInfo += `\n同职业参考单位：\n${sameClassUnits.map(u =>
          `- ${u.name}：${u.description}${u.attributes ? `，属性：${JSON.stringify(u.attributes.baseAttributes || {})}` : ''}`
        ).join('\n')}`;
      }

      if (sameFactionUnits.length > 1) {
        referenceInfo += `\n同阵营参考单位：\n${sameFactionUnits.map(u =>
          `- ${u.name}(${u.class})：${u.description}${u.attributes ? `，属性：${JSON.stringify(u.attributes.baseAttributes || {})}` : ''}`
        ).join('\n')}`;
      }

      return referenceInfo;
    };

    const prompt = `
作为军事分析师，请对以下${unitsInfo.length}个单位进行个体战力分析：

${unitsInfo.map((unit, index) => `
${index + 1}. ${unit.name}
   - 职业：${unit.class}
   - 阵营：${unit.faction}
   - 稀有度：${unit.rarity}
   - 描述：${unit.description}
   - 技能：${unit.skills.map(skill => skill.name).join(', ')}
   - 背景：${unit.story}
   ${unit.attributes ? `- 现有属性：${JSON.stringify(unit.attributes.baseAttributes || {})}` : ''}
`).join('\n')}

${buildReferenceInfo()}

⚠️ 重要要求：
- 属性范围规定为1-10级，每个基础属性都必须在1-10之间
- 参考同职业和同阵营单位的属性分布，保持一致性
- 战力点数要合理反映稀有度、职业特点和属性等级

请按照以下格式进行评估，使用"战力点"作为标准单位（1000战力点 = 一个标准步兵连的战斗力）

⚠️ 重要：所有数值必须是计算后的最终数字，不要使用数学表达式或公式！

{
  "analyses": [
    {
      "unitName": "单位名称",
      "individualPower": 具体数字(如3500),
      "strengths": ["优势1", "优势2", "优势3"],
      "weaknesses": ["劣势1", "劣势2"],
      "description": "角色总体描述，包括职业特点、战斗风格、在团队中的作用等",
      "suggestedAttributes": {
        "attack": 数值(1-10),
        "defense": 数值(1-10),
        "health": 数值(1-10),
        "speed": 数值(1-10),
        "intelligence": 数值(1-10),
        "leadership": 数值(1-10)
      }
    }
  ]
}

要求：每个单位都必须分析，战力点数要合理反映稀有度和能力，属性建议必须在1-10级范围内。`;

    setBatchProgress({
      current: batchIndex + 1,
      total: totalBatches,
      stage: `分析第${batchIndex + 1}批单位 (${batchUnits.length}个)`
    });

    const maxRetries = 3;
    let lastError: any = null;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        console.log(`🔄 批次${batchIndex + 1}分析，尝试 ${attempt}/${maxRetries}`);

        const inputTokens = estimateTokens(prompt);
        const response = await generateContent(prompt, 600000); // 10分钟超时，给战力评估更多时间
        const outputTokens = estimateTokens(response);

        setTokenUsage(prev => ({
          input: prev.input + inputTokens,
          output: prev.output + outputTokens,
          total: prev.total + inputTokens + outputTokens
        }));

        // 提取JSON部分
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        let result;
        if (jsonMatch) {
          result = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in batch analysis response');
        }

        const analyses = result.analyses || [];

        // 验证解析结果
        if (!Array.isArray(analyses) || analyses.length === 0) {
          throw new Error('Invalid analysis format: not an array or empty');
        }

        // 验证每个分析项的完整性
        for (const analysis of analyses) {
          if (!analysis.unitName || !analysis.individualPower || !analysis.strengths || !analysis.weaknesses) {
            throw new Error('Incomplete analysis data');
          }
        }

        console.log(`✅ 批次${batchIndex + 1}分析成功，获得${analyses.length}个分析结果`);
        return analyses;

      } catch (error) {
        lastError = error;
        console.error(`❌ 批次${batchIndex + 1}分析失败 (尝试 ${attempt}/${maxRetries}):`, error);

        if (attempt < maxRetries) {
          const waitTime = attempt * 2000; // 2秒, 4秒
          console.log(`⏳ 等待 ${waitTime}ms 后重试批次${batchIndex + 1}...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
        }
      }
    }

    // 所有重试都失败后，生成默认分析
    console.error(`💥 批次${batchIndex + 1}分析彻底失败，使用默认分析。最后错误:`, lastError);
    return batchUnits.map(unit => ({
      unitName: unit.name,
      individualPower: 800 + Math.random() * 400,
      strengths: [`${unit.characterClass}职业优势`, "战斗经验丰富"],
      weaknesses: ["需要团队配合"],
      description: `${unit.characterClass}职业的核心战斗单位，具备该职业的典型特征和战斗能力`
    }));
  };

  // 并发处理批次的函数
  const processBatchesConcurrently = async (batches: any[][], concurrency: number) => {
    const results: any[] = [];
    const totalBatches = batches.length;
    let completedBatches = 0;

    // 创建并发处理的Promise池
    const processBatch = async (batchUnits: any[], batchIndex: number) => {
      const batchAnalyses = await analyzeBatch(batchUnits, batchIndex, totalBatches);
      completedBatches++;
      setBatchProgress({
        current: completedBatches,
        total: totalBatches,
        stage: `已完成 ${completedBatches}/${totalBatches} 批次`
      });
      return batchAnalyses;
    };

    // 分组处理，每次最多处理concurrency个批次
    for (let i = 0; i < batches.length; i += concurrency) {
      const currentBatches = batches.slice(i, i + concurrency);
      const promises = currentBatches.map((batch, index) =>
        processBatch(batch, i + index)
      );

      const batchResults = await Promise.all(promises);
      console.log(`🔍 批次 ${i}-${i + concurrency - 1} 结果:`, batchResults.map(batch => `${batch.length}个分析`));
      results.push(...batchResults.flat());
    }

    console.log(`🔍 所有批次处理完成，总分析结果: ${results.length}个`);
    console.log(`🔍 分析结果角色名: ${results.map(r => r.unitName).slice(0, 10).join(', ')}${results.length > 10 ? '...' : ''}`);
    return results;
  };

  // 按职业或阵营分组的函数
  const groupCardsByClassOrFaction = (cards: ArmyCardData[]) => {
    const groups = new Map<string, ArmyCardData[]>();

    cards.forEach(card => {
      const faction = extractFaction(card.stylePrompt);
      const characterClass = card.characterClass;

      // 尝试找到同职业或同阵营的组
      let groupKey = '';
      let found = false;

      // 首先查找同职业同阵营的组
      const exactKey = `${characterClass}-${faction}`;
      if (groups.has(exactKey)) {
        groupKey = exactKey;
        found = true;
      } else {
        // 查找同职业的组
        for (const [key, _] of groups) {
          if (key.startsWith(`${characterClass}-`)) {
            groupKey = key;
            found = true;
            break;
          }
        }

        // 如果没找到同职业的，查找同阵营的组
        if (!found) {
          for (const [key, _] of groups) {
            if (key.endsWith(`-${faction}`)) {
              groupKey = key;
              found = true;
              break;
            }
          }
        }

        // 如果都没找到，创建新组
        if (!found) {
          groupKey = exactKey;
        }
      }

      if (!groups.has(groupKey)) {
        groups.set(groupKey, []);
      }
      groups.get(groupKey)!.push(card);
    });

    return Array.from(groups.values());
  };

  // 第一阶段：分批个体分析
  const generateBatchAnalysis = async () => {
    // 按职业或阵营分组，而不是简单的数量分组
    const batches = groupCardsByClassOrFaction(armyCards);
    const totalBatches = batches.length;

    // 重置token统计
    setTokenUsage({ input: 0, output: 0, total: 0 });

    // 第一阶段：并发分批个体分析
    setBatchProgress({ current: 0, total: totalBatches, stage: '开始按职业/阵营分组分析...' });
    const allAnalyses = await processBatchesConcurrently(batches, concurrentRequests);

    // 缓存分析结果
    setBatchAnalysisResults(allAnalyses);
    setBatchProgress({ current: totalBatches, total: totalBatches, stage: '个体分析完成！' });

    return allAnalyses;
  };

  // 第二阶段：基于缓存结果生成汇总
  const generateSummaryFromCache = async () => {
    if (!batchAnalysisResults) {
      throw new Error('没有缓存的分析结果');
    }

    setIsGeneratingSummary(true);
    try {
      return await generateFinalSummary(batchAnalysisResults);
    } finally {
      setIsGeneratingSummary(false);
    }
  };

  // 汇总分析函数
  const generateFinalSummary = async (allAnalyses: any[]) => {
    const synthesisPrompt = `
作为一位资深的军事战略分析师，请基于以下${allAnalyses.length}个单位的个体分析，进行军队整体战力评估：

个体分析结果：
${allAnalyses.map((analysis, index) => `
${index + 1}. ${analysis.unitName} (${analysis.individualPower}战力点)
   - 描述：${analysis.description || '战斗单位'}
   - 优势：${analysis.strengths.join('、')}
   - 劣势：${analysis.weaknesses.join('、')}
`).join('\n')}

${campName && flagUrl ? `
军队信息：
- 军队名称：${campName}
- 拥有统一旗帜：是（统一的旗帜极大缓解内部矛盾，一致对外，增强团结度和士气）
` : `
军队信息：
- 军队名称：未命名军队
- 拥有统一旗帜：否（缺乏统一标识，可能影响军队凝聚力）
`}

请按照以下格式进行评估，使用"战力点"作为标准单位（1000战力点 = 一个标准步兵连的战斗力）：

1. 总体战力：基于个体分析计算总战力点数值
2. 团队协同：评估单位间的配合效果（0-100分）
3. 联合技能：分析团队中某些单位组合可能产生的特殊联合效果
4. 优势与劣势：分析整体军队的强项和弱点
5. 战术建议：提供3-5条改进建议
6. 实力对比：与历史著名军队或虚构军队进行对比，用下标表示对应位置角色。

请用JSON格式回复，除了json不要有多余输出！，包含以下字段：
{
  "totalPower": 数值,
  "powerUnit": "战力点",
  "teamSynergy": 数值,
  "comboSkills": [
    {
      "name": "联合技能名称",
      "description": "技能描述和效果",
      "participants": ["角色名1", "角色名2"],
      "powerBonus": 额外战力加成数值
    }
  ],
  "recommendations": ["建议1", "建议2", "建议3", "建议4", "建议5"],
  "comparison": "与历史著名军队或虚构军队进行详细对比描述"
}`;

    // 记录最终合并阶段统计
    const promptCharCount = synthesisPrompt.length;
    const inputTokens = estimateTokens(synthesisPrompt);

    console.log('🔍 最终合并阶段统计：');
    console.log(`📝 Prompt总字符数: ${promptCharCount.toLocaleString()}`);
    console.log(`🎯 估算输入Token数: ${inputTokens.toLocaleString()}`);
    console.log(`📦 分析结果数量: ${allAnalyses.length}`);
    console.log(`👥 总角色数: ${armyCards.length}`);
    console.log('🔍 分析结果详情:', allAnalyses.map(a => a.unitName).join(', '));

    const synthesisResponse = await generateContent(synthesisPrompt, 600000); // 10分钟超时
    const outputTokens = estimateTokens(synthesisResponse);

    // 记录响应统计
    const responseCharCount = synthesisResponse.length;

    setTokenUsage(prev => ({
      input: prev.input + inputTokens,
      output: prev.output + outputTokens,
      total: prev.total + inputTokens + outputTokens
    }));

    try {
      console.log('🔍 汇总响应内容:', synthesisResponse);

      // 提取JSON部分（与单次生成保持一致）
      const jsonMatch = synthesisResponse.match(/\{[\s\S]*\}/);
      let result;
      if (jsonMatch) {
        console.log('🔍 提取的JSON字符串:', jsonMatch[0]);

        // 尝试修复常见的JSON格式问题
        let cleanedJson = jsonMatch[0]
          .replace(/,(\s*[}\]])/g, '$1') // 移除多余的逗号
          .replace(/([{,]\s*)(\w+):/g, '$1"$2":') // 为属性名添加引号
          .replace(/:\s*([^",\[\]{}\s]+)(?=\s*[,}])/g, ':"$1"') // 为字符串值添加引号
          .replace(/:"(\d+)"/g, ':$1') // 恢复数字
          .replace(/:"(true|false|null)"/g, ':$1'); // 恢复布尔值和null

        console.log('🔍 清理后的JSON字符串:', cleanedJson);
        result = JSON.parse(cleanedJson);
      } else {
        throw new Error('No JSON found in synthesis response');
      }

      // 直接使用之前生成的个体分析结果
      result.breakdown = allAnalyses.map(analysis => ({
        unitName: analysis.unitName,
        individualPower: analysis.individualPower,
        contribution: analysis.description || analysis.specialAbilities || `${analysis.unitName}的战斗贡献`,
        strengths: analysis.strengths,
        weaknesses: analysis.weaknesses
      }));
      return result;
    } catch (error) {
      console.error('❌ 汇总结果解析失败:', error);
      console.error('❌ 原始响应:', synthesisResponse);
      // 返回基于个体分析的汇总结果
      return {
        totalPower: allAnalyses.reduce((sum, a) => sum + a.individualPower, 0),
        powerUnit: "战力点",
        breakdown: allAnalyses.map(analysis => ({
          unitName: analysis.unitName,
          individualPower: analysis.individualPower,
          contribution: analysis.description || analysis.specialAbilities || `${analysis.unitName}的战斗贡献`,
          strengths: analysis.strengths,
          weaknesses: analysis.weaknesses
        })),
        teamSynergy: 75,
        comboSkills: [],
        recommendations: ["加强团队协作训练", "优化阵型配置", "提升整体战术素养"],
        comparison: "这支军队展现出了良好的整体实力和发展潜力。"
      };
    }
  };

  // 生成战场阵型
  const generateFormation = async () => {
    if (!assessment) {
      setError('请先完成战力评估');
      return;
    }

    setIsGeneratingFormation(true);
    try {
      // 按职业排序，相同职业的单位聚集在一起，便于批量分配
      const sortedCards = [...armyCards].sort((a, b) => {
        // 首先按职业排序
        if (a.characterClass !== b.characterClass) {
          return a.characterClass.localeCompare(b.characterClass);
        }
        // 相同职业内按稀有度排序（传说 > 史诗 > 稀有 > 普通）
        const rarityOrder = { '传说': 4, '史诗': 3, '稀有': 2, '普通': 1 };
        const aOrder = rarityOrder[a.rarity.level as keyof typeof rarityOrder] || 0;
        const bOrder = rarityOrder[b.rarity.level as keyof typeof rarityOrder] || 0;
        return bOrder - aOrder;
      });

      const formationPrompt = `
作为一位资深的军事战术专家，请为以下${sortedCards.length}个单位设计最优战场阵型：

**重要：使用序号（如1, 5, "9-12"）来指代角色，不要使用角色名字！**
**提示：单位已按职业分组排序，相同职业的单位序号相邻，可以使用范围表示法（如"1-8"表示前8个战士）**

单位列表（按职业分组排序）：
${sortedCards.map((card, index) => `
${index + 1}. ${card.name} - ${card.characterClass} (${card.rarity.level})
   描述：${card.description}
   技能：${card.skills.map(skill => skill.name).join('、')}
`).join('')}

职业分布统计：
${Object.entries(
  sortedCards.reduce((acc, card) => {
    acc[card.characterClass] = (acc[card.characterClass] || 0) + 1;
    return acc;
  }, {} as Record<string, number>)
).map(([className, count]) => `- ${className}: ${count}个单位`).join('\n')}

请在20x20的战场网格中安排角色位置：
- 战场前方在右边（X=15-20），后方在左边（X=1-5）
- 坐标系：(1,1)在左下角，(20,20)在右上角
- 考虑职业特点。
- 角色不要重复，所有角色都要用到。
- 多分几队不要挤在一起，注重阵型安排。
请用JSON格式回复，包含以下字段：
{
  "name": "阵型名称",
  "description": "阵型说明",
  "positions": [
    {
      "units": [1, 4, 6, "9-12", 30],
      "position": [10, 10],
      "role": "前锋/后卫/侧翼等角色说明"
    }
  ]
}`;

      const response = await generateContent(formationPrompt, 300000); // 5分钟超时

      // 记录token消耗
      const inputTokens = estimateTokens(formationPrompt);
      const outputTokens = estimateTokens(response);

      // 解析JSON
      const jsonMatch = response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const formation = JSON.parse(jsonMatch[0]);

        // 更新assessment，添加formation
        setAssessment(prev => prev ? { ...prev, formation } : null);
      } else {
        throw new Error('无法解析阵型数据');
      }
    } catch (error) {
      console.error('生成阵型失败:', error);
      setError('生成阵型失败，请重试');
      setTimeout(() => setError(null), 5000);
    } finally {
      setIsGeneratingFormation(false);
    }
  };

  const canAfford = userPoints >= assessmentCost;

  const generatePowerAssessment = async () => {
    if (isLoading || !canAfford) return;
    
    if (armyCards.length === 0) {
      setError("没有卡牌可以评估！");
      setTimeout(() => setError(null), 3000);
      return;
    }

    setIsLoading(true);
    setError(null);
    setBatchProgress({ current: 0, total: 0, stage: '' });

    try {
      // 先扣除积分
      onPointsChange(userPoints - assessmentCost);

      let result;
      if (useBatchProcessing && armyCards.length > 50) {
        // 使用分批处理 - 只进行第一阶段分析
        await generateBatchAnalysis();
        return; // 不继续执行，等待用户点击汇总按钮
      } else {
        // 使用原有的单次处理
        setTokenUsage({ input: 0, output: 0, total: 0 });

      // 准备角色信息，包含阵营和属性信息
      const unitsInfo = armyCards.map(unit => ({
        name: unit.name,
        class: unit.characterClass,
        faction: extractFaction(unit.stylePrompt),
        rarity: unit.rarity.level,
        description: unit.description,
        skills: unit.skills,
        story: unit.story,
        attributes: unit.attributes
      }));

      // 构建同职业和同阵营的参考信息
      const buildGlobalReferenceInfo = () => {
        const classGroups = new Map<string, typeof unitsInfo>();
        const factionGroups = new Map<string, typeof unitsInfo>();

        unitsInfo.forEach(unit => {
          // 按职业分组
          if (!classGroups.has(unit.class)) {
            classGroups.set(unit.class, []);
          }
          classGroups.get(unit.class)!.push(unit);

          // 按阵营分组
          if (!factionGroups.has(unit.faction)) {
            factionGroups.set(unit.faction, []);
          }
          factionGroups.get(unit.faction)!.push(unit);
        });

        let referenceInfo = '\n=== 同职业/阵营参考信息 ===\n';

        // 职业参考信息
        for (const [className, units] of classGroups) {
          if (units.length > 1) {
            referenceInfo += `\n【${className}职业】参考单位：\n`;
            units.forEach(u => {
              referenceInfo += `- ${u.name}：${u.description}${u.attributes ? `，属性：${JSON.stringify(u.attributes.baseAttributes || {})}` : ''}\n`;
            });
          }
        }

        // 阵营参考信息
        for (const [factionName, units] of factionGroups) {
          if (units.length > 1) {
            referenceInfo += `\n【${factionName}阵营】参考单位：\n`;
            units.forEach(u => {
              referenceInfo += `- ${u.name}(${u.class})：${u.description}${u.attributes ? `，属性：${JSON.stringify(u.attributes.baseAttributes || {})}` : ''}\n`;
            });
          }
        }

        return referenceInfo;
      };

      const prompt = `
作为一位资深的军事战略分析师，请对以下军队进行详细的战力评估：

军队组成（共${unitsInfo.length}个单位）：
${unitsInfo.map((unit, index) => `
${index + 1}. ${unit.name}
   - 职业：${unit.class}
   - 阵营：${unit.faction}
   - 稀有度：${unit.rarity}
   - 描述：${unit.description}
   - 技能：${unit.skills.map((skill: any) => skill.name).join(', ')}
   - 背景：${unit.story}
   ${unit.attributes ? `- 现有属性：${JSON.stringify(unit.attributes.baseAttributes || {})}` : ''}
`).join('\n')}

${buildGlobalReferenceInfo()}

${campName && flagUrl ? `
营地信息：
- 营地名称：${campName}
- 拥有统一旗帜：是（统一的旗帜极大缓解内部矛盾，一致对外，增强团结度和士气）
` : `
营地信息：
- 营地名称：未命名营地
- 拥有统一旗帜：否（缺乏统一标识，可能影响军队凝聚力）
`}

⚠️ 重要要求：
- 必须对上述列出的所有${unitsInfo.length}个单位进行个体战力分析，一个都不能遗漏！
- 在breakdown数组中必须包含所有${unitsInfo.length}个单位的详细分析
- 属性范围规定为1-10级，每个基础属性都必须在1-10之间
- 参考同职业和同阵营单位的属性分布，保持一致性
- 不要偷懒只分析部分单位，每个单位都要有具体的战力点数值和分析

请按照以下格式进行评估，使用"战力点"作为标准单位（1000战力点 = 一个标准步兵连的战斗力）：

1. 总体战力：给出总战力点数值
2. 个体分析：每个单位的战力点和贡献分析（必须包含所有${unitsInfo.length}个单位）
3. 团队协同：评估单位间的配合效果（0-100分）
4. 联合技能：分析团队中某些单位组合可能产生的特殊联合效果
5. 优势与劣势：分析整体军队的强项和弱点
6. 战术建议：提供3-5条改进建议，阵型建议
7. 实力对比：与历史著名军队或虚构军队进行对比
8. 实力映射： 相当于现实中什么编制，多少人和装备，类比于哪个或哪些部队或国家，集团。

⚠️ 重要：所有数值必须是计算后的最终数字，不要使用数学表达式或公式！

请用JSON格式回复，包含以下字段：
{
  "totalPower": 具体数字(如15000),
  "powerUnit": "战力点",
  "breakdown": [
    // ⚠️ 这个数组必须包含所有${unitsInfo.length}个单位，每个单位一个对象！
    {
      "unitName": "单位名称",
      "individualPower": 具体数字(如3500),
      "contribution": "贡献描述",
      "strengths": ["优势1", "优势2"],
      "weaknesses": ["劣势1", "劣势2"],
      "suggestedAttributes": {
        "attack": 数值(1-10),
        "defense": 数值(1-10),
        "health": 数值(1-10),
        "speed": 数值(1-10),
        "intelligence": 数值(1-10),
        "leadership": 数值(1-10)
      }
    }
    // ... 重复${unitsInfo.length}次，每个单位都要有！
  ],
  "teamSynergy": 数值(0-100),
  "comboSkills": [
    {
      "name": "联合技能名称",
      "description": "技能描述和效果",
      "participants": ["参与单位1", "参与单位2"],
      "powerBonus": 额外战力加成数值
    }
  ],
  "recommendations": ["建议1", "建议2", "建议3"],
  "comparison": "与其他军队的对比描述"
}

再次强调：breakdown数组的长度必须等于${unitsInfo.length}！属性建议必须在1-10级范围内！
`;

      const response = await generateContent(prompt, 600000); // 10分钟超时
      
      // 尝试解析JSON响应
      let parsedResult: any;
      try {
        // 提取JSON部分
        const jsonMatch = response.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
          parsedResult = JSON.parse(jsonMatch[0]);
        } else {
          throw new Error('No JSON found in response');
        }
      } catch (parseError) {
        console.error('Failed to parse AI response as JSON:', parseError);
        // 如果解析失败，创建一个基础的评估结果
        parsedResult = {
          totalPower: armyCards.length * 1000 + Math.random() * 2000,
          powerUnit: "战力点",
          breakdown: armyCards.map(unit => ({
            unitName: unit.name,
            individualPower: 800 + Math.random() * 400,
            contribution: `${unit.characterClass}职业的核心战斗单位`,
            strengths: [unit.skills[0]?.name || "基础战斗技能"],
            weaknesses: ["需要更多实战经验"]
          })),
          teamSynergy: 60 + Math.random() * 30,
          comboSkills: [
            {
              name: "基础协同",
              description: "团队成员间的基本配合，提升整体作战效率",
              participants: armyCards.slice(0, 2).map(unit => unit.name),
              powerBonus: 200
            }
          ],
          recommendations: ["加强团队训练", "优化装备配置", "提升战术协调"],
          comparison: "相当于一支训练有素的精英小队，具备良好的战斗潜力。"
        };
      }

      const inputTokens = estimateTokens(prompt);
      const outputTokens = estimateTokens(response);
      setTokenUsage({
        input: inputTokens,
        output: outputTokens,
        total: inputTokens + outputTokens
      });

      result = {
        ...parsedResult,
        timestamp: new Date().toLocaleString('zh-CN')
      };
      }

      setAssessment(result);
      
    } catch (error) {
      console.error('Power assessment failed:', error);
      setError("战力评估失败，请重试。");
      // 退还积分
      onPointsChange(userPoints);
    } finally {
      setIsLoading(false);
    }
  };

  const exportToHTML = async () => {
    if (!assessment) return;

    setIsExporting(true);

    try {
      // 生成HTML内容
      const htmlContent = generateHTMLReport(assessment, armyCards);

      // 创建Blob并下载
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `战力评估报告_${new Date().toISOString().slice(0, 10)}.html`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
    } catch (error) {
      console.error('导出HTML失败:', error);
      setError('导出失败，请重试。');
      setTimeout(() => setError(null), 3000);
    } finally {
      setIsExporting(false);
    }
  };

  const generateHTMLReport = (assessment: PowerAssessmentResult, units: ArmyCardData[]): string => {
    const unitImageMap = units.reduce((acc, unit) => {
      acc[unit.name] = unit.imageUrl || '';
      return acc;
    }, {} as Record<string, string>);

    // 生成阵型网格HTML
    const generateFormationHTML = (formation: any) => {
      if (!formation) return '';

      const gridSize = 20;
      const positionMap = new Map();
      formation.positions.forEach((pos: any) => {
        const key = `${pos.position[0]},${pos.position[1]}`;
        positionMap.set(key, pos);
      });

      // 解析单位索引
      const parseUnitIndices = (units: (number | string)[]): number[] => {
        const result: number[] = [];
        units.forEach(unit => {
          if (typeof unit === 'number') {
            result.push(unit);
          } else if (typeof unit === 'string' && unit.includes('-')) {
            const [start, end] = unit.split('-').map(Number);
            for (let i = start; i <= end; i++) {
              result.push(i);
            }
          } else if (typeof unit === 'string') {
            const num = parseInt(unit);
            if (!isNaN(num)) {
              result.push(num);
            }
          }
        });
        return result;
      };

      let gridHTML = '<div class="formation-container">';
      gridHTML += `<h3>🏛️ ${formation.name}</h3>`;
      gridHTML += `<p>${formation.description}</p>`;
      gridHTML += '<div class="battlefield-grid">';

      // 生成网格
      for (let y = gridSize; y >= 1; y--) {
        for (let x = 1; x <= gridSize; x++) {
          const key = `${x},${y}`;
          const position = positionMap.get(key);
          const hasUnits = position ? parseUnitIndices(position.units).length > 0 : false;
          const unitCount = hasUnits ? parseUnitIndices(position.units).length : 0;

          gridHTML += `<div class="grid-cell ${hasUnits ? 'has-units' : ''}" title="${hasUnits ? `位置(${x},${y}): ${unitCount}个单位 - ${position.role}` : `位置(${x},${y})`}">`;
          if (hasUnits) {
            gridHTML += `<span class="unit-count">${unitCount}</span>`;
          }
          gridHTML += '</div>';
        }
      }

      gridHTML += '</div>';

      // 位置详情
      gridHTML += '<div class="formation-details">';
      gridHTML += '<h4>阵型详情：</h4>';
      formation.positions.forEach((pos: any) => {
        const unitIndices = parseUnitIndices(pos.units);
        gridHTML += `<div class="position-detail">`;
        gridHTML += `<strong>位置 (${pos.position[0]}, ${pos.position[1]}) - ${pos.role}</strong><br>`;
        gridHTML += `单位索引: ${pos.units.join(', ')}<br>`;
        gridHTML += `</div>`;
      });
      gridHTML += '</div>';
      gridHTML += '</div>';

      return gridHTML;
    };

    return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>军队战力评估报告</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
            color: #e2e8f0;
            line-height: 1.6;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: rgba(30, 41, 59, 0.9);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 2px solid #fbbf24;
        }

        .title {
            font-size: 2.5rem;
            font-weight: bold;
            background: linear-gradient(45deg, #fbbf24, #f59e0b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .subtitle {
            color: #94a3b8;
            font-size: 1.1rem;
        }

        .power-overview {
            background: linear-gradient(135deg, #fbbf24, #f59e0b);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 30px;
            color: #1e293b;
        }

        .power-value {
            font-size: 3rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .power-level {
            font-size: 1.5rem;
            font-weight: 600;
        }

        .section {
            background: rgba(51, 65, 85, 0.5);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .synergy-bar {
            background: #475569;
            height: 12px;
            border-radius: 6px;
            overflow: hidden;
            margin: 10px 0;
        }

        .synergy-fill {
            height: 100%;
            background: linear-gradient(90deg, #0ea5e9, #3b82f6);
            border-radius: 6px;
            transition: width 0.5s ease;
        }

        .unit-card {
            background: rgba(71, 85, 105, 0.5);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 20px;
            border: 1px solid rgba(148, 163, 184, 0.1);
        }

        .unit-image {
            width: 80px;
            height: 80px;
            border-radius: 10px;
            object-fit: cover;
            border: 2px solid #fbbf24;
        }

        .unit-info {
            flex: 1;
        }

        .unit-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #fbbf24;
            margin-bottom: 5px;
        }

        .unit-power {
            font-size: 1.1rem;
            color: #10b981;
            font-weight: 600;
            margin-bottom: 10px;
        }

        .unit-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 10px;
        }

        .strengths, .weaknesses {
            padding: 10px;
            border-radius: 8px;
        }

        .strengths {
            background: rgba(16, 185, 129, 0.1);
            border: 1px solid rgba(16, 185, 129, 0.3);
        }

        .weaknesses {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
        }

        .combo-skill {
            background: rgba(251, 191, 36, 0.1);
            border: 1px solid rgba(251, 191, 36, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
        }

        .combo-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 10px;
        }

        .combo-name {
            font-size: 1.2rem;
            font-weight: bold;
            color: #fbbf24;
        }

        .combo-bonus {
            color: #f59e0b;
            font-weight: 600;
        }

        .participants {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-top: 10px;
        }

        .participant-tag {
            background: rgba(251, 191, 36, 0.2);
            color: #fbbf24;
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9rem;
        }

        .recommendations {
            list-style: none;
        }

        .recommendations li {
            background: rgba(139, 92, 246, 0.1);
            border: 1px solid rgba(139, 92, 246, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-bottom: 10px;
            position: relative;
            padding-left: 40px;
        }

        .recommendations li::before {
            content: counter(recommendation);
            counter-increment: recommendation;
            position: absolute;
            left: 15px;
            top: 15px;
            background: #8b5cf6;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.8rem;
            font-weight: bold;
        }

        .recommendations {
            counter-reset: recommendation;
        }

        /* 阵型样式 */
        .formation-container {
            background: rgba(251, 146, 60, 0.1);
            border: 1px solid rgba(251, 146, 60, 0.3);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
        }

        .battlefield-grid {
            display: grid;
            grid-template-columns: repeat(20, 1fr);
            gap: 1px;
            background: #374151;
            border: 2px solid #6b7280;
            border-radius: 8px;
            padding: 4px;
            margin: 15px 0;
            max-width: 600px;
        }

        .grid-cell {
            width: 20px;
            height: 20px;
            background: #1f2937;
            border: 1px solid #374151;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
        }

        .grid-cell.has-units {
            background: #3b82f6;
            border-color: #60a5fa;
        }

        .unit-count {
            color: white;
            font-size: 8px;
        }

        .formation-details {
            margin-top: 15px;
        }

        .position-detail {
            background: rgba(75, 85, 99, 0.3);
            border-radius: 6px;
            padding: 10px;
            margin: 8px 0;
            font-size: 0.9rem;
        }

        /* 折叠功能样式 */
        .collapsible-section {
            margin: 20px 0;
        }

        .collapsible-header {
            background: rgba(34, 197, 94, 0.1);
            border: 1px solid rgba(34, 197, 94, 0.3);
            border-radius: 8px;
            padding: 15px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }

        .collapsible-header:hover {
            background: rgba(34, 197, 94, 0.2);
        }

        .collapsible-content {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .collapsible-content.expanded {
            max-height: 2000px;
        }

        .toggle-btn {
            background: #22c55e;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
        }

        .toggle-btn:hover {
            background: #16a34a;
        }

        .page-controls {
            text-align: center;
            margin: 20px 0;
        }

        .page-btn {
            background: #6366f1;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 0 5px;
            border-radius: 6px;
            cursor: pointer;
        }

        .page-btn:hover {
            background: #4f46e5;
        }

        .page-btn:disabled {
            background: #6b7280;
            cursor: not-allowed;
        }

        .comparison {
            background: rgba(59, 130, 246, 0.1);
            border: 1px solid rgba(59, 130, 246, 0.3);
            border-radius: 10px;
            padding: 20px;
            font-style: italic;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid rgba(148, 163, 184, 0.2);
            color: #94a3b8;
        }

        @media print {
            body {
                background: white;
                color: black;
            }

            .container {
                background: white;
                box-shadow: none;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">⚔️ 军队战力评估报告</h1>
            <p class="subtitle">AI驱动的专业军事分析 • 生成时间: ${assessment.timestamp}</p>
        </div>

        <div class="power-overview">
            <div class="power-value">${assessment.totalPower.toLocaleString()}</div>
            <div class="power-level">${assessment.powerUnit} • ${getPowerLevel(assessment.totalPower).level}级军队</div>
        </div>

        <div class="section">
            <h2 class="section-title">🤝 团队协同效果</h2>
            <div style="display: flex; align-items: center; gap: 15px;">
                <div style="flex: 1;">
                    <div class="synergy-bar">
                        <div class="synergy-fill" style="width: ${assessment.teamSynergy}%"></div>
                    </div>
                </div>
                <span style="color: #0ea5e9; font-weight: bold; font-size: 1.1rem;">${assessment.teamSynergy}/100</span>
            </div>
        </div>

        <div class="collapsible-section">
            <div class="collapsible-header" onclick="toggleSection('breakdown')">
                <h2 class="section-title">👥 个体战力分析 (${assessment.breakdown.length}个单位)</h2>
                <button class="toggle-btn" id="breakdown-toggle">展开详情</button>
            </div>
            <div class="collapsible-content" id="breakdown-content">
                <div class="page-controls">
                    <button class="page-btn" onclick="changePage(-1)" id="prev-btn">上一页</button>
                    <span id="page-info">第 1 页，共 ${Math.ceil(assessment.breakdown.length / 10)} 页</span>
                    <button class="page-btn" onclick="changePage(1)" id="next-btn">下一页</button>
                </div>
                <div id="units-container">
                    ${assessment.breakdown.slice(0, 10).map(unit => `
                        <div class="unit-card">
                            ${unitImageMap[unit.unitName] ? `<img src="${unitImageMap[unit.unitName]}" alt="${unit.unitName}" class="unit-image" onerror="this.style.display='none'">` : ''}
                            <div class="unit-info">
                                <div class="unit-name">${unit.unitName}</div>
                                <div class="unit-power">${unit.individualPower.toLocaleString()} 战力点</div>
                                <p style="color: #cbd5e1; margin-bottom: 10px;">${unit.contribution}</p>
                                <div class="unit-details">
                                    <div class="strengths">
                                        <strong style="color: #10b981;">优势:</strong>
                                        <ul style="margin-top: 5px; padding-left: 15px;">
                                            ${unit.strengths.map(strength => `<li>${strength}</li>`).join('')}
                                        </ul>
                                    </div>
                                    <div class="weaknesses">
                                        <strong style="color: #ef4444;">劣势:</strong>
                                        <ul style="margin-top: 5px; padding-left: 15px;">
                                            ${unit.weaknesses.map(weakness => `<li>${weakness}</li>`).join('')}
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `).join('')}
                </div>
            </div>
        </div>

        ${assessment.comboSkills.length > 0 ? `
        <div class="section">
            <h2 class="section-title">⚡ 联合技能</h2>
            ${assessment.comboSkills.map(combo => `
                <div class="combo-skill">
                    <div class="combo-header">
                        <div class="combo-name">${combo.name}</div>
                        <div class="combo-bonus">+${combo.powerBonus.toLocaleString()} 战力点</div>
                    </div>
                    <p style="color: #cbd5e1; margin-bottom: 10px;">${combo.description}</p>
                    <div>
                        <strong style="color: #f59e0b;">参与单位:</strong>
                        <div class="participants">
                            ${combo.participants.map(participant => `<span class="participant-tag">${participant}</span>`).join('')}
                        </div>
                    </div>
                </div>
            `).join('')}
        </div>
        ` : ''}

        <div class="section">
            <h2 class="section-title">💡 战术建议</h2>
            <ol class="recommendations">
                ${assessment.recommendations.map(rec => `<li>${rec}</li>`).join('')}
            </ol>
        </div>

        <div class="section">
            <h2 class="section-title">📊 实力对比</h2>
            <div class="comparison">
                ${assessment.comparison}
            </div>
        </div>

        ${assessment.formation ? generateFormationHTML(assessment.formation) : ''}

        <div class="footer">
            <p>本报告由 Habit Hero AI 战力评估系统生成</p>
            <p>评估基于当前军队配置，实际战斗效果可能因战术运用而有所差异</p>
        </div>
    </div>

    <script>
        // 分页功能
        let currentPage = 1;
        const itemsPerPage = 10;
        const allUnits = ${JSON.stringify(assessment.breakdown)};
        const unitImageMap = ${JSON.stringify(unitImageMap)};

        function toggleSection(sectionId) {
            const content = document.getElementById(sectionId + '-content');
            const toggle = document.getElementById(sectionId + '-toggle');

            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                toggle.textContent = '展开详情';
            } else {
                content.classList.add('expanded');
                toggle.textContent = '收起详情';
            }
        }

        function changePage(direction) {
            const totalPages = Math.ceil(allUnits.length / itemsPerPage);
            currentPage += direction;

            if (currentPage < 1) currentPage = 1;
            if (currentPage > totalPages) currentPage = totalPages;

            updateUnitsDisplay();
            updatePageControls();
        }

        function updateUnitsDisplay() {
            const container = document.getElementById('units-container');
            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, allUnits.length);
            const pageUnits = allUnits.slice(startIndex, endIndex);

            container.innerHTML = pageUnits.map(unit => \`
                <div class="unit-card">
                    \${unitImageMap[unit.unitName] ? \`<img src="\${unitImageMap[unit.unitName]}" alt="\${unit.unitName}" class="unit-image" onerror="this.style.display='none'">\` : ''}
                    <div class="unit-info">
                        <div class="unit-name">\${unit.unitName}</div>
                        <div class="unit-power">\${unit.individualPower.toLocaleString()} 战力点</div>
                        <p style="color: #cbd5e1; margin-bottom: 10px;">\${unit.contribution}</p>
                        <div class="unit-details">
                            <div class="strengths">
                                <strong style="color: #10b981;">优势:</strong>
                                <ul style="margin-top: 5px; padding-left: 15px;">
                                    \${unit.strengths.map(strength => \`<li>\${strength}</li>\`).join('')}
                                </ul>
                            </div>
                            <div class="weaknesses">
                                <strong style="color: #ef4444;">劣势:</strong>
                                <ul style="margin-top: 5px; padding-left: 15px;">
                                    \${unit.weaknesses.map(weakness => \`<li>\${weakness}</li>\`).join('')}
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            \`).join('');
        }

        function updatePageControls() {
            const totalPages = Math.ceil(allUnits.length / itemsPerPage);
            document.getElementById('page-info').textContent = \`第 \${currentPage} 页，共 \${totalPages} 页\`;
            document.getElementById('prev-btn').disabled = currentPage === 1;
            document.getElementById('next-btn').disabled = currentPage === totalPages;
        }
    </script>
</body>
</html>`;
  };

  const getRarityColor = (rarity: string) => {
    const colors: Record<string, string> = {
      'Common': 'text-gray-400',
      'Uncommon': 'text-green-400',
      'Rare': 'text-blue-400',
      'Epic': 'text-purple-400',
      'Legendary': 'text-yellow-400'
    };
    return colors[rarity] || 'text-gray-400';
  };

  const getPowerLevel = (power: number) => {
    if (power < 2000) return { level: '新兵', color: 'text-gray-400' };
    if (power < 5000) return { level: '精锐', color: 'text-green-400' };
    if (power < 10000) return { level: '王牌', color: 'text-blue-400' };
    if (power < 20000) return { level: '传奇', color: 'text-purple-400' };
    return { level: '神话', color: 'text-yellow-400' };
  };

  return (
    <div className="bg-slate-800 rounded-xl p-6 border border-slate-700">
      <div className="flex items-center justify-between mb-6">
        <div>
          <h3 className="text-2xl font-bold text-amber-400 mb-2">⚔️ 战力评估系统</h3>
          <p className="text-slate-400">AI驱动的军队战力分析</p>
        </div>
        <div className="text-right">
          <div className="text-sm text-slate-400">评估费用</div>
          <div className="text-lg font-bold text-amber-400">{assessmentCost} 积分</div>
        </div>
      </div>

      {/* 分批处理设置 */}
      {armyCards.length > 50 && (
        <div className="mb-4 p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
          <div className="flex items-center justify-between mb-2">
            <label className="flex items-center space-x-2 cursor-pointer">
              <input
                type="checkbox"
                checked={useBatchProcessing}
                onChange={(e) => setUseBatchProcessing(e.target.checked)}
                className="w-4 h-4 text-amber-600 bg-slate-600 border-slate-500 rounded focus:ring-amber-500"
              />
              <span className="text-sm font-medium text-slate-200">启用分批处理</span>
            </label>
            <span className="text-xs text-slate-400">推荐用于大型军队</span>
          </div>
          <p className="text-xs text-slate-400">
            💡 当前有 {armyCards.length} 张卡牌。分批处理可以避免请求过大，提高成功率。
          </p>

          {/* 并发数量设置 */}
          {useBatchProcessing && (
            <div className="mt-3 pt-3 border-t border-slate-600/30">
              <div className="flex items-center justify-between mb-2">
                <label className="text-sm text-slate-300">并发请求数量:</label>
                <div className="flex items-center space-x-2">
                  <input
                    type="number"
                    min="1"
                    max="10"
                    value={concurrentRequests}
                    onChange={(e) => setConcurrentRequests(Math.max(1, Math.min(10, parseInt(e.target.value) || 1)))}
                    className="w-16 px-2 py-1 text-sm bg-slate-600 text-white rounded border border-slate-500 focus:outline-none focus:ring-2 focus:ring-amber-500"
                  />
                  <span className="text-xs text-slate-400">个</span>
                </div>
              </div>
              <p className="text-xs text-slate-500">
                ⚡ 同时发起多个请求可以加快处理速度，建议设置为 2-5 个
              </p>
            </div>
          )}
        </div>
      )}

      {/* Token使用统计 */}
      {(tokenUsage.total > 0 || isLoading) && (
        <div className="mb-4 p-3 bg-slate-700/20 rounded-lg border border-slate-600/30">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-slate-300">Token 使用统计</span>
            <span className="text-xs text-slate-400">实时消耗</span>
          </div>
          <div className="grid grid-cols-3 gap-2 text-xs">
            <div className="text-center">
              <div className="text-blue-400 font-medium">{tokenUsage.input.toLocaleString()}</div>
              <div className="text-slate-500">输入</div>
            </div>
            <div className="text-center">
              <div className="text-green-400 font-medium">{tokenUsage.output.toLocaleString()}</div>
              <div className="text-slate-500">输出</div>
            </div>
            <div className="text-center">
              <div className="text-amber-400 font-medium">{tokenUsage.total.toLocaleString()}</div>
              <div className="text-slate-500">总计</div>
            </div>
          </div>
        </div>
      )}

      {/* 分批进度显示 */}
      {isLoading && batchProgress.total > 0 && (
        <div className="mb-4 p-3 bg-slate-700/20 rounded-lg border border-amber-500/30">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-amber-300">分批处理进度</span>
            <span className="text-xs text-slate-400">{batchProgress.current}/{batchProgress.total}</span>
          </div>
          <div className="w-full bg-slate-600 rounded-full h-2 mb-2">
            <div
              className="bg-gradient-to-r from-amber-500 to-orange-500 h-2 rounded-full transition-all duration-300"
              style={{ width: `${(batchProgress.current / batchProgress.total) * 100}%` }}
            />
          </div>
          <div className="text-xs text-slate-400">{batchProgress.stage}</div>
        </div>
      )}

      {/* 评估按钮 */}
      <div className="mb-6">
        <button
          onClick={generatePowerAssessment}
          disabled={!canAfford || isLoading || armyCards.length === 0}
          className={`w-full py-4 px-6 rounded-lg font-bold text-lg transition-all duration-300 ${
            canAfford && !isLoading && armyCards.length > 0
              ? 'bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white shadow-lg hover:shadow-xl transform hover:scale-105'
              : 'bg-slate-600 text-slate-400 cursor-not-allowed'
          }`}
        >
          {isLoading ? (
            <div className="flex items-center justify-center space-x-2">
              <Loader />
              <span>AI正在分析战力...</span>
            </div>
          ) : (
            '🔍 开始战力评估'
          )}
        </button>
        
        {!canAfford && (
          <p className="text-red-400 text-sm mt-2 text-center">
            积分不足！还需要 {assessmentCost - userPoints} 积分
          </p>
        )}
        
        {armyCards.length === 0 && (
          <p className="text-yellow-400 text-sm mt-2 text-center">
            没有卡牌可以评估
          </p>
        )}
      </div>

      {/* 分批分析结果和汇总按钮 */}
      {batchAnalysisResults && !assessment && (
        <div className="bg-green-900/30 rounded-lg p-4 border border-green-500/30">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h4 className="text-lg font-bold text-green-400">✅ 个体分析完成</h4>
              <p className="text-green-300 text-sm">
                已完成 {batchAnalysisResults.length} 个角色的个体分析，结果已缓存
              </p>
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => {
                  setBatchAnalysisResults(null);
                  setBatchProgress({ current: 0, total: 0, stage: '' });
                }}
                className="px-3 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors text-sm"
              >
                🗑️ 清除缓存
              </button>
              <button
                onClick={async () => {
                  try {
                    const result = await generateSummaryFromCache();
                    setAssessment({
                      ...result,
                      timestamp: new Date().toLocaleString()
                    });
                    // 清除缓存
                    setBatchAnalysisResults(null);
                  } catch (error) {
                    console.error('汇总分析失败:', error);
                    setError('汇总分析失败，请重试');
                    setTimeout(() => setError(null), 5000);
                  }
                }}
                disabled={isGeneratingSummary}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium"
              >
                {isGeneratingSummary ? '汇总中...' : '🔄 生成最终汇总'}
              </button>
            </div>
          </div>

          {/* 显示个体分析预览 */}
          <div className="bg-slate-800 rounded p-3 max-h-64 overflow-y-auto">
            <h5 className="text-sm font-semibold text-slate-300 mb-2">个体分析预览：</h5>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-2">
              {batchAnalysisResults.slice(0, 12).map((analysis, index) => (
                <div key={index} className="bg-slate-700 p-2 rounded text-xs">
                  <div className="text-white font-medium">{analysis.unitName}</div>
                  <div className="text-slate-300">{analysis.individualPower} 战力点</div>
                  <div className="text-slate-400 truncate">{analysis.strengths?.[0] || '分析中...'}</div>
                </div>
              ))}
              {batchAnalysisResults.length > 12 && (
                <div className="bg-slate-700 p-2 rounded text-xs flex items-center justify-center text-slate-400">
                  还有 {batchAnalysisResults.length - 12} 个...
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {error && (
        <div className="bg-red-900/50 border border-red-500 rounded-lg p-4 mb-6">
          <p className="text-red-400">{error}</p>
        </div>
      )}

      {/* 评估结果 */}
      {assessment && (
        <div className="space-y-6">
          {/* 总体战力 */}
          <div className="bg-gradient-to-r from-amber-900/30 to-orange-900/30 rounded-lg p-6 border border-amber-500/30">
            <div className="text-center">
              <div className="flex items-center justify-center gap-4 mb-2">
                <h4 className="text-xl font-bold text-amber-400">总体战力评估</h4>
                <button
                  onClick={handleRegenerateAssessment}
                  disabled={isRegeneratingAssessment}
                  className="px-3 py-1 bg-amber-600 hover:bg-amber-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded text-sm transition-colors"
                  title="重新生成战力评估"
                >
                  {isRegeneratingAssessment ? '生成中...' : '🔄 重新生成'}
                </button>
              </div>
              <div className="text-4xl font-bold text-amber-300 mb-2">
                {assessment.totalPower.toLocaleString()} {assessment.powerUnit}
              </div>
              <div className={`text-lg font-semibold ${getPowerLevel(assessment.totalPower).color}`}>
                {getPowerLevel(assessment.totalPower).level}级军队
              </div>
              <div className="text-sm text-slate-400 mt-2">
                评估时间: {assessment.timestamp}
              </div>

              {/* 导出按钮 */}
              <div className="mt-4">
                <button
                  onClick={exportToHTML}
                  disabled={isExporting}
                  className="px-6 py-3 bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 disabled:from-slate-600 disabled:to-slate-600 text-white rounded-lg font-semibold transition-all duration-300 transform hover:scale-105 disabled:scale-100 disabled:cursor-not-allowed shadow-lg"
                >
                  {isExporting ? (
                    <div className="flex items-center space-x-2">
                      <Loader size="small" />
                      <span>导出中...</span>
                    </div>
                  ) : (
                    <div className="flex items-center space-x-2">
                      <span>📄</span>
                      <span>导出HTML报告</span>
                    </div>
                  )}
                </button>
              </div>
            </div>
          </div>

          {/* 团队协同 */}
          <div className="bg-slate-700/50 rounded-lg p-4">
            <h4 className="text-lg font-bold text-sky-400 mb-3">团队协同效果</h4>
            <div className="flex items-center space-x-4">
              <div className="flex-1 bg-slate-600 rounded-full h-3">
                <div
                  className="bg-gradient-to-r from-sky-500 to-blue-500 h-3 rounded-full transition-all duration-1000"
                  style={{ width: `${assessment.teamSynergy}%` }}
                />
              </div>
              <span className="text-sky-400 font-bold">{assessment.teamSynergy}/100</span>
            </div>
          </div>

          {/* 联合技能 */}
          <div className="bg-gradient-to-r from-orange-900/30 to-red-900/30 rounded-lg p-4 border border-orange-500/30">
            <h4 className="text-lg font-bold text-orange-400 mb-4">⚡ 联合技能</h4>
            {assessment.comboSkills.length > 0 ? (
              <div className="space-y-4">
                {assessment.comboSkills.map((combo, index) => (
                  <div key={index} className="bg-slate-700/50 rounded-lg p-4">
                    <div className="flex justify-between items-start mb-2">
                      <h5 className="font-bold text-orange-300">{combo.name}</h5>
                      <span className="text-orange-400 font-bold text-sm">
                        +{combo.powerBonus.toLocaleString()} 战力点
                      </span>
                    </div>
                    <p className="text-slate-300 text-sm mb-3">{combo.description}</p>
                    <div className="flex flex-wrap gap-2">
                      <span className="text-orange-400 text-sm font-semibold">参与单位:</span>
                      {combo.participants.map((participant, i) => (
                        <span key={i} className="bg-orange-800/30 px-2 py-1 rounded text-orange-200 text-sm">
                          {participant}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <p className="text-slate-400 text-center py-4">暂未发现特殊联合技能</p>
            )}
          </div>

          {/* 个体分析（可折叠） */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <h4 className="text-lg font-bold text-green-400">👥 个体战力分析</h4>
              <div className="flex items-center space-x-2">
                <span className="text-slate-400 text-sm">
                  显示 {Math.min(showBreakdownCount, assessment.breakdown.length)} / {assessment.breakdown.length}
                </span>
                {assessment.breakdown.length > showBreakdownCount && (
                  <button
                    onClick={() => setShowBreakdownCount(prev => Math.min(prev + 10, assessment.breakdown.length))}
                    className="px-3 py-1 bg-green-600 hover:bg-green-700 text-white rounded text-sm transition-colors"
                  >
                    显示更多 (+10)
                  </button>
                )}
                {showBreakdownCount > 10 && (
                  <button
                    onClick={() => setShowBreakdownCount(10)}
                    className="px-3 py-1 bg-gray-600 hover:bg-gray-700 text-white rounded text-sm transition-colors"
                  >
                    收起
                  </button>
                )}
              </div>
            </div>
            <div className="grid gap-4 max-h-96 overflow-y-auto">
              {assessment.breakdown.slice(0, showBreakdownCount).map((unit, index) => (
                <div key={index} className="bg-slate-700/50 rounded-lg p-4">
                  <div className="flex justify-between items-start mb-3">
                    <h5 className="font-bold text-white">{unit.unitName}</h5>
                    <span className="text-green-400 font-bold">
                      {unit.individualPower.toLocaleString()} 战力点
                    </span>
                  </div>
                  <p className="text-slate-300 text-sm mb-3">{unit.contribution}</p>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-3 text-sm">
                    <div>
                      <span className="text-green-400 font-semibold">优势:</span>
                      <ul className="text-slate-300 ml-2">
                        {unit.strengths.map((strength, i) => (
                          <li key={i}>• {strength}</li>
                        ))}
                      </ul>
                    </div>
                    <div>
                      <span className="text-red-400 font-semibold">劣势:</span>
                      <ul className="text-slate-300 ml-2">
                        {unit.weaknesses.map((weakness, i) => (
                          <li key={i}>• {weakness}</li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* 战术建议 */}
          <div className="bg-purple-900/30 rounded-lg p-4 border border-purple-500/30">
            <h4 className="text-lg font-bold text-purple-400 mb-3">💡 战术建议</h4>
            <ul className="space-y-2">
              {assessment.recommendations.map((rec, index) => (
                <li key={index} className="text-slate-300 flex items-start">
                  <span className="text-purple-400 mr-2">{index + 1}.</span>
                  {rec}
                </li>
              ))}
            </ul>
          </div>

          {/* 实力对比 */}
          <div className="bg-blue-900/30 rounded-lg p-4 border border-blue-500/30">
            <h4 className="text-lg font-bold text-blue-400 mb-3">📊 实力对比</h4>
            <p className="text-slate-300">{assessment.comparison}</p>
          </div>

          {/* 战场阵型生成 */}
          <div className="bg-orange-900/30 rounded-lg p-4 border border-orange-500/30">
            <div className="flex items-center justify-between mb-3">
              <h4 className="text-lg font-bold text-orange-400">🏛️ 战场阵型</h4>
              <div className="flex gap-2">
                {!assessment.formation && (
                  <button
                    onClick={generateFormation}
                    disabled={isGeneratingFormation}
                    className="px-4 py-2 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors font-medium"
                  >
                    {isGeneratingFormation ? '生成中...' : '🎯 生成推荐阵型'}
                  </button>
                )}
                {assessment.formation && (
                  <button
                    onClick={handleRegenerateFormation}
                    disabled={isRegeneratingFormation}
                    className="px-3 py-1 bg-orange-600 hover:bg-orange-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded text-sm transition-colors"
                    title="重新生成阵型"
                  >
                    {isRegeneratingFormation ? '生成中...' : '🔄 重新生成'}
                  </button>
                )}
              </div>
            </div>

            {assessment.formation ? (
              <BattlefieldGrid
                formation={assessment.formation}
                armyCards={armyCards}
              />
            ) : (
              <div className="text-center text-orange-300 py-8">
                <div className="text-4xl mb-2">🎯</div>
                <p>点击按钮生成专业的战场阵型推荐</p>
                <p className="text-sm text-orange-400 mt-1">基于角色职业和特点进行最优布局</p>
              </div>
            )}
          </div>


        </div>
      )}
    </div>
  );
};

export default PowerAssessment;
