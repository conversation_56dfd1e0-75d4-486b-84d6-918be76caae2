import { generateText, generateImage } from './unifiedApiService';

// 向后兼容的API - 现在使用统一API服务
export const generateContent = async (prompt: string, timeoutMs: number = 300000): Promise<string> => {
  return generateText(prompt, timeoutMs);
};

// 保留这些函数用于向后兼容，但它们现在通过API配置管理
export const updateApiKey = (newApiKey: string) => {
  console.warn('updateApiKey is deprecated. Please use API Config Manager instead.');
};

export const getCurrentModel = (): string => {
  console.warn('getCurrentModel is deprecated. Please use API Config Manager instead.');
  return 'gemini-2.5-flash';
};

export const setCurrentModel = (model: string): void => {
  console.warn('setCurrentModel is deprecated. Please use API Config Manager instead.');
};

// 获取可用模型列表 - 现在返回默认列表
export const getAvailableModels = async (): Promise<string[]> => {
  console.warn('getAvailableModels is deprecated. Please use API Config Manager instead.');
  // 返回一些常见的模型作为备选
  return [
    'gemini-2.5-flash',
    'gemini-1.5-flash',
    'gemini-1.5-pro',
    'gemini-pro',
    'gemini-2.0-flash',
    'gemini-2.5-flash-lite-preview-06-17'
  ];
};

// Army card types and interfaces
export enum ArmyRarityLevel {
  C = '普通',
  R = '稀有',
  Epic = '史诗',
  Legendary = '传说',
  Mythic = '神话',
}

export interface ArmyRarity {
  level: ArmyRarityLevel;
  color: string;
  glowColor: string;
  probability: number;
}

export interface ArmySkill {
  name: string;
  description: string;
}

export interface ArmyCardData {
  id: string;
  name: string;
  characterClass: string;
  description: string;
  rarity: ArmyRarity;
  imageUrl: string;
  skills: ArmySkill[];
  story: string;
}

export interface DeployedCard extends ArmyCardData {
  deploymentId: string;
  position: { top: string; left: string };
}

// Army rarities configuration
export const ARMY_RARITIES: ArmyRarity[] = [
  { level: ArmyRarityLevel.C, color: 'text-gray-400', glowColor: '#9CA3AF', probability: 0.50 },
  { level: ArmyRarityLevel.R, color: 'text-blue-400', glowColor: '#60A5FA', probability: 0.30 },
  { level: ArmyRarityLevel.Epic, color: 'text-purple-400', glowColor: '#A78BFA', probability: 0.15 },
  { level: ArmyRarityLevel.Legendary, color: 'text-orange-400', glowColor: '#FBBF24', probability: 0.04 },
  { level: ArmyRarityLevel.Mythic, color: 'text-yellow-300', glowColor: '#FDE047', probability: 0.01 },
];

// Character classes
export const CHARACTER_CLASSES: Record<string, string> = {
  '战士': 'Warrior',
  '弓箭手': 'Archer',
  '攻城器械': 'Siege Engine',
  '指挥官': 'Commander',
  '盾兵': 'Shield Bearer',
  '暗法师': 'Dark Mage',
  '召唤师': 'Summoner',
  '工程师': 'Engineer',
  '枪兵': 'Lancer',
  '巨人': 'Giant',
  '骑兵': 'Cavalry',
  '领主': 'Lord',
  '国王': 'King',
  '火枪手': 'Musketeer',
  '特殊武器': 'Special Weapon',
  '堡垒': 'Fortress',
  '军乐队': 'Military Band',
  '近卫队': 'Royal Guard',
  '空中单位': 'Air Unit',
  '防空单位': 'Anti-Air Unit',
  '舰船': 'Warship',
  '法师': 'Mage',
  '医师': 'Healer',
};

// Helper to select rarity based on probability
export const selectRarity = (): ArmyRarity => {
  let random = Math.random();
  let cumulativeProbability = 0;
  for (const rarity of ARMY_RARITIES) {
    cumulativeProbability += rarity.probability;
    if (random < cumulativeProbability) {
      return rarity;
    }
  }
  return ARMY_RARITIES[ARMY_RARITIES.length - 1];
};

// The concept now includes skills and a story
interface CardConcept {
  name_cn: string;
  description_cn: string;
  description_en: string;
  skills: ArmySkill[];
  story: string;
}

const rarityToEnglish = (rarity: ArmyRarityLevel): string => {
    switch(rarity) {
        case ArmyRarityLevel.C: return 'Common';
        case ArmyRarityLevel.R: return 'Rare';
        case ArmyRarityLevel.Epic: return 'Epic';
        case ArmyRarityLevel.Legendary: return 'Legendary';
        case ArmyRarityLevel.Mythic: return 'Mythic';
        default: return 'Common';
    }
}

const generateCardConcept = async (rarity: ArmyRarity, characterClass: string, stylePrompt?: string, existingCards?: ArmyCardData[], referenceImage?: string, customClass?: any): Promise<CardConcept> => {
  const englishClass = customClass ? customClass.name : (CHARACTER_CLASSES[characterClass] || 'Character');

  // 解析当前阵营
  const currentFaction = parseStylePrompt(stylePrompt);

  // 筛选相同阵营的现有角色
  const sameFactionCards = existingCards?.filter(card => {
    const cardFaction = card.stylePrompt || '默认风格';
    return cardFaction === currentFaction;
  }) || [];

  // 生成现有角色信息文本
  let existingCharactersInfo = '';
  if (sameFactionCards.length > 0) {
    existingCharactersInfo = `

当前${currentFaction}阵营已有以下角色：
${sameFactionCards.map(card => `- ${card.name} (${card.rarity.level}) - ${card.characterClass}`).join('\n')}

请注意：
1. 尽量避免生成相似的角色名字
2. 即使是相同职业，也要在特性、技能、背景故事上有所区分
3. 不同稀有度的角色应该有明显的实力差异
4. 角色设计要符合${currentFaction}阵营的整体风格`;
  }

  let skillPromptPart = '';
  switch (rarity.level) {
    case ArmyRarityLevel.C:
      skillPromptPart = '请为此角色生成1个普通技能。';
      break;
    case ArmyRarityLevel.R:
      skillPromptPart = '请为此角色生成2个技能。';
      break;
    case ArmyRarityLevel.Epic:
      skillPromptPart = '请为此角色生成3个非常强大的技能。';
      break;
    case ArmyRarityLevel.Legendary:
      skillPromptPart = '请为此角色生成3个传奇的、独特的技能。';
      break;
    case ArmyRarityLevel.Mythic:
      skillPromptPart = '请为此角色生成4个神话级的、改变游戏规则的技能。';
      break;
  }

  const customClassInfo = customClass ? `
    **自定义职业**: 这是一个自定义职业"${customClass.name}"，描述：${customClass.description || '无特殊描述'}。
    该职业属于${customClass.faction}阵营，请确保角色设计符合该阵营的特色和风格。
    作为自定义职业，角色应该具有独特性和该阵营的鲜明特征。` : '';

  const prompt = `
    为一个抽卡游戏生成一个中世纪奇幻角色概念。
    该角色的职业是"${characterClass}"，稀有度是"${rarity.level} (${rarityToEnglish(rarity.level)})"。
    ${customClassInfo}
    ${existingCharactersInfo}

    请遵循以下要求：
    1.  **艺术风格**: ${stylePrompt ? `必须严格遵循以下艺术风格: "${stylePrompt}"` : "采用经典的16位像素艺术风格(pixel art style)。"}${referenceImage ? `\n    **参考角色**: 用户提供了参考角色图片，请在生成英文描述时参考该角色的外观特征、服装设计、装备风格、姿态动作等具体元素。` : ''}
    2.  **技能**: ${skillPromptPart} 技能应与角色的职业和稀有度相匹配${customClass ? '，并体现自定义职业的独特性' : ''}。
    3.  **背景故事**: 生成一段引人入胜的中文背景故事，限定在150字以内${customClass ? '，要体现该自定义职业在' + customClass.faction + '阵营中的作用' : ''}。
    4.  **描述**: 提供一句史诗般的中文简短描述，以及一句详细的、用于生成图片的英文描述（应包含职业：${englishClass}并符合设定的艺术风格${referenceImage ? '，并模仿参考角色的外观特征、服装风格、装备设计等具体元素' : ''}）。
    ${customClass ? '**重要**: 作为自定义职业，角色必须体现出独特性和创新性，区别于标准职业。' : '（注意如果是攻城器械指的是现实中存在的种类）'}
    请以JSON格式返回，包含以下五个键：
    - "name_cn": 角色中文名 (string)
    - "description_cn": 角色中文短描述 (string)
    - "description_en": 用于图片生成的英文描述 (string)
    - "skills": 技能数组 (array of objects, with "name" and "description" string properties)
    - "story": 背景故事 (string)
  `;

  // 如果有参考角色，在prompt中添加更详细的说明
  let finalPrompt = prompt;
  if (referenceImage) {
    finalPrompt += `\n\n**重要提示**: 用户提供了参考角色图片。在生成英文描述时，请特别注意参考角色的：
    - 面部特征和表情神态
    - 发型、发色和头部装饰
    - 服装款式、颜色和材质
    - 武器装备的造型和细节
    - 身体姿态和动作特征
    - 整体的角色气质和风格
    请将这些具体的角色特征融入到英文描述中，生成一个在外观上相似但符合指定职业的新角色。`;
  }

  const maxRetries = 5;
  let lastError: any = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // 生成角色概念中...

      const response = await generateText(finalPrompt);

      if (!response) {
        throw new Error('Empty response from AI');
      }

      let jsonStr = response.trim();
      const fenceRegex = /^```(\w*)?\s*\n?(.*?)\n?\s*```$/s;
      const match = jsonStr.match(fenceRegex);
      if (match && match[2]) {
        jsonStr = match[2].trim();
      }

      const result = JSON.parse(jsonStr) as CardConcept;

      // 验证结果的完整性
      if (!result.name_cn || !result.description_cn || !result.description_en || !result.skills || !result.story) {
        throw new Error('Incomplete card concept data');
      }

      // 角色概念生成成功
      return result;

    } catch (error) {
      lastError = error;
      console.error(`❌ 角色概念生成失败 (尝试 ${attempt}/${maxRetries}):`, error);

      if (attempt < maxRetries) {
        // 等待一段时间后重试，每次等待时间递增
        const waitTime = attempt * 2000; // 2秒, 4秒, 6秒, 8秒
        // 等待重试中...
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  // 所有重试都失败后，返回默认值
  console.error(`💥 角色概念生成彻底失败，使用默认值。最后错误:`, lastError);
  return {
      name_cn: "像素骑士",
      description_cn: "一位用方块和决心铸就传奇的骑士。",
      description_en: `A noble ${englishClass} in shining armor, rendered in detailed 16-bit pixel art style. Medieval fantasy setting. ${stylePrompt || ''}`,
      skills: [
          { name: "方块斩", description: "用像素化的剑进行一次基础攻击。" }
      ],
      story: "在8位宇宙的边缘，这位骑士被创造出来守护一个被遗忘的王国。他的盔甲由最坚固的像素构成，他的意志如代码般坚定不移。"
  };
};

const generateCardImage = async (name: string, englishDescription: string, stylePrompt?: string, referenceImage?: string): Promise<string> => {
  const finalStylePrompt = stylePrompt ? `Art style: ${stylePrompt}.` : 'Detailed pixel art, 16-bit retro RPG video game style.';
  let prompt = `${finalStylePrompt} Full-body portrait of a medieval fantasy character. Character description: ${englishDescription}. Centered character. Vibrant medieval fantasy color palette. No text, watermarks, or anti-aliasing.`;

  // 如果有参考角色，添加参考角色说明
  if (referenceImage) {
    prompt += ' Reference the character design, clothing style, equipment details, and overall appearance of the provided character image.';
  }
  const maxRetries = 5;
  let lastError: any = null;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // 生成图片中...

      // 使用统一API生成图片
      const imageUrl = await generateImage(prompt);

      if (imageUrl) {
        // 图片生成成功
        return imageUrl;
      } else {
        console.warn(`⚠️ 图片生成无结果: ${name} (尝试 ${attempt}/${maxRetries})`);
        lastError = new Error('No images generated');
      }
    } catch (error) {
      const isTimeout = error instanceof Error && error.message.includes('timeout');
      console.error(`❌ 图片生成失败: ${name} (尝试 ${attempt}/${maxRetries}) ${isTimeout ? '(超时)' : ''}:`, error);
      lastError = error;

      // 如果不是最后一次尝试，等待一段时间再重试
      if (attempt < maxRetries) {
        const waitTime = isTimeout ? 8000 : attempt * 3000; // 超时后等待8秒，其他错误递增等待
        // 等待重试中...
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  // 所有重试都失败了
  console.error(`💥 图片生成彻底失败: ${name} (${maxRetries}次尝试全部失败)。最后错误:`, lastError);

  // 使用占位符图片
  // 使用占位符图片
  return `https://picsum.photos/seed/${encodeURIComponent(name)}/400/600`;
};

// 解析风格提示词，提取阵营标签
const parseStylePrompt = (stylePrompt?: string): string => {
  if (!stylePrompt) {
    return '默认风格';
  }

  // 匹配 "阵营（说明）" 格式
  const match = stylePrompt.match(/^([^（(]+)[（(]([^）)]*)[）)]/);
  if (match) {
    return match[1].trim();
  }

  // 如果不匹配格式，直接使用原文本作为阵营
  return stylePrompt;
};

export const generateArmyCard = async (
  rarity: ArmyRarity,
  characterClass: string,
  stylePrompt?: string,
  existingCards?: ArmyCardData[],
  referenceImage?: string,
  customClass?: any,
  attributeSchema?: any
): Promise<Omit<ArmyCardData, 'id' | 'rarity'>> => {
  const concept = await generateCardConcept(rarity, characterClass, stylePrompt, existingCards, referenceImage, customClass);
  const imageUrl = await generateCardImage(concept.name_cn, concept.description_en, stylePrompt, referenceImage);

  // 解析风格提示词，提取阵营标签
  const faction = parseStylePrompt(stylePrompt);

  // 创建基础卡片数据
  const baseCard = {
    name: concept.name_cn,
    description: concept.description_cn,
    imageUrl,
    skills: concept.skills,
    story: concept.story,
    characterClass: characterClass,
    stylePrompt: faction, // 只保存阵营作为标签
    createdAt: new Date().toISOString()
  };

  // 如果有属性模式，自动生成属性
  if (attributeSchema && existingCards) {
    try {
      const { generateAttributesForNewCard } = await import('./attributeService');

      // 创建临时卡片对象用于属性生成
      const tempCard = {
        ...baseCard,
        id: 'temp',
        rarity
      };

      const attributes = await generateAttributesForNewCard(
        tempCard,
        existingCards,
        attributeSchema
      );

      return {
        ...baseCard,
        attributes
      };
    } catch (error) {
      console.error('Failed to generate attributes for new card:', error);
      // 如果属性生成失败，返回不带属性的卡片
      return baseCard;
    }
  }

  return baseCard;
};

// 生成阅兵描述
export const generateParadeDescription = async (campUnits: any[]): Promise<string> => {
  try {
    // 按兵种和稀有度分组统计
    const unitSummary = campUnits.reduce((acc, unit) => {
      const key = `${unit.characterClass}-${unit.rarity.level}`;
      acc[key] = (acc[key] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const unitList = Object.entries(unitSummary)
      .map(([key, count]) => {
        const [characterClass, rarity] = key.split('-');
        return `${count}名${rarity}级${characterClass}`;
      })
      .join('、');

    // 收集具体的士兵信息用于详细描述
    const detailedUnits = campUnits.map(unit => ({
      name: unit.name,
      characterClass: unit.characterClass,
      rarity: unit.rarity.level,
      description: unit.description,
      skills: unit.skills?.map(skill => skill.name).join('、') || '无',
      story: unit.story
    }));

    // 按稀有度分组展示代表性士兵
    const rarityGroups = detailedUnits.reduce((acc, unit) => {
      if (!acc[unit.rarity]) acc[unit.rarity] = [];
      acc[unit.rarity].push(unit);
      return acc;
    }, {} as Record<string, typeof detailedUnits>);

    // 为每个稀有度选择1-2个代表性士兵进行详细描述
    const representativeUnits = Object.entries(rarityGroups)
      .map(([rarity, units]) => {
        const selected = units.slice(0, Math.min(2, units.length));
        return selected.map(unit =>
          `【${unit.name}】${rarity}级${unit.characterClass} - ${unit.description}，掌握技能：${unit.skills}`
        ).join('\n');
      })
      .filter(desc => desc.length > 0)
      .join('\n');

    const prompt = `
请为一场军队阅兵仪式生成一段详细、有感染力的描述。

军队组成概况：
${unitList}
总计：${campUnits.length}名士兵

代表性士兵详情：
${representativeUnits}

要求：
1. 描述要生动、有画面感，让人仿佛身临其境
2. 突出不同兵种的特色和威严，要提到具体的士兵名字和他们的技能
3. 体现军队的纪律性和战斗力
4. 包含阅兵场面的宏大气势
5. 字数控制在400-600字
6. 语言要有感染力和震撼力，煽动性
7. 多用emoji表情符号
8. 要体现不同稀有度士兵的地位差异和特殊性
9. 描述士兵们的装备、气势和战斗技能展示
请生成阅兵描述：
`;

    const response = await generateText(prompt);

    return response || '阅兵仪式正在进行中，士兵们展现出了卓越的军事素养和团队精神！';
  } catch (error) {
    console.error('生成阅兵描述失败:', error);
    throw new Error('生成阅兵描述失败');
  }
};

// 生成营地旗帜
export const generateCampFlag = async (campUnits: any[]): Promise<{campName: string, flagPrompt: string, designDescription: string}> => {
  try {
    const unitTypes = [...new Set(campUnits.map(unit => unit.characterClass))];
    const rarityDistribution = campUnits.reduce((acc, unit) => {
      acc[unit.rarity.level] = (acc[unit.rarity.level] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const prompt = `
根据以下军队信息，为这个军队起一个威严的名字，并设计一面战旗：

军队信息：
- 兵种：${unitTypes.join('、')}
- 总兵力：${campUnits.length}名士兵
- 稀有度分布：${Object.entries(rarityDistribution).map(([rarity, count]) => `${rarity}级${count}名`).join('、')}

请提供：
1. 一个威严、有特色的军队名称（2-6个字）
2. 旗帜设计的中文描述（包含设计理念、象征意义、颜色搭配等，约100-200字）
3. 旗帜绘图的英文prompt（用于AI绘图，详细描述旗帜的视觉元素，强调整面旗帜占满画面，无其他背景元素）

格式：
军队名称：[名称]
设计描述：[中文设计说明]
绘图描述：[英文绘图prompt]
`;

    const response = await generateText(prompt);

    // 解析响应
    const responseText = response || '';
    const lines = responseText.split('\n');
    let campName = '威武军营';
    let designDescription = '这面旗帜象征着军队的团结与荣耀，采用庄重的色彩搭配，体现了战士们的勇气与决心。';
    let flagPrompt = 'A majestic military flag with golden eagle emblem on red background, symbolizing strength and honor';

    for (const line of lines) {
      const cleanLine = line.trim();

      if (cleanLine.includes('营地名称：') || cleanLine.includes('营地名称:')) {
        const extracted = cleanLine.split('：')[1]?.trim() || cleanLine.split(':')[1]?.trim();
        if (extracted && extracted !== '**' && extracted.length > 0) {
          campName = extracted.replace(/\*\*/g, '').trim(); // 移除markdown格式
        }
      }
      if (cleanLine.includes('设计描述：') || cleanLine.includes('设计描述:')) {
        const extracted = cleanLine.split('：')[1]?.trim() || cleanLine.split(':')[1]?.trim();
        if (extracted && extracted !== '**' && extracted.length > 0) {
          designDescription = extracted.replace(/\*\*/g, '').trim(); // 移除markdown格式
        }
      }
      if (cleanLine.includes('绘图描述：') || cleanLine.includes('绘图描述:')) {
        const extracted = cleanLine.split('：')[1]?.trim() || cleanLine.split(':')[1]?.trim();
        if (extracted && extracted !== '**' && extracted.length > 0) {
          flagPrompt = extracted.replace(/\*\*/g, '').trim(); // 移除markdown格式
        }
      }
    }

    // 解析结果完成

    return { campName, flagPrompt, designDescription };
  } catch (error) {
    console.error('生成营地信息失败:', error);
    return {
      campName: '威武军营',
      designDescription: '这面旗帜象征着军队的团结与荣耀，采用庄重的色彩搭配，体现了战士们的勇气与决心。',
      flagPrompt: 'A majestic military flag with golden eagle emblem on red background, symbolizing strength and honor'
    };
  }
};

// 生成旗帜图像
export const generateFlagImage = async (flagPrompt: string): Promise<string> => {
  const maxRetries = 3;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      // 使用统一API生成图片
      const imageUrl = await generateImage(flagPrompt);

      if (imageUrl) {
        return imageUrl;
      }
    } catch (error) {
      console.error(`Flag image generation failed (attempt ${attempt}/${maxRetries}):`, error);

      // 如果不是最后一次尝试，等待一段时间再重试
      if (attempt < maxRetries) {
        const waitTime = attempt * 2000; // 递增等待时间
        await new Promise(resolve => setTimeout(resolve, waitTime));
      }
    }
  }

  // 所有重试都失败了，使用占位符图像
  const flagId = Math.floor(Math.random() * 1000) + 100;
  return `https://picsum.photos/seed/flag-${flagId}/200/150`;
};
