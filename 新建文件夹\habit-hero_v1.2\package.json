{"name": "habit-hero-task-gacha", "private": true, "version": "0.0.0", "description": "A to-do list app that gamifies your daily tasks with points and a collectible card gacha system", "author": "Habit Hero Team", "type": "module", "main": "electron/main.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron:dev": "concurrently \"npm run dev\" \"cross-env NODE_ENV=development electron .\"", "electron:build": "vite build && electron-builder", "electron:debug": "cross-env NODE_ENV=development electron ."}, "dependencies": {"@google/genai": "^1.7.0", "@types/better-sqlite3": "^7.6.13", "better-sqlite3": "^12.1.1", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0"}, "devDependencies": {"@types/node": "^22.14.0", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^30.0.0", "electron-builder": "^24.13.3", "typescript": "~5.7.2", "vite": "^6.2.0"}, "build": {"appId": "com.habithero.app", "productName": "Habit Hero", "files": ["dist/**/*", "electron/**/*"], "directories": {"output": "release"}, "win": {"target": "nsis"}}}