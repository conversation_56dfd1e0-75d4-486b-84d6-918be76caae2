import React, { useState, useRef, useEffect } from 'react';
import { ArmyCardData, DeployedCard, CustomCharacterClass, AttributeSchema } from '../types';
import {
  generateArmyCard,
  selectRarity,
  CHARACTER_CLASSES,
  ARMY_RARITIES
} from '../services/geminiService';
import { ArmyCardView } from './ArmyCardView';
import { Loader } from './Loader';

interface ArmyGachaSystemProps {
  userPoints: number;
  onPointsChange: (points: number) => void;
  onCardGenerated: (card: ArmyCardData) => void;
  onUnitDeployed: (unit: DeployedCard) => void;
  stylePrompt: string;
  onStylePromptChange: (prompt: string) => void;
  existingCards: ArmyCardData[]; // 现有卡牌列表，用于避免重复
  onCustomClassChange?: () => void; // 新增：自定义职业变化回调
  attributeSchema?: AttributeSchema; // 属性模式
}

// 获取召唤消耗积分的函数
const getSummonCost = (): number => {
  const saved = localStorage.getItem('app-settings');
  if (saved) {
    try {
      const settings = JSON.parse(saved);
      return settings.summonCost || 10; // 默认10积分
    } catch {
      return 10;
    }
  }
  return 10;
};

export const ArmyGachaSystem: React.FC<ArmyGachaSystemProps> = ({
  userPoints,
  onPointsChange,
  onCardGenerated,
  onUnitDeployed,
  stylePrompt,
  onStylePromptChange,
  existingCards,
  onCustomClassChange,
  attributeSchema,
}) => {
  const [currentCard, setCurrentCard] = useState<ArmyCardData | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isRevealing, setIsRevealing] = useState(false);
  const [isDetailsVisible, setIsDetailsVisible] = useState(true);
  const [isClassSelectorOpen, setIsClassSelectorOpen] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // 自定义职业相关状态
  const [customClasses, setCustomClasses] = useState<CustomCharacterClass[]>([]);
  const [showCustomClassModal, setShowCustomClassModal] = useState(false);
  const [newCustomClass, setNewCustomClass] = useState({
    name: '',
    description: ''
  });

  // 获取当前阵营的自定义职业
  const getCurrentFactionCustomClasses = () => {
    if (!stylePrompt) return [];
    const currentFaction = stylePrompt.split('（')[0].split('(')[0].trim();
    return customClasses.filter(cc => cc.faction === currentFaction);
  };

  // 删除自定义职业
  const handleDeleteCustomClass = async (classId: string, event: React.MouseEvent) => {
    event.stopPropagation(); // 防止触发父按钮的点击事件

    try {
      if (window.electronAPI?.database) {
        await window.electronAPI.database.deleteCustomClass(classId);
        setCustomClasses(prev => prev.filter(cc => cc.id !== classId));
        // 通知其他组件自定义职业已更新
        onCustomClassChange?.();
      }
    } catch (error) {
      console.error('Failed to delete custom class:', error);
      setError('删除自定义职业失败');
    }
  };

  // 生成一套卡牌功能状态
  const [isGeneratingSet, setIsGeneratingSet] = useState(false);
  const [setProgress, setSetProgress] = useState({ current: 0, total: 0 });
  const [setResults, setSetResults] = useState<ArmyCardData[]>([]);

  // 参考图功能状态
  const [referenceImage, setReferenceImage] = useState<string | null>(null);
  const [showReferenceUpload, setShowReferenceUpload] = useState(false);

  // 获取当前的召唤消耗积分
  const summonCost = getSummonCost();

  const classSelectorRef = useRef<HTMLDivElement>(null);

  // 加载自定义职业
  useEffect(() => {
    const loadCustomClasses = async () => {
      try {
        if (window.electronAPI?.database) {
          const classes = await window.electronAPI.database.getCustomClasses();
          setCustomClasses(classes);
        }
      } catch (error) {
        console.error('Failed to load custom classes:', error);
      }
    };
    loadCustomClasses();
  }, []);

  // 创建自定义职业
  const handleCreateCustomClass = async () => {
    if (!newCustomClass.name.trim()) {
      setError('请填写职业名称');
      return;
    }

    if (userPoints < 10) {
      setError('积分不足！创建自定义职业需要10积分');
      return;
    }

    // 使用当前召唤设置的阵营，去掉括号补充
    const currentFaction = stylePrompt ? stylePrompt.split('（')[0].split('(')[0].trim() : '未知阵营';

    try {
      const customClass: CustomCharacterClass = {
        id: Date.now().toString(),
        name: newCustomClass.name.trim(),
        description: newCustomClass.description.trim(),
        faction: currentFaction,
        cost: 10,
        createdAt: new Date().toISOString()
      };

      if (window.electronAPI?.database) {
        await window.electronAPI.database.saveCustomClass(customClass);
        setCustomClasses(prev => [customClass, ...prev]);
        onPointsChange(userPoints - 10);
        setShowCustomClassModal(false);
        setNewCustomClass({ name: '', description: '' });
        setError(null);
        // 通知其他组件自定义职业已更新
        onCustomClassChange?.();
      }
    } catch (error) {
      console.error('Failed to create custom class:', error);
      setError('创建自定义职业失败');
    }
  };

  const handleCardClick = () => {
    if (currentCard) {
      setIsDetailsVisible(prev => !prev);
    }
  };

  // 处理参考图上传
  const handleReferenceImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      // 检查文件类型
      if (!file.type.startsWith('image/')) {
        alert('请选择角色图片文件');
        return;
      }

      // 检查文件大小 (限制为5MB)
      if (file.size > 5 * 1024 * 1024) {
        alert('角色图片文件过大，请选择小于5MB的图片');
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setReferenceImage(result);
        setShowReferenceUpload(false);
      };
      reader.readAsDataURL(file);
    }
  };

  // 清除参考图
  const clearReferenceImage = () => {
    setReferenceImage(null);
  };

  const handleDraw = async (characterClass: string) => {
    if (isLoading) return;

    // 检查是否是自定义职业
    const customClass = customClasses.find(cc => cc.id === characterClass);
    const actualCost = customClass ? customClass.cost : summonCost;

    if (userPoints < actualCost) {
      setError(`积分不足！${customClass ? '使用自定义职业' : '召唤'}需要 ${actualCost} 积分。`);
      setTimeout(() => setError(null), 3000);
      return;
    }

    setIsClassSelectorOpen(false);
    setIsLoading(true);
    if (isRevealing) {
      setIsRevealing(false);
      await new Promise(resolve => setTimeout(resolve, 400));
    }
    setError(null);

    try {
      // 先扣除积分，使用当前的 userPoints 值
      const newPoints = userPoints - actualCost;
      onPointsChange(newPoints);

      const selectedRarity = selectRarity();

      // 如果选择了随机职业，从所有职业中随机选择一个
      let finalCharacterClass = characterClass;
      let finalCustomClass = customClass;

      if (characterClass === '随机') {
        const classNames = Object.keys(CHARACTER_CLASSES);
        finalCharacterClass = classNames[Math.floor(Math.random() * classNames.length)];
        finalCustomClass = undefined;
      } else if (customClass) {
        finalCharacterClass = customClass.name;
      }

      const cardConcept = await generateArmyCard(selectedRarity, finalCharacterClass, stylePrompt, existingCards, referenceImage || undefined, finalCustomClass, attributeSchema);
      const newCard: ArmyCardData = {
        ...cardConcept,
        id: `army-card-${Date.now()}-${Math.random()}`,
        rarity: selectedRarity,
      };

      // Process the new card
      setCurrentCard(newCard);
      onCardGenerated(newCard);

      // Immediately add the new soldier to the camp with pixel-based positioning
      const campWidth = 640;
      const campHeight = 480;
      const newUnit: DeployedCard = {
        ...newCard,
        deploymentId: `camp-unit-${newCard.id}`,
        position: {
          top: `${Math.random() * (campHeight * 0.85)}px`,
          left: `${Math.random() * (campWidth * 0.85)}px`
        },
      };
      onUnitDeployed(newUnit);

      setTimeout(() => {
        setIsRevealing(true);
        setIsDetailsVisible(true);
        setIsLoading(false);
      }, 100);

    } catch (e) {
      console.error("Failed to draw army card:", e);
      setError("召唤新兵失败。魔力不足，请重试。");
      // 退还积分 - 恢复到原始积分
      onPointsChange(userPoints);
      setIsLoading(false);
    }
  };

  // 生成一套卡牌的函数
  const handleGenerateCardSet = async () => {
    if (isGeneratingSet || isLoading) return;

    const allClasses = Object.keys(CHARACTER_CLASSES);
    const totalCost = allClasses.length * summonCost;

    if (userPoints < totalCost) {
      setError(`积分不足！需要 ${totalCost} 积分来生成一套完整卡牌（${allClasses.length} 个职业 × ${summonCost} 积分）。`);
      setTimeout(() => setError(null), 5000);
      return;
    }

    setIsGeneratingSet(true);
    setSetProgress({ current: 0, total: allClasses.length });
    setSetResults([]);
    setError(null);

    try {
      const results: ArmyCardData[] = [];

      for (let i = 0; i < allClasses.length; i++) {
        const characterClass = allClasses[i];
        setSetProgress({ current: i + 1, total: allClasses.length });

        try {
          // 扣除积分
          const newPoints = userPoints - summonCost * (i + 1);
          onPointsChange(newPoints);

          const selectedRarity = selectRarity();
          const cardConcept = await generateArmyCard(selectedRarity, characterClass, stylePrompt, existingCards, referenceImage || undefined, undefined, attributeSchema);
          const newCard: ArmyCardData = {
            ...cardConcept,
            id: `set-card-${Date.now()}-${i}`,
            rarity: selectedRarity,
          };

          // 添加到营地
          const campWidth = 640;
          const campHeight = 480;
          const newUnit: DeployedCard = {
            ...newCard,
            deploymentId: `set-unit-${newCard.id}`,
            position: {
              top: `${Math.random() * (campHeight * 0.85)}px`,
              left: `${Math.random() * (campWidth * 0.85)}px`
            },
          };

          onCardGenerated(newCard);
          onUnitDeployed(newUnit);
          results.push(newCard);

          // 短暂延迟，避免API调用过于频繁
          await new Promise(resolve => setTimeout(resolve, 1000));

        } catch (error) {
          console.error(`生成 ${characterClass} 失败:`, error);
          // 如果单个职业失败，继续下一个，但退还这次的积分
          const refundPoints = userPoints - summonCost * i;
          onPointsChange(refundPoints);
        }
      }

      setSetResults(results);
      setCurrentCard(results[results.length - 1] || null);

      if (results.length > 0) {
        setIsRevealing(true);
        setIsDetailsVisible(true);
      }

    } catch (error) {
      console.error('生成卡牌套装失败:', error);
      setError('生成过程中发生错误，请重试。');
      // 退还所有积分
      onPointsChange(userPoints);
    } finally {
      setIsGeneratingSet(false);
      setSetProgress({ current: 0, total: 0 });
    }
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (classSelectorRef.current && !classSelectorRef.current.contains(event.target as Node)) {
        setIsClassSelectorOpen(false);
      }
    };
    if (isClassSelectorOpen) document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [isClassSelectorOpen]);

  const canAfford = userPoints >= summonCost;

  return (
    <div className="p-6 bg-slate-800 rounded-xl shadow-2xl">
      <h3 className="text-2xl font-semibold text-sky-400 mb-4 text-center">军队召唤系统</h3>
      
      {/* Card Display */}
      <div className="flex justify-center mb-6">
        <ArmyCardView 
          card={currentCard} 
          isRevealing={isRevealing} 
          isDetailsVisible={isDetailsVisible} 
          onCardClick={handleCardClick} 
        />
      </div>

      {/* Controls */}
      <div className="space-y-4">
        {/* Style Prompt Input */}
        <div>
          <label htmlFor="army-style-prompt" className="block text-center text-sm font-medium text-gray-400 mb-2">
            卡片艺术风格 (可选)
          </label>
          <input
            id="army-style-prompt"
            type="text"
            value={stylePrompt}
            onChange={e => onStylePromptChange(e.target.value)}
            placeholder="例如：光明圣殿（神圣光辉风格）, 暗影军团（黑暗哥特风格）"
            className="w-full p-3 rounded-md bg-gray-700 text-white text-center border border-gray-600 focus:outline-none focus:ring-2 focus:ring-purple-500 disabled:bg-gray-800"
            disabled={isLoading}
          />
          <p className="text-xs text-gray-500 text-center mt-1">
            💡 建议格式：阵营（风格说明），阵营将作为筛选标签
          </p>
        </div>

        {/* Reference Character Upload */}
        <div>
          <label className="block text-center text-sm font-medium text-gray-400 mb-2">
            参考角色 (可选)
          </label>

          {referenceImage ? (
            <div className="relative">
              <img
                src={referenceImage}
                alt="参考角色"
                className="w-full max-w-xs mx-auto rounded-lg border border-gray-600"
                style={{ maxHeight: '200px', objectFit: 'contain' }}
              />
              <button
                onClick={clearReferenceImage}
                className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs"
                title="删除参考角色"
              >
                ×
              </button>
              <p className="text-xs text-green-400 text-center mt-2">
                ✓ 已上传参考角色，将参考此角色的外观特征生成新角色
              </p>
            </div>
          ) : (
            <div className="text-center">
              <button
                onClick={() => setShowReferenceUpload(!showReferenceUpload)}
                className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors text-sm"
                disabled={isLoading}
              >
                👤 上传参考角色
              </button>
              {showReferenceUpload && (
                <div className="mt-3">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={handleReferenceImageUpload}
                    className="block w-full text-sm text-gray-400 file:mr-4 file:py-2 file:px-4 file:rounded-lg file:border-0 file:text-sm file:font-medium file:bg-purple-600 file:text-white hover:file:bg-purple-700"
                  />
                  <p className="text-xs text-gray-500 mt-2">
                    上传角色图片作为参考，AI将模仿其外观特征、服装风格等
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Draw Button and Class Selector */}
        <div className="relative" ref={classSelectorRef}>
          <button
            onClick={() => !isLoading && setIsClassSelectorOpen(prev => !prev)}
            disabled={isLoading || !canAfford}
            className={`w-full py-3 px-6 rounded-lg font-semibold text-white transition-all duration-200 ease-in-out transform hover:scale-105 ${
              canAfford && !isLoading
                ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 shadow-lg' 
                : 'bg-slate-600 cursor-not-allowed opacity-70'
            }`}
          >
            {isLoading ? <Loader /> : `招募新兵 (消耗 ${summonCost} 积分)`}
          </button>
          
          {isClassSelectorOpen && (
            <div className="absolute bottom-full mb-3 w-full p-2 bg-gray-800 border border-purple-500/50 rounded-lg shadow-2xl z-10 grid grid-cols-3 sm:grid-cols-4 gap-2 animate-fade-in-up">
              {/* 随机选项 */}
              <button
                onClick={() => handleDraw('随机')}
                className="p-2 text-sm text-white bg-gradient-to-r from-purple-600 to-pink-600 rounded-md hover:from-purple-700 hover:to-pink-700 transition-all duration-200 font-semibold border border-purple-400/50"
              >
                🎲 随机
              </button>

              {/* 具体职业选项 */}
              {Object.keys(CHARACTER_CLASSES).map(className => (
                <button
                  key={className}
                  onClick={() => handleDraw(className)}
                  className="p-2 text-sm text-white bg-gray-700/80 rounded-md hover:bg-purple-600 transition-colors duration-200"
                >
                  {className}
                </button>
              ))}

              {/* 自定义职业选项 */}
              {getCurrentFactionCustomClasses().map(customClass => (
                <div key={customClass.id} className="relative">
                  <button
                    onClick={() => handleDraw(customClass.id)}
                    className="w-full p-2 text-sm text-white bg-gradient-to-r from-indigo-600 to-purple-600 rounded-md hover:from-indigo-700 hover:to-purple-700 transition-colors duration-200 border border-indigo-400/50"
                    title={`${customClass.description} (${customClass.cost}积分)`}
                  >
                    ⭐ {customClass.name}
                  </button>
                  <button
                    onClick={(e) => handleDeleteCustomClass(customClass.id, e)}
                    className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 hover:bg-red-600 text-white rounded-full text-xs flex items-center justify-center transition-colors"
                    title="删除自定义职业"
                  >
                    ×
                  </button>
                </div>
              ))}

              {/* 创建自定义职业按钮 */}
              {stylePrompt && (
                <button
                  onClick={() => setShowCustomClassModal(true)}
                  className="p-2 text-sm text-white bg-gradient-to-r from-amber-600 to-orange-600 rounded-md hover:from-amber-700 hover:to-orange-700 transition-colors duration-200 border border-amber-400/50"
                  title={`为 ${stylePrompt.split('（')[0].split('(')[0].trim()} 阵营创建专属职业`}
                >
                  ➕ 创建职业
                </button>
              )}
            </div>
          )}
        </div>

        {/* Generate Card Set Button */}
        <div className="relative">
          <button
            onClick={handleGenerateCardSet}
            disabled={isGeneratingSet || isLoading || userPoints < Object.keys(CHARACTER_CLASSES).length * summonCost}
            className={`w-full py-2 px-4 rounded-lg font-medium text-white transition-all duration-200 text-sm ${
              !isGeneratingSet && !isLoading && userPoints >= Object.keys(CHARACTER_CLASSES).length * summonCost
                ? 'bg-gradient-to-r from-emerald-600 to-teal-600 hover:from-emerald-700 hover:to-teal-700 shadow-md hover:scale-105'
                : 'bg-slate-600 cursor-not-allowed opacity-70'
            }`}
          >
            {isGeneratingSet ? (
              <div className="flex items-center justify-center space-x-2">
                <Loader size="small" />
                <span>生成中... ({setProgress.current}/{setProgress.total})</span>
              </div>
            ) : (
              `🎴 生成一套卡牌 (消耗 ${Object.keys(CHARACTER_CLASSES).length * summonCost} 积分)`
            )}
          </button>

          {/* Generation Progress Bar */}
          {isGeneratingSet && setProgress.total > 0 && (
            <div className="mt-2">
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div
                  className="bg-gradient-to-r from-emerald-500 to-teal-500 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${(setProgress.current / setProgress.total) * 100}%` }}
                />
              </div>
              <div className="text-xs text-gray-400 text-center mt-1">
                正在生成: {Object.keys(CHARACTER_CLASSES)[setProgress.current - 1] || '准备中...'}
              </div>
            </div>
          )}
        </div>

        {/* Error Message */}
        {error && <p className="text-red-400 text-center animate-pulse">{error}</p>}
        
        {/* Insufficient Points Warning */}
        {!canAfford && (
          <p className="text-sm text-red-400 text-center">还差 {summonCost - userPoints} 积分才能召唤。</p>
        )}

        {/* Card Set Results Summary */}
        {setResults.length > 0 && (
          <div className="bg-slate-800/50 rounded-lg p-3 border border-emerald-500/30">
            <h4 className="text-sm font-semibold text-emerald-400 mb-2">🎴 卡牌套装</h4>
            <div className="text-xs text-gray-300 space-y-1">
              <p>成功生成: {setResults.length}/{Object.keys(CHARACTER_CLASSES).length} 张卡牌</p>
              <div className="flex flex-wrap gap-1">
                {setResults.map((card, index) => (
                  <span
                    key={card.id}
                    className={`px-2 py-1 rounded text-xs ${card.rarity.color} bg-slate-700/50`}
                  >
                    {card.characterClass}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )}

        {/* Rarity Information */}
        <div className="text-center text-xs text-gray-400 space-y-1">
          <p className="font-bold">稀有度概率:</p>
          <div className="flex justify-center flex-wrap gap-x-4 gap-y-1">
            {ARMY_RARITIES.map(r => (
              <span key={r.level} className="whitespace-nowrap">
                <span className={`${r.color} font-semibold`}>{r.level}</span>: {(r.probability * 100).toFixed(0)}%
              </span>
            ))}
          </div>
        </div>
      </div>

      {/* 自定义职业创建模态框 */}
      {showCustomClassModal && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-xl p-6 w-96 max-w-[90vw] border border-slate-600">
            <h3 className="text-xl font-bold text-amber-400 mb-2">创建自定义职业</h3>
            <p className="text-sm text-slate-400 mb-4">
              为 <span className="text-amber-300 font-semibold">
                {stylePrompt ? stylePrompt.split('（')[0].split('(')[0].trim() : '未知'}
              </span> 阵营创建专属职业
            </p>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  职业名称 *
                </label>
                <input
                  type="text"
                  value={newCustomClass.name}
                  onChange={(e) => setNewCustomClass(prev => ({ ...prev, name: e.target.value }))}
                  placeholder="例如：暗影刺客"
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-amber-500"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  所属阵营
                </label>
                <div className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-amber-300">
                  {stylePrompt ? stylePrompt.split('（')[0].split('(')[0].trim() : '未设置阵营'}
                </div>
                <p className="text-xs text-slate-400 mt-1">
                  自动使用当前召唤设置的阵营
                </p>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  职业描述（可选）
                </label>
                <textarea
                  value={newCustomClass.description}
                  onChange={(e) => setNewCustomClass(prev => ({ ...prev, description: e.target.value }))}
                  placeholder="描述这个职业的特点和能力..."
                  rows={3}
                  className="w-full px-3 py-2 bg-slate-700 border border-slate-600 rounded-lg text-white placeholder-slate-400 focus:outline-none focus:border-amber-500 resize-none"
                />
              </div>

              <div className="bg-amber-900/20 border border-amber-500/30 rounded-lg p-3">
                <p className="text-amber-300 text-sm">
                  💰 创建自定义职业需要消耗 <span className="font-bold">10 积分</span>
                </p>
                <p className="text-amber-300 text-sm mt-1">
                  🎯 使用自定义职业召唤角色也需要额外消耗 <span className="font-bold">10 积分</span>
                </p>
                <p className="text-amber-300 text-sm mt-1">
                  🏛️ 自定义职业与阵营绑定，只在对应阵营下显示
                </p>
              </div>
            </div>

            <div className="flex space-x-3 mt-6">
              <button
                onClick={() => {
                  setShowCustomClassModal(false);
                  setNewCustomClass({ name: '', description: '' });
                  setError(null);
                }}
                className="flex-1 px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors"
              >
                取消
              </button>
              <button
                onClick={handleCreateCustomClass}
                disabled={!newCustomClass.name.trim() || userPoints < 10 || !stylePrompt}
                className={`flex-1 px-4 py-2 rounded-lg font-medium transition-colors ${
                  newCustomClass.name.trim() && userPoints >= 10 && stylePrompt
                    ? 'bg-gradient-to-r from-amber-600 to-orange-600 hover:from-amber-700 hover:to-orange-700 text-white'
                    : 'bg-slate-600 text-slate-400 cursor-not-allowed'
                }`}
              >
                创建 (10积分)
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};
