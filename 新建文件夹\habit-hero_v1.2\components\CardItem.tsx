import React from 'react';
import { Card, Rarity } from '../types';
import { RARITY_COLORS, RARITY_TEXT_COLORS, RARITY_GRADIENTS, RARITY_PROBABILITIES } from '../constants';

interface CardItemProps {
  card: Card;
  isCollected: boolean;
  onClick?: () => void;
}

const CardItem: React.FC<CardItemProps> = ({ card, isCollected, onClick }) => {
  const cardRarityClass = RARITY_COLORS[card.rarity] || RARITY_COLORS[Rarity.COMMON];
  const cardGradientClass = RARITY_GRADIENTS[card.rarity] || RARITY_GRADIENTS[Rarity.COMMON];
  const cardTextColorClass = RARITY_TEXT_COLORS[card.rarity] || RARITY_TEXT_COLORS[Rarity.COMMON];

  const probability = (RARITY_PROBABILITIES[card.rarity] * 100).toFixed(0);

  const cardContent = (
    <>
      {isCollected && (
        <div className="absolute top-2 right-2 bg-green-500 text-white text-xs font-bold px-1.5 py-0.5 rounded-full z-10">
          x1
        </div>
      )}
      <div className={`h-40 sm:h-48 flex items-center justify-center text-6xl sm:text-7xl ${cardGradientClass} ${!isCollected ? 'bg-slate-700' : ''}`}>
        {isCollected ? card.emoji : '❓'}
      </div>
      <div className={`p-3 sm:p-4 text-center ${!isCollected ? 'bg-slate-700' : 'bg-slate-800'}`}>
        <h3 className={`text-base sm:text-lg font-semibold truncate ${isCollected ? 'text-sky-300' : 'text-slate-400'}`}>{isCollected ? card.name : '未解锁'}</h3>
        <p className={`text-xs font-bold uppercase ${cardTextColorClass} px-2 py-0.5 rounded-full inline-block ${cardRarityClass} bg-opacity-30 my-1`}>
            {card.rarity}
        </p>
        <p className={`text-xs sm:text-sm h-10 overflow-y-auto text-ellipsis ${isCollected ? 'text-slate-400' : 'text-slate-500'}`}>{isCollected ? card.description : '完成任务，收集更多卡片吧！'}</p>
        <p className="text-xs text-sky-400 mt-1">
          获取概率: {probability}%
        </p>
      </div>
    </>
  );

  return onClick ? (
    <button
      onClick={onClick}
      className={`w-full rounded-xl shadow-xl overflow-hidden transform hover:scale-105 transition-transform duration-300 border-2 ${cardRarityClass} ${!isCollected ? 'opacity-60 grayscale' : ''} relative focus:outline-none focus:ring-2 focus:ring-sky-500 focus:ring-opacity-50`}
      aria-label={`查看卡片 ${card.name} 详情`}
    >
      {cardContent}
    </button>
  ) : (
    <div className={`rounded-xl shadow-xl overflow-hidden transform hover:scale-105 transition-transform duration-300 border-2 ${cardRarityClass} ${!isCollected ? 'opacity-60 grayscale' : ''} relative`}>
      {cardContent}
    </div>
  );
};

export default CardItem;