
import React, { useState, useEffect } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { JournalEntry, JournalType } from '../types';
import { EMOJI_SUGGESTIONS, JOURNAL_TEXT_SUGGESTIONS } from '../constants';
import { PencilSquareIcon } from './icons';
import { getFormattedDate } from '../utils/dateUtils';

interface AddJournalEntryFormProps {
  onAddJournalEntry: (entry: JournalEntry) => void;
  onClose: () => void;
  selectedDate: Date;
}

const AddJournalEntryForm: React.FC<AddJournalEntryFormProps> = ({ onAddJournalEntry, onClose, selectedDate }) => {
  const [text, setText] = useState('');
  const [icon, setIcon] = useState<string>('');
  const [entryDate, setEntryDate] = useState<string>(getFormattedDate(selectedDate));
  const [journalType, setJournalType] = useState<JournalType>(JournalType.OUTPUT); // 默认输出型
  
  // 存储用户之前输入过的日志文本和图标
  const [previousTexts, setPreviousTexts] = useState<string[]>([]);
  const [previousIcons, setPreviousIcons] = useState<string[]>([]);
  
  // 从 localStorage 加载之前输入过的内容
  useEffect(() => {
    const savedTexts = localStorage.getItem('journalTextHistory');
    const savedIcons = localStorage.getItem('journalIconHistory');
    
    if (savedTexts) {
      try {
        const parsedTexts = JSON.parse(savedTexts);
        if (Array.isArray(parsedTexts)) {
          setPreviousTexts(parsedTexts);
        }
      } catch (e) {
        console.error('Failed to parse journal text history:', e);
      }
    }
    
    if (savedIcons) {
      try {
        const parsedIcons = JSON.parse(savedIcons);
        if (Array.isArray(parsedIcons)) {
          setPreviousIcons(parsedIcons);
        }
      } catch (e) {
        console.error('Failed to parse journal icon history:', e);
      }
    }
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (!text.trim()) return;

    const newEntry: JournalEntry = {
      id: uuidv4(),
      content: text.trim(), // Changed from text to content
      date: entryDate, // Changed from entryDate to date
      createdAt: new Date().toISOString(), // Timestamp of creation
      icon: icon || undefined,
      type: journalType, // 添加日志类型
    };
    
    // 保存到历史记录
    if (text.trim()) {
      const newTexts = [text.trim(), ...previousTexts.filter(t => t !== text.trim())].slice(0, 10);
      setPreviousTexts(newTexts);
      localStorage.setItem('journalTextHistory', JSON.stringify(newTexts));
    }
    
    if (icon) {
      const newIcons = [icon, ...previousIcons.filter(i => i !== icon)].slice(0, 10);
      setPreviousIcons(newIcons);
      localStorage.setItem('journalIconHistory', JSON.stringify(newIcons));
    }
    
    onAddJournalEntry(newEntry);
    onClose(); 
  };

  // 合并建议和历史记录
  const allTextSuggestions = [...new Set([...previousTexts, ...JOURNAL_TEXT_SUGGESTIONS])];
  const allIconSuggestions = [...new Set([...previousIcons, ...EMOJI_SUGGESTIONS])];

  return (
    <form onSubmit={handleSubmit} className="space-y-5">
      <div>
        <label htmlFor="journalText" className="block text-sm font-medium text-slate-300 mb-1">日志内容</label>
        <textarea
          id="journalText"
          value={text}
          onChange={(e) => setText(e.target.value)}
          placeholder={journalType === JournalType.OUTPUT
            ? "今天发生了什么值得记录的事..."
            : "今天有什么想法、感受或计划..."
          }
          className="w-full p-3 h-32 bg-slate-700 border border-slate-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 outline-none transition-colors text-slate-100 placeholder-slate-400"
          list="text-suggestions"
          required
        />
        <datalist id="text-suggestions">
          {allTextSuggestions.map((suggestion, index) => (
            <option key={index} value={suggestion} />
          ))}
        </datalist>
      </div>

      {/* 日志类型切换 */}
      <div>
        <label className="block text-sm font-medium text-slate-300 mb-2">日志类型</label>
        <div className="flex bg-slate-700 rounded-lg p-1 border border-slate-600">
          <button
            type="button"
            onClick={() => setJournalType(JournalType.OUTPUT)}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
              journalType === JournalType.OUTPUT
                ? 'bg-blue-600 text-white shadow-md'
                : 'text-slate-300 hover:text-white hover:bg-slate-600'
            }`}
          >
            📝 输出型
          </button>
          <button
            type="button"
            onClick={() => setJournalType(JournalType.INPUT)}
            className={`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-all duration-200 ${
              journalType === JournalType.INPUT
                ? 'bg-purple-600 text-white shadow-md'
                : 'text-slate-300 hover:text-white hover:bg-slate-600'
            }`}
          >
            💭 输入型
          </button>
        </div>
        <p className="text-xs text-slate-400 mt-1">
          {journalType === JournalType.OUTPUT
            ? '记录发生的事情、行为、事件等'
            : '记录想法、感受、计划、反思等'
          }
        </p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <label htmlFor="journalIcon" className="block text-sm font-medium text-slate-300 mb-1">图标 (Emoji)</label>
          <input
            id="journalIcon"
            type="text"
            value={icon}
            onChange={(e) => setIcon(e.target.value)}
            placeholder={`可选, 例如: ${EMOJI_SUGGESTIONS[Math.floor(Math.random() * EMOJI_SUGGESTIONS.length)]}`}
            className="w-full p-3 bg-slate-700 border border-slate-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 outline-none transition-colors text-slate-100 placeholder-slate-400"
            maxLength={2}
            list="icon-suggestions"
          />
          <datalist id="icon-suggestions">
            {allIconSuggestions.map((suggestion, index) => (
              <option key={index} value={suggestion} />
            ))}
          </datalist>
        </div>
        <div>
          <label htmlFor="journalEntryDate" className="block text-sm font-medium text-slate-300 mb-1">日志日期</label>
          <input
            id="journalEntryDate"
            type="date"
            value={entryDate}
            onChange={(e) => setEntryDate(e.target.value)}
            className="w-full p-3 bg-slate-700 border border-slate-600 rounded-md focus:ring-2 focus:ring-purple-500 focus:border-purple-500 outline-none transition-colors text-slate-100"
            required
          />
        </div>
      </div>
      
      <button
        type="submit"
        className="w-full flex items-center justify-center p-3 bg-gradient-to-r from-purple-500 to-pink-500 text-white font-semibold rounded-md hover:from-purple-600 hover:to-pink-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-slate-800 focus:ring-purple-500 transition-all duration-150 ease-in-out transform hover:scale-105"
      >
        <PencilSquareIcon className="w-5 h-5 mr-2" /> 保存日志
      </button>
    </form>
  );
};

export default AddJournalEntryForm;

