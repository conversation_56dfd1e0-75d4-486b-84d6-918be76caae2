// 统一API服务 - 支持多供应商
import { GoogleGenAI } from "@google/genai";
import {
  getCurrentConfig,
  getTextConfig,
  getImageConfig,
  type SimpleApiConfig
} from './apiConfigService';

// Gemini客户端缓存
const geminiClients = new Map<string, any>();

// 获取或创建Gemini客户端
const getGeminiClient = (apiKey: string) => {
  if (!geminiClients.has(apiKey)) {
    const client = new GoogleGenAI({ apiKey });
    geminiClients.set(apiKey, client);
  }
  return geminiClients.get(apiKey);
};

// OpenAI格式的文本生成
const generateTextWithOpenAI = async (
  config: SimpleApiConfig['text'],
  prompt: string,
  timeoutMs: number = 300000
): Promise<string> => {
  const baseUrl = config.baseUrl || 'https://api.openai.com/v1';
  const url = `${baseUrl}/chat/completions`;

  const requestBody = {
    model: config.model,
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ],
    max_tokens: 4000,
    temperature: 0.7
  };

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${config.apiKey}`
      },
      body: JSON.stringify(requestBody),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();

    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Invalid response format from OpenAI API');
    }

    return data.choices[0].message.content || '';
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error(`OpenAI API timeout after ${timeoutMs}ms`);
    }
    throw error;
  }
};

// Gemini格式的文本生成
const generateTextWithGemini = async (
  config: SimpleApiConfig['text'],
  prompt: string,
  timeoutMs: number = 300000
): Promise<string> => {
  const client = getGeminiClient(config.apiKey);

  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`Gemini API timeout after ${timeoutMs}ms`)), timeoutMs);
  });

  const generatePromise = client.models.generateContent({
    model: config.model,
    contents: prompt,
  });

  const response = await Promise.race([generatePromise, timeoutPromise]) as any;
  return response.text || '';
};

// OpenAI格式的图片生成
const generateImageWithOpenAI = async (
  config: SimpleApiConfig['image'],
  prompt: string
): Promise<string> => {
  const baseUrl = config.baseUrl || 'https://api.openai.com/v1';
  const url = `${baseUrl}/images/generations`;

  const requestBody = {
    model: config.model,
    prompt: prompt,
    n: 1,
    size: "1024x1024",
    response_format: "b64_json"
  };

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${config.apiKey}`
    },
    body: JSON.stringify(requestBody)
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`OpenAI Image API error: ${response.status} - ${errorText}`);
  }

  const data = await response.json();

  if (!data.data || !data.data[0] || !data.data[0].b64_json) {
    throw new Error('Invalid response format from OpenAI Image API');
  }

  return `data:image/png;base64,${data.data[0].b64_json}`;
};

// Gemini格式的图片生成
const generateImageWithGemini = async (
  config: SimpleApiConfig['image'],
  prompt: string
): Promise<string> => {
  const client = getGeminiClient(config.apiKey);

  const timeoutMs = 120000;
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`Gemini Image API timeout after ${timeoutMs}ms`)), timeoutMs);
  });

  const generatePromise = client.models.generateImages({
    model: config.model,
    prompt: prompt,
    config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
  });

  const response = await Promise.race([generatePromise, timeoutPromise]) as any;

  if (response.generatedImages && response.generatedImages.length > 0 && response.generatedImages[0]?.image?.imageBytes) {
    const base64ImageBytes: string = response.generatedImages[0].image.imageBytes;
    return `data:image/jpeg;base64,${base64ImageBytes}`;
  } else {
    throw new Error('No images generated from Gemini API');
  }
};

// 统一的文本生成接口
export const generateText = async (prompt: string, timeoutMs: number = 300000): Promise<string> => {
  const textConfig = getTextConfig();

  if (!textConfig.apiKey) {
    throw new Error('No text generation API key configured');
  }

  try {
    if (textConfig.provider === 'openai') {
      return await generateTextWithOpenAI(textConfig, prompt, timeoutMs);
    } else if (textConfig.provider === 'gemini') {
      return await generateTextWithGemini(textConfig, prompt, timeoutMs);
    } else {
      throw new Error(`Unsupported provider type: ${textConfig.provider}`);
    }
  } catch (error) {
    console.error(`Text generation failed with ${textConfig.provider}:`, error);
    throw new Error(`文本生成失败 (${textConfig.provider}): ${error.message}`);
  }
};

// 统一的图片生成接口
export const generateImage = async (prompt: string): Promise<string> => {
  const imageConfig = getImageConfig();

  if (!imageConfig.apiKey) {
    throw new Error('No image generation API key configured');
  }

  try {
    if (imageConfig.provider === 'openai') {
      return await generateImageWithOpenAI(imageConfig, prompt);
    } else if (imageConfig.provider === 'gemini') {
      return await generateImageWithGemini(imageConfig, prompt);
    } else {
      throw new Error(`Unsupported provider type: ${imageConfig.provider}`);
    }
  } catch (error) {
    console.error(`Image generation failed with ${imageConfig.provider}:`, error);
    throw new Error(`图片生成失败 (${imageConfig.provider}): ${error.message}`);
  }
};
