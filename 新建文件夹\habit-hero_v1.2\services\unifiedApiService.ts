// 统一API服务 - 支持多供应商
import { GoogleGenAI } from "@google/genai";
import { 
  getCurrentConfig, 
  getTextProvider, 
  getImageProvider, 
  type ApiProvider 
} from './apiConfigService';

// Gemini客户端缓存
const geminiClients = new Map<string, any>();

// 获取或创建Gemini客户端
const getGeminiClient = (provider: ApiProvider) => {
  if (!geminiClients.has(provider.id)) {
    const client = new GoogleGenAI({ apiKey: provider.apiKey });
    geminiClients.set(provider.id, client);
  }
  return geminiClients.get(provider.id);
};

// OpenAI格式的文本生成
const generateTextWithOpenAI = async (
  provider: ApiProvider, 
  prompt: string, 
  model: string,
  timeoutMs: number = 300000
): Promise<string> => {
  const baseUrl = provider.baseUrl || 'https://api.openai.com/v1';
  const url = `${baseUrl}/chat/completions`;
  
  const requestBody = {
    model: model,
    messages: [
      {
        role: 'user',
        content: prompt
      }
    ],
    max_tokens: 4000,
    temperature: 0.7
  };

  const controller = new AbortController();
  const timeoutId = setTimeout(() => controller.abort(), timeoutMs);

  try {
    const response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${provider.apiKey}`
      },
      body: JSON.stringify(requestBody),
      signal: controller.signal
    });

    clearTimeout(timeoutId);

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`OpenAI API error: ${response.status} - ${errorText}`);
    }

    const data = await response.json();
    
    if (!data.choices || !data.choices[0] || !data.choices[0].message) {
      throw new Error('Invalid response format from OpenAI API');
    }

    return data.choices[0].message.content || '';
  } catch (error) {
    clearTimeout(timeoutId);
    if (error.name === 'AbortError') {
      throw new Error(`OpenAI API timeout after ${timeoutMs}ms`);
    }
    throw error;
  }
};

// Gemini格式的文本生成
const generateTextWithGemini = async (
  provider: ApiProvider, 
  prompt: string, 
  model: string,
  timeoutMs: number = 300000
): Promise<string> => {
  const client = getGeminiClient(provider);
  
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`Gemini API timeout after ${timeoutMs}ms`)), timeoutMs);
  });

  const generatePromise = client.models.generateContent({
    model: model,
    contents: prompt,
  });

  const response = await Promise.race([generatePromise, timeoutPromise]) as any;
  return response.text || '';
};

// OpenAI格式的图片生成
const generateImageWithOpenAI = async (
  provider: ApiProvider, 
  prompt: string, 
  model: string
): Promise<string> => {
  const baseUrl = provider.baseUrl || 'https://api.openai.com/v1';
  const url = `${baseUrl}/images/generations`;
  
  const requestBody = {
    model: model,
    prompt: prompt,
    n: 1,
    size: "1024x1024",
    response_format: "b64_json"
  };

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${provider.apiKey}`
    },
    body: JSON.stringify(requestBody)
  });

  if (!response.ok) {
    const errorText = await response.text();
    throw new Error(`OpenAI Image API error: ${response.status} - ${errorText}`);
  }

  const data = await response.json();
  
  if (!data.data || !data.data[0] || !data.data[0].b64_json) {
    throw new Error('Invalid response format from OpenAI Image API');
  }

  return `data:image/png;base64,${data.data[0].b64_json}`;
};

// Gemini格式的图片生成
const generateImageWithGemini = async (
  provider: ApiProvider, 
  prompt: string, 
  model: string
): Promise<string> => {
  const client = getGeminiClient(provider);
  
  const timeoutMs = 120000;
  const timeoutPromise = new Promise((_, reject) => {
    setTimeout(() => reject(new Error(`Gemini Image API timeout after ${timeoutMs}ms`)), timeoutMs);
  });

  const generatePromise = client.models.generateImages({
    model: model,
    prompt: prompt,
    config: { numberOfImages: 1, outputMimeType: 'image/jpeg' },
  });

  const response = await Promise.race([generatePromise, timeoutPromise]) as any;

  if (response.generatedImages && response.generatedImages.length > 0 && response.generatedImages[0]?.image?.imageBytes) {
    const base64ImageBytes: string = response.generatedImages[0].image.imageBytes;
    return `data:image/jpeg;base64,${base64ImageBytes}`;
  } else {
    throw new Error('No images generated from Gemini API');
  }
};

// 统一的文本生成接口
export const generateText = async (prompt: string, timeoutMs: number = 300000): Promise<string> => {
  const config = getCurrentConfig();
  const provider = getTextProvider();
  
  if (!provider) {
    throw new Error('No text generation provider configured');
  }

  const model = config.selectedTextModel;
  
  try {
    if (provider.type === 'openai') {
      return await generateTextWithOpenAI(provider, prompt, model, timeoutMs);
    } else if (provider.type === 'gemini') {
      return await generateTextWithGemini(provider, prompt, model, timeoutMs);
    } else {
      throw new Error(`Unsupported provider type: ${provider.type}`);
    }
  } catch (error) {
    console.error(`Text generation failed with ${provider.name}:`, error);
    throw new Error(`文本生成失败 (${provider.name}): ${error.message}`);
  }
};

// 统一的图片生成接口
export const generateImage = async (prompt: string): Promise<string> => {
  const config = getCurrentConfig();
  const provider = getImageProvider();
  
  if (!provider) {
    throw new Error('No image generation provider configured');
  }

  const model = config.selectedImageModel;
  
  try {
    if (provider.type === 'openai') {
      return await generateImageWithOpenAI(provider, prompt, model);
    } else if (provider.type === 'gemini') {
      return await generateImageWithGemini(provider, prompt, model);
    } else {
      throw new Error(`Unsupported provider type: ${provider.type}`);
    }
  } catch (error) {
    console.error(`Image generation failed with ${provider.name}:`, error);
    throw new Error(`图片生成失败 (${provider.name}): ${error.message}`);
  }
};
