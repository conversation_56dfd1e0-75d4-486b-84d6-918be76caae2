/**
 * Enhanced Storage Service using IndexedDB for better handling of large data like images
 * Falls back to localStorage for compatibility
 */

interface StorageItem<T = any> {
  key: string;
  value: T;
  timestamp: number;
  type: 'json' | 'blob' | 'string';
}

class StorageService {
  private dbName = 'HabitHeroDB';
  private dbVersion = 1;
  private storeName = 'storage';
  private db: IDBDatabase | null = null;
  private isIndexedDBSupported = false;

  constructor() {
    this.initializeDB();
  }

  private async initializeDB(): Promise<void> {
    if (!window.indexedDB) {
      console.warn('IndexedDB not supported, falling back to localStorage');
      return;
    }

    try {
      this.db = await this.openDB();
      this.isIndexedDBSupported = true;
    } catch (error) {
      console.error('Failed to initialize IndexedDB:', error);
      this.isIndexedDBSupported = false;
    }
  }

  private openDB(): Promise<IDBDatabase> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(this.dbName, this.dbVersion);

      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve(request.result);

      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains(this.storeName)) {
          const store = db.createObjectStore(this.storeName, { keyPath: 'key' });
          store.createIndex('timestamp', 'timestamp', { unique: false });
        }
      };
    });
  }

  async setItem<T>(key: string, value: T): Promise<void> {
    if (this.isIndexedDBSupported && this.db) {
      return this.setItemIndexedDB(key, value);
    } else {
      return this.setItemLocalStorage(key, value);
    }
  }

  async getItem<T>(key: string): Promise<T | null> {
    if (this.isIndexedDBSupported && this.db) {
      return this.getItemIndexedDB<T>(key);
    } else {
      return this.getItemLocalStorage<T>(key);
    }
  }

  async removeItem(key: string): Promise<void> {
    if (this.isIndexedDBSupported && this.db) {
      return this.removeItemIndexedDB(key);
    } else {
      return this.removeItemLocalStorage(key);
    }
  }

  async clear(): Promise<void> {
    if (this.isIndexedDBSupported && this.db) {
      return this.clearIndexedDB();
    } else {
      return this.clearLocalStorage();
    }
  }

  // IndexedDB methods
  private async setItemIndexedDB<T>(key: string, value: T): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    const item: StorageItem<T> = {
      key,
      value,
      timestamp: Date.now(),
      type: this.getValueType(value)
    };

    return new Promise((resolve, reject) => {
      const request = store.put(item);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  private async getItemIndexedDB<T>(key: string): Promise<T | null> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.storeName], 'readonly');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.get(key);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => {
        const result = request.result as StorageItem<T> | undefined;
        resolve(result ? result.value : null);
      };
    });
  }

  private async removeItemIndexedDB(key: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.delete(key);
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  private async clearIndexedDB(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const transaction = this.db.transaction([this.storeName], 'readwrite');
    const store = transaction.objectStore(this.storeName);

    return new Promise((resolve, reject) => {
      const request = store.clear();
      request.onerror = () => reject(request.error);
      request.onsuccess = () => resolve();
    });
  }

  // localStorage fallback methods
  private async setItemLocalStorage<T>(key: string, value: T): Promise<void> {
    try {
      const serialized = JSON.stringify(value);
      localStorage.setItem(key, serialized);
    } catch (error) {
      console.error('Failed to set item in localStorage:', error);
      throw error;
    }
  }

  private async getItemLocalStorage<T>(key: string): Promise<T | null> {
    try {
      const item = localStorage.getItem(key);
      return item ? JSON.parse(item) : null;
    } catch (error) {
      console.error('Failed to get item from localStorage:', error);
      return null;
    }
  }

  private async removeItemLocalStorage(key: string): Promise<void> {
    localStorage.removeItem(key);
  }

  private async clearLocalStorage(): Promise<void> {
    localStorage.clear();
  }

  private getValueType(value: any): 'json' | 'blob' | 'string' {
    if (value instanceof Blob) return 'blob';
    if (typeof value === 'string') return 'string';
    return 'json';
  }

  // Migration utility
  async migrateFromLocalStorage(keys: string[]): Promise<void> {
    if (!this.isIndexedDBSupported) return;

    for (const key of keys) {
      try {
        const value = localStorage.getItem(key);
        if (value !== null) {
          const parsedValue = JSON.parse(value);
          await this.setItem(key, parsedValue);
          console.log(`Migrated ${key} from localStorage to IndexedDB`);
        }
      } catch (error) {
        console.error(`Failed to migrate ${key}:`, error);
      }
    }
  }

  // Utility methods
  get isUsingIndexedDB(): boolean {
    return this.isIndexedDBSupported;
  }

  async getStorageInfo(): Promise<{ type: string; size?: number }> {
    if (this.isIndexedDBSupported) {
      return { type: 'IndexedDB' };
    } else {
      const size = new Blob(Object.values(localStorage)).size;
      return { type: 'localStorage', size };
    }
  }
}

// Create singleton instance
export const storageService = new StorageService();
export default storageService;
