/* Army System Styles */

/* 3D Card Flip Animation */
.perspective-1000 {
  perspective: 1000px;
}

.transform-style-preserve-3d {
  transform-style: preserve-3d;
}

.backface-hidden {
  backface-visibility: hidden;
}

.rotate-y-180 {
  transform: rotateY(180deg);
}

/* Text Shadow Effects */
.text-shadow {
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.text-shadow-sm {
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* 文本截断工具类 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Army Camp Deployed Cards */
.deployed-card {
  position: absolute;
  /* 移除可能冲突的过渡属性，完全由行内样式控制 */
  /* transition: all 0.3s ease-in-out; */
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
}

.deployed-card:hover {
  /* hover效果需要与translate结合 */
  z-index: 10;
  box-shadow: 0 0 20px var(--glow-color, #9CA3AF);
  /* scale效果现在由行内样式的hover处理 */
}

/* Camp Area */
.camp-area {
  position: relative;
  background: 
    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.1) 0%, transparent 50%);
}

/* Army Gallery Card Container */
.db-card-container {
  position: relative;
  display: inline-block;
}

.db-card-delete-btn {
  position: absolute;
  top: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background-color: #dc2626;
  color: white;
  border: none;
  border-radius: 50%;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.2s ease-in-out;
  z-index: 10;
}

.db-card-container:hover .db-card-delete-btn {
  opacity: 1;
}

.db-card-delete-btn:hover {
  background-color: #b91c1c;
}

/* Fade in animation for class selector */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

/* Slide in animation for feedback messages */
@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slideInUp {
  animation: slideInUp 0.3s ease-out;
}

/* Glow effects for rarity */
.rarity-glow-common {
  box-shadow: 0 0 10px #9CA3AF40;
}

.rarity-glow-rare {
  box-shadow: 0 0 10px #60A5FA40;
}

.rarity-glow-epic {
  box-shadow: 0 0 10px #A78BFA40;
}

.rarity-glow-legendary {
  box-shadow: 0 0 10px #FBBF2440;
}

.rarity-glow-mythic {
  box-shadow: 0 0 10px #FDE04740;
}

/* Loading spinner */
.spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #3498db;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Desktop Layout Styles */
.desktop-sidebar {
  transition: width 0.3s ease-in-out;
}

.desktop-sidebar:hover {
  box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1);
}

.desktop-main-content {
  transition: margin-left 0.3s ease-in-out;
}

/* Sidebar Navigation Styles */
.sidebar-nav-item {
  position: relative;
  overflow: hidden;
}

.sidebar-nav-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 3px;
  background: linear-gradient(to bottom, #0ea5e9, #06b6d4);
  transform: scaleY(0);
  transition: transform 0.2s ease-in-out;
}

.sidebar-nav-item.active::before {
  transform: scaleY(1);
}

/* Desktop Grid Layouts */
.desktop-grid {
  display: grid;
  gap: 2rem;
}

.desktop-grid-2 {
  grid-template-columns: 1fr 1fr;
}

.desktop-grid-3 {
  grid-template-columns: 1fr 2fr;
}

@media (max-width: 1280px) {
  .desktop-grid-2,
  .desktop-grid-3 {
    grid-template-columns: 1fr;
  }
}

/* Enhanced Card Layouts for Desktop */
.desktop-card {
  background: rgba(30, 41, 59, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
  border-radius: 12px;
  transition: all 0.3s ease-in-out;
}

.desktop-card:hover {
  border-color: rgba(148, 163, 184, 0.2);
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
  transform: translateY(-2px);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .deployed-card {
    width: 12px !important;
    height: 16px !important;
  }

  .camp-area {
    height: 20rem !important;
  }

  /* Hide sidebar on mobile, show mobile nav instead */
  .desktop-sidebar {
    display: none;
  }

  .desktop-main-content {
    margin-left: 0;
  }
}

/* Custom scrollbar for army components */
.army-scroll::-webkit-scrollbar {
  width: 6px;
}

.army-scroll::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.5);
  border-radius: 3px;
}

.army-scroll::-webkit-scrollbar-thumb {
  background: rgba(147, 51, 234, 0.7);
  border-radius: 3px;
}

.army-scroll::-webkit-scrollbar-thumb:hover {
  background: rgba(147, 51, 234, 0.9);
}

/* Hover effects for army cards */
.army-card-hover {
  transition: all 0.2s ease-in-out;
}

.army-card-hover:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

/* Army navigation button styles */
.army-nav-button {
  transition: all 0.2s ease-in-out;
}

.army-nav-button:hover {
  transform: scale(1.05);
}

/* Army stats display */
.army-stats {
  background: linear-gradient(135deg, rgba(55, 65, 81, 0.8) 0%, rgba(75, 85, 99, 0.8) 100%);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(147, 51, 234, 0.2);
}

/* Army card detail modal enhancements */
.army-modal-content {
  background: linear-gradient(135deg, rgba(30, 41, 59, 0.95) 0%, rgba(51, 65, 85, 0.95) 100%);
  backdrop-filter: blur(20px);
}

/* Army camp background pattern */
.camp-pattern {
  background-image: 
    radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0);
  background-size: 20px 20px;
}

/* Army rarity border animations */
.rarity-border-animate {
  position: relative;
  overflow: hidden;
}

.rarity-border-animate::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
  transition: left 0.5s;
}

.rarity-border-animate:hover::before {
  left: 100%;
}

/* Army unit movement animation */
.army-unit-move {
  transition: all 4s ease-in-out;
}

/* Army collection progress bar */
.collection-progress {
  background: linear-gradient(90deg, #8B5CF6 0%, #A78BFA 50%, #C4B5FD 100%);
  height: 4px;
  border-radius: 2px;
  overflow: hidden;
}

.collection-progress::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  background-image: linear-gradient(
    -45deg,
    rgba(255, 255, 255, .2) 25%,
    transparent 25%,
    transparent 50%,
    rgba(255, 255, 255, .2) 50%,
    rgba(255, 255, 255, .2) 75%,
    transparent 75%,
    transparent
  );
  background-size: 50px 50px;
  animation: move 2s linear infinite;
}

@keyframes move {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 50px 50px;
  }
}
