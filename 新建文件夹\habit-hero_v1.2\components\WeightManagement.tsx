import React, { useState, useEffect } from 'react';
import { WeightRecord } from '../types';
import { v4 as uuidv4 } from 'uuid';
import WeightChart from './WeightChart';

interface WeightManagementProps {
  weightRecords: WeightRecord[];
  onSaveWeight: (record: WeightRecord) => void;
  onDeleteWeight: (id: string) => void;
}

const WeightManagement: React.FC<WeightManagementProps> = ({
  weightRecords,
  onSaveWeight,
  onDeleteWeight,
}) => {
  const [selectedDate, setSelectedDate] = useState<string>(() => {
    const today = new Date();
    return `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;
  });
  const [weightInput, setWeightInput] = useState<string>('');
  const [noteInput, setNoteInput] = useState<string>('');
  const [currentMonth, setCurrentMonth] = useState<Date>(new Date());

  // 获取选中日期的体重记录
  const selectedDateRecord = weightRecords.find(record => record.date === selectedDate);

  // 当选择日期变化时，更新输入框
  useEffect(() => {
    if (selectedDateRecord) {
      setWeightInput(selectedDateRecord.weight.toString());
      setNoteInput(selectedDateRecord.note || '');
    } else {
      setWeightInput('');
      setNoteInput('');
    }
  }, [selectedDate, selectedDateRecord]);

  // 保存体重记录
  const handleSaveWeight = () => {
    const weight = parseFloat(weightInput);
    if (isNaN(weight) || weight <= 0) {
      alert('请输入有效的体重值');
      return;
    }

    const record: WeightRecord = {
      id: selectedDateRecord?.id || uuidv4(),
      weight,
      date: selectedDate,
      note: noteInput.trim() || undefined,
      createdAt: new Date().toISOString(),
    };

    onSaveWeight(record);
  };

  // 删除体重记录
  const handleDeleteWeight = () => {
    if (selectedDateRecord && confirm('确定要删除这条体重记录吗？')) {
      onDeleteWeight(selectedDateRecord.id);
    }
  };

  // 生成日历
  const generateCalendar = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const days = [];
    const current = new Date(startDate);

    // 获取今天的本地日期字符串
    const today = new Date();
    const todayStr = `${today.getFullYear()}-${(today.getMonth() + 1).toString().padStart(2, '0')}-${today.getDate().toString().padStart(2, '0')}`;

    for (let i = 0; i < 42; i++) {
      // 使用本地日期而不是UTC日期
      const dateStr = `${current.getFullYear()}-${(current.getMonth() + 1).toString().padStart(2, '0')}-${current.getDate().toString().padStart(2, '0')}`;
      const isCurrentMonth = current.getMonth() === month;
      const isToday = dateStr === todayStr;
      const isSelected = dateStr === selectedDate;
      const hasRecord = weightRecords.some(record => record.date === dateStr);

      days.push({
        date: new Date(current),
        dateStr,
        isCurrentMonth,
        isToday,
        isSelected,
        hasRecord,
      });

      current.setDate(current.getDate() + 1);
    }

    return days;
  };

  const calendarDays = generateCalendar();

  // 导航到上个月
  const goToPreviousMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() - 1));
  };

  // 导航到下个月
  const goToNextMonth = () => {
    setCurrentMonth(new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1));
  };

  return (
    <div className="desktop-grid">
      {/* 左侧：日历视图 */}
      <div className="desktop-grid-left">
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">体重管理</h2>
            <div className="flex items-center space-x-4">
              <button
                onClick={goToPreviousMonth}
                className="p-2 hover:bg-slate-700 rounded-lg transition-colors"
              >
                ←
              </button>
              <span className="text-lg font-semibold text-white min-w-[120px] text-center">
                {currentMonth.toLocaleDateString('zh-CN', { year: 'numeric', month: 'long' })}
              </span>
              <button
                onClick={goToNextMonth}
                className="p-2 hover:bg-slate-700 rounded-lg transition-colors"
              >
                →
              </button>
            </div>
          </div>

          {/* 星期标题 */}
          <div className="grid grid-cols-7 gap-1 mb-2">
            {['日', '一', '二', '三', '四', '五', '六'].map((day) => (
              <div key={day} className="text-center text-slate-400 text-sm font-medium p-2">
                {day}
              </div>
            ))}
          </div>

          {/* 日历网格 */}
          <div className="grid grid-cols-7 gap-1">
            {calendarDays.map((day, index) => (
              <button
                key={index}
                onClick={() => setSelectedDate(day.dateStr)}
                className={`
                  p-3 text-sm rounded-lg transition-all duration-200 relative
                  ${!day.isCurrentMonth 
                    ? 'text-slate-600 hover:bg-slate-700/50' 
                    : 'text-slate-200 hover:bg-slate-700'
                  }
                  ${day.isToday ? 'ring-2 ring-sky-500' : ''}
                  ${day.isSelected ? 'bg-sky-600 text-white' : ''}
                  ${day.hasRecord ? 'font-bold' : ''}
                `}
              >
                {day.date.getDate()}
                {day.hasRecord && (
                  <div className="absolute bottom-1 left-1/2 transform -translate-x-1/2 w-1 h-1 bg-green-400 rounded-full"></div>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* 右侧：体重输入和图表 */}
      <div className="desktop-grid-right space-y-6">
        {/* 体重输入区域 */}
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 className="text-xl font-bold text-white mb-4">
            {selectedDate} 体重记录
          </h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                体重 (kg)
              </label>
              <input
                type="number"
                step="0.1"
                value={weightInput}
                onChange={(e) => setWeightInput(e.target.value)}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-sky-500"
                placeholder="输入体重"
              />
            </div>

            <div>
              <label className="block text-slate-300 text-sm font-medium mb-2">
                备注 (可选)
              </label>
              <textarea
                value={noteInput}
                onChange={(e) => setNoteInput(e.target.value)}
                className="w-full bg-slate-700 border border-slate-600 rounded-lg px-4 py-2 text-white focus:outline-none focus:ring-2 focus:ring-sky-500 resize-none"
                rows={3}
                placeholder="添加备注..."
              />
            </div>

            <div className="flex space-x-3">
              <button
                onClick={handleSaveWeight}
                disabled={!weightInput}
                className="flex-1 bg-sky-600 hover:bg-sky-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white py-2 px-4 rounded-lg transition-colors"
              >
                {selectedDateRecord ? '更新记录' : '保存记录'}
              </button>
              
              {selectedDateRecord && (
                <button
                  onClick={handleDeleteWeight}
                  className="bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-lg transition-colors"
                >
                  删除
                </button>
              )}
            </div>
          </div>
        </div>

        {/* 体重趋势图 */}
        <WeightChart weightRecords={weightRecords} />
      </div>
    </div>
  );
};

export default WeightManagement;
