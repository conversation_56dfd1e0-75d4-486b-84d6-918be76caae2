const { contextBridge, ipc<PERSON><PERSON>er } = require('electron')

contextBridge.exposeInMainWorld('electronAPI', {
  exportData: (data) => ipcRenderer.invoke('export-data', data),
  importData: () => ipcRenderer.invoke('import-data'),
})

// 监听来自主进程的消息
ipcRenderer.on('open-add-journal-modal', () => {
  // 触发自定义事件，让React组件能够捕获
  document.dispatchEvent(new CustomEvent('electron-open-journal-modal'))
})