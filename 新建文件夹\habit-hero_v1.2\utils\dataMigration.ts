import { UserData } from '../types';

/**
 * Data migration utilities for upgrading from older versions
 */

interface LegacyUserData {
  points: number;
  tasks: any[];
  collectedCards?: Record<string, any>;
  journalEntries: any[];
}

const LEGACY_STORAGE_KEYS = [
  'habitHeroData_v3_cards_colors_journal_timeline',
  'habitHeroData_v2',
  'habitHeroData_v1',
  'habitHeroData'
];

const CURRENT_STORAGE_KEY = 'habitHeroData_v4_army_system';

/**
 * Migrate data from legacy storage to new army system
 */
export const migrateLegacyData = (): UserData | null => {
  // Check if current version data already exists
  const currentData = localStorage.getItem(CURRENT_STORAGE_KEY);
  if (currentData) {
    try {
      return JSON.parse(currentData);
    } catch (error) {
      console.error('Error parsing current data:', error);
    }
  }

  // Look for legacy data to migrate
  for (const legacyKey of LEGACY_STORAGE_KEYS) {
    const legacyData = localStorage.getItem(legacyKey);
    if (legacyData) {
      try {
        const parsed: LegacyUserData = JSON.parse(legacyData);
        const migratedData: UserData = {
          points: parsed.points || 100, // Give some starting points if none exist
          tasks: parsed.tasks || [],
          journalEntries: parsed.journalEntries || [],
          // Initialize army system data
          armyCards: [],
          campUnits: [],
          armyStylePrompt: '',
        };

        // Save migrated data to new storage key
        localStorage.setItem(CURRENT_STORAGE_KEY, JSON.stringify(migratedData));
        
        console.log(`Successfully migrated data from ${legacyKey} to ${CURRENT_STORAGE_KEY}`);
        
        // Optionally remove legacy data (commented out for safety)
        // localStorage.removeItem(legacyKey);
        
        return migratedData;
      } catch (error) {
        console.error(`Error migrating data from ${legacyKey}:`, error);
      }
    }
  }

  // No legacy data found, return null to use default initialization
  return null;
};

/**
 * Clean up old storage keys (call this after successful migration and user confirmation)
 */
export const cleanupLegacyStorage = (): void => {
  LEGACY_STORAGE_KEYS.forEach(key => {
    if (localStorage.getItem(key)) {
      localStorage.removeItem(key);
      console.log(`Cleaned up legacy storage key: ${key}`);
    }
  });
};

/**
 * Get storage usage information
 */
export const getStorageInfo = (): { 
  currentSize: number; 
  legacySize: number; 
  totalSize: number;
  keys: string[];
} => {
  let currentSize = 0;
  let legacySize = 0;
  const keys: string[] = [];

  // Calculate current data size
  const currentData = localStorage.getItem(CURRENT_STORAGE_KEY);
  if (currentData) {
    currentSize = new Blob([currentData]).size;
    keys.push(CURRENT_STORAGE_KEY);
  }

  // Calculate legacy data size
  LEGACY_STORAGE_KEYS.forEach(key => {
    const data = localStorage.getItem(key);
    if (data) {
      legacySize += new Blob([data]).size;
      keys.push(key);
    }
  });

  return {
    currentSize,
    legacySize,
    totalSize: currentSize + legacySize,
    keys
  };
};

/**
 * Export data for backup
 */
export const exportUserData = (): string => {
  const currentData = localStorage.getItem(CURRENT_STORAGE_KEY);
  if (currentData) {
    const parsed = JSON.parse(currentData);
    return JSON.stringify({
      version: 'v4_army_system',
      exportDate: new Date().toISOString(),
      data: parsed
    }, null, 2);
  }
  throw new Error('No data to export');
};

/**
 * Import data from backup
 */
export const importUserData = (backupData: string): UserData => {
  try {
    const parsed = JSON.parse(backupData);

    let userData: any;

    // Handle different backup formats
    if (parsed.version && parsed.data) {
      // New format with metadata
      userData = parsed.data;
    } else {
      // Legacy format - direct data
      userData = parsed;
    }

    // Migrate task data: convert old 'text' field to 'title'
    if (userData.tasks && Array.isArray(userData.tasks)) {
      userData.tasks = userData.tasks.map((task: any) => {
        const migratedTask = { ...task };

        // Convert text -> title
        if (task.text && !task.title) {
          migratedTask.title = task.text;
          delete migratedTask.text;
        }

        // Convert icon -> emoji
        if (task.icon && !task.emoji) {
          migratedTask.emoji = task.icon;
          delete migratedTask.icon;
        }

        // Ensure required fields exist
        if (!migratedTask.title) {
          migratedTask.title = '未命名任务';
        }

        // Ensure other required fields
        if (!migratedTask.type) {
          migratedTask.type = '每日';
        }

        if (!migratedTask.points) {
          migratedTask.points = 10;
        }

        if (!migratedTask.completedDates) {
          migratedTask.completedDates = [];
        }

        return migratedTask;
      });
    }

    // Migrate journal entry data: convert old 'text' field to 'content'
    if (userData.journalEntries && Array.isArray(userData.journalEntries)) {
      userData.journalEntries = userData.journalEntries.map((entry: any) => {
        const migratedEntry = { ...entry };

        // Convert text -> content
        if (entry.text && !entry.content) {
          migratedEntry.content = entry.text;
          delete migratedEntry.text;
        }

        // Convert entryDate -> date
        if (entry.entryDate && !entry.date) {
          migratedEntry.date = entry.entryDate;
          delete migratedEntry.entryDate;
        }

        // Ensure required fields exist
        if (!migratedEntry.content) {
          migratedEntry.content = '';
        }

        if (!migratedEntry.date) {
          migratedEntry.date = new Date().toISOString().split('T')[0];
        }

        return migratedEntry;
      });
    }

    // Ensure all required arrays exist
    if (!userData.tasks) userData.tasks = [];
    if (!userData.journalEntries) userData.journalEntries = [];
    if (!userData.armyCards) userData.armyCards = [];
    if (!userData.campUnits) userData.campUnits = [];

    // Ensure points exist
    if (typeof userData.points !== 'number') {
      userData.points = 100;
    }

    // Ensure totalPointsEarned exists
    if (typeof userData.totalPointsEarned !== 'number') {
      userData.totalPointsEarned = 0;
    }

    // Ensure armyStylePrompt exists
    if (!userData.armyStylePrompt) {
      userData.armyStylePrompt = '';
    }

    // Ensure camp info exists
    if (!userData.campName) {
      userData.campName = '';
    }

    if (!userData.flagUrl) {
      userData.flagUrl = '';
    }

    return userData as UserData;
  } catch (error) {
    console.error('Error parsing backup data:', error);
    throw new Error('Invalid backup data format');
  }
};

/**
 * Validate user data structure
 */
export const validateUserData = (data: any): data is UserData => {
  if (typeof data !== 'object' || data === null) {
    return false;
  }

  // Check basic structure
  if (typeof data.points !== 'number') {
    return false;
  }

  if (!Array.isArray(data.tasks)) {
    return false;
  }

  if (!Array.isArray(data.journalEntries)) {
    return false;
  }

  if (!Array.isArray(data.armyCards)) {
    return false;
  }

  if (!Array.isArray(data.campUnits)) {
    return false;
  }

  // Validate task structure
  for (const task of data.tasks) {
    if (!task.title || typeof task.title !== 'string') {
      return false;
    }
    if (!task.type || typeof task.type !== 'string') {
      return false;
    }
    if (typeof task.points !== 'number') {
      return false;
    }
  }

  // Validate journal entry structure
  for (const entry of data.journalEntries) {
    if (!entry.content || typeof entry.content !== 'string') {
      return false;
    }
    if (!entry.date || typeof entry.date !== 'string') {
      return false;
    }
  }

  return true;
};
