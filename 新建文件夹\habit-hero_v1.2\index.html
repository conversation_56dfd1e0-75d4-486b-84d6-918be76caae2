
<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Habit Hero</title>
  <script src="https://cdn.tailwindcss.com"></script>
  <style>
    body {
      font-family: 'Inter', sans-serif;
      background-color: #f0f4f8; /* Light blue-gray background */
    }
    /* For custom scrollbar aesthetics (optional, but nice) */
    ::-webkit-scrollbar {
      width: 8px;
    }
    ::-webkit-scrollbar-track {
      background: #f1f1f1;
    }
    ::-webkit-scrollbar-thumb {
      background: #888;
      border-radius: 4px;
    }
    ::-webkit-scrollbar-thumb:hover {
      background: #555;
    }
    .card-gradient-Common { background-image: linear-gradient(to top right, #e5e7eb, #d1d5db); } /* slate-200 to slate-300 */
    .card-gradient-Rare { background-image: linear-gradient(to top right, #60a5fa, #3b82f6); }
    .card-gradient-Epic { background-image: linear-gradient(to top right, #a855f7, #9333ea); }
    .card-gradient-Legendary { background-image: linear-gradient(to top right, #f97316, #ea580c); } /* orange-500 to orange-600 */
  </style>
   <link rel="preconnect" href="https://fonts.googleapis.com">
   <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
   <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Orbitron:wght@400;500;700&display=swap" rel="stylesheet">
<script type="importmap">
{
  "imports": {
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/",
    "uuid": "https://esm.sh/uuid@^11.1.0"
  }
}
</script>
<link rel="stylesheet" href="/index.css">
</head>
<body class="bg-slate-900 text-slate-100">
  <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>