const { contextBridge, ipc<PERSON><PERSON><PERSON> } = require('electron')

console.log('Preload script is loading...')

contextBridge.exposeInMainWorld('electronAPI', {
  exportData: (data) => ipcRenderer.invoke('export-data', data),
  importData: () => ipcRenderer.invoke('import-data'),
  hideWindow: () => ipcRenderer.invoke('hide-window'),
  database: {
    getUserData: () => ipcRenderer.invoke('db-get-user-data'),
    saveArmyCard: (card) => ipcRenderer.invoke('db-save-army-card', card),
    saveCampUnit: (unit) => ipcRenderer.invoke('db-save-camp-unit', unit),
    updateUserPoints: (points) => ipcRenderer.invoke('db-update-user-points', points),
    addTotalPoints: (pointsToAdd) => ipcRenderer.invoke('db-add-total-points', pointsToAdd),
    setTotalPoints: (totalPoints) => ipcRenderer.invoke('db-set-total-points', totalPoints),
    updateArmyStylePrompt: (prompt) => ipcRenderer.invoke('db-update-army-style-prompt', prompt),
    saveCampInfo: (campName, flagUrl) => ipcRenderer.invoke('db-save-camp-info', campName, flagUrl),
    saveAttributeSchema: (schema) => ipcRenderer.invoke('db-save-attribute-schema', schema),
    deleteArmyCard: (cardId) => ipcRenderer.invoke('db-delete-army-card', cardId),
    deleteCampUnit: (unitId) => ipcRenderer.invoke('db-delete-camp-unit', unitId),
    saveTask: (task) => ipcRenderer.invoke('db-save-task', task),
    deleteTask: (taskId) => ipcRenderer.invoke('db-delete-task', taskId),
    saveJournalEntry: (entry) => ipcRenderer.invoke('db-save-journal-entry', entry),
    deleteJournalEntry: (entryId) => ipcRenderer.invoke('db-delete-journal-entry', entryId),
    saveWeightRecord: (record) => ipcRenderer.invoke('db-save-weight-record', record),
    deleteWeightRecord: (id) => ipcRenderer.invoke('db-delete-weight-record', id),
    getStats: () => ipcRenderer.invoke('db-get-stats'),
    getArmyCardsPaginated: (page, limit) => ipcRenderer.invoke('db-get-army-cards-paginated', page, limit),
    saveCustomClass: (customClass) => ipcRenderer.invoke('db-save-custom-class', customClass),
    getCustomClasses: () => ipcRenderer.invoke('db-get-custom-classes'),
    deleteCustomClass: (classId) => ipcRenderer.invoke('db-delete-custom-class', classId)
  },
  exportData: (data) => ipcRenderer.invoke('export-data', data),
  importData: () => ipcRenderer.invoke('import-data'),
  saveAppSettings: (settings) => ipcRenderer.invoke('save-app-settings', settings),
  loadAppSettings: () => ipcRenderer.invoke('load-app-settings'),
  getCurrentDatabasePath: () => ipcRenderer.invoke('get-current-database-path'),

  // 监听主进程发送的事件
  on: (channel, callback) => {
    ipcRenderer.on(channel, callback);
  },
  removeListener: (channel, callback) => {
    ipcRenderer.removeListener(channel, callback);
  }
})

// 监听来自主进程的托盘事件
ipcRenderer.on('open-add-journal-modal', () => {
  // 发送自定义事件到渲染进程
  const event = new CustomEvent('electron-open-journal-modal');
  document.dispatchEvent(event);
});

console.log('Preload script loaded successfully, electronAPI exposed to window')

// 监听来自主进程的消息
ipcRenderer.on('open-add-journal-modal', () => {
  // 触发自定义事件，让React组件能够捕获
  document.dispatchEvent(new CustomEvent('electron-open-journal-modal'))
})
