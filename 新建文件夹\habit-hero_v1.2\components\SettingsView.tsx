import React, { useState, useEffect } from 'react';
import { UserData } from '../types';
import { useTheme } from '../contexts/ThemeContext';
import { getAvailableModels, getCurrentModel, setCurrentModel } from '../services/geminiService';
import { generateCardGalleryHTML } from '../utils/cardGalleryExporter';
import {
  UploadIcon,
  DownloadIcon,
  TrashIcon,
  ExclamationTriangleIcon,
  SunIcon,
  MoonIcon
} from './icons';

interface SettingsViewProps {
  userData: UserData;
  onExportData: () => void;
  onImportData: () => void;
  onClearDatabase: () => void;
  isUsingDatabase: boolean;
  onOpenApiConfig: () => void;
}

interface AppSettings {
  theme: 'dark' | 'light' | 'auto';
  notifications: boolean;
  autoSave: boolean;
  animationsEnabled: boolean;
  geminiModel: string;
  databasePath: string;
  summonCost: number;
  assessmentCost: number;
}

const SettingsView: React.FC<SettingsViewProps> = ({
  userData,
  onExportData,
  onImportData,
  onClearDatabase,
  isUsingDatabase,
  onOpenApiConfig
}) => {
  const { theme, toggleTheme, setTheme } = useTheme();
  const [showClearConfirm, setShowClearConfirm] = useState(false);
  const [availableModels, setAvailableModels] = useState<string[]>([]);
  const [loadingModels, setLoadingModels] = useState(false);
  const [showPathInput, setShowPathInput] = useState(false);
  const [tempDatabasePath, setTempDatabasePath] = useState('');
  const [isTestingGemini, setIsTestingGemini] = useState(false);
  const [geminiTestResult, setGeminiTestResult] = useState<string | null>(null);
  const [showApiKeyInput, setShowApiKeyInput] = useState(false);
  const [tempApiKey, setTempApiKey] = useState('');
  const [settings, setSettings] = useState<AppSettings>(() => {
    const saved = localStorage.getItem('app-settings');
    return saved ? JSON.parse(saved) : {
      theme: theme as 'dark' | 'light' | 'auto',
      notifications: true,
      autoSave: true,
      animationsEnabled: true,
      geminiModel: getCurrentModel(),
      databasePath: '', // 空字符串表示使用默认路径
      summonCost: 10, // 默认召唤消耗10积分
      assessmentCost: 50 // 默认战力评估消耗50积分
    };
  });

  const updateSetting = async <K extends keyof AppSettings>(key: K, value: AppSettings[K]) => {
    const newSettings = { ...settings, [key]: value };
    setSettings(newSettings);
    localStorage.setItem('app-settings', JSON.stringify(newSettings));

    // Also save to file system if in Electron environment
    if (window.electronAPI?.saveAppSettings) {
      try {
        await window.electronAPI.saveAppSettings(newSettings);
      } catch (error) {
        console.error('Failed to save settings to file system:', error);
      }
    }

    // 如果更新的是Gemini模型，同时更新服务中的配置
    if (key === 'geminiModel') {
      setCurrentModel(value as string);
    }
  };

  // 加载可用模型列表和当前数据库路径
  useEffect(() => {
    const loadModels = async () => {
      setLoadingModels(true);
      try {
        const models = await getAvailableModels();
        setAvailableModels(models);
      } catch (error) {
        console.error('Failed to load models:', error);
      } finally {
        setLoadingModels(false);
      }
    };

    const loadCurrentDatabasePath = async () => {
      if (window.electronAPI?.getCurrentDatabasePath) {
        try {
          const result = await window.electronAPI.getCurrentDatabasePath();
          if (result.success && result.path) {
            // 如果当前路径不是默认路径，则显示在输入框中
            if (!result.path.includes('userData')) {
              setSettings(prev => ({ ...prev, databasePath: result.path }));
            }
          }
        } catch (error) {
          console.error('Failed to get current database path:', error);
        }
      }
    };

    loadModels();
    loadCurrentDatabasePath();
  }, []);

  // 保存API Key
  const saveApiKey = () => {
    if (tempApiKey.trim()) {
      updateSetting('geminiApiKey', tempApiKey.trim());
      setShowApiKeyInput(false);
      setTempApiKey('');

      // 更新geminiService中的API Key
      import('../services/geminiService').then(({ updateApiKey }) => {
        updateApiKey(tempApiKey.trim());
      });
    }
  };

  // 清除API Key
  const clearApiKey = () => {
    updateSetting('geminiApiKey', '');

    // 清除geminiService中的API Key
    import('../services/geminiService').then(({ updateApiKey }) => {
      updateApiKey('');
    });
  };

  // 测试Gemini连接
  const testGeminiConnection = async () => {
    setIsTestingGemini(true);
    setGeminiTestResult(null);

    try {
      // 导入generateContent函数
      const { generateContent } = await import('../services/geminiService');

      // 发送简单的测试消息
      const testPrompt = "请回复'连接成功'来确认API工作正常。";
      const response = await generateContent(testPrompt);

      if (response && response.trim()) {
        setGeminiTestResult(`✅ 连接成功！响应: ${response.slice(0, 100)}${response.length > 100 ? '...' : ''}`);
      } else {
        setGeminiTestResult('❌ 连接失败：收到空响应');
      }
    } catch (error) {
      console.error('Gemini test failed:', error);
      setGeminiTestResult(`❌ 连接失败：${error instanceof Error ? error.message : '未知错误'}`);
    } finally {
      setIsTestingGemini(false);
      // 5秒后清除结果
      setTimeout(() => setGeminiTestResult(null), 5000);
    }
  };

  const handleClearDatabase = () => {
    if (showClearConfirm) {
      onClearDatabase();
      setShowClearConfirm(false);
    } else {
      setShowClearConfirm(true);
    }
  };

  const handleExportCardGallery = () => {
    if (userData.armyCards.length === 0) {
      alert('没有卡牌可以导出');
      return;
    }

    try {
      const htmlContent = generateCardGalleryHTML(userData.armyCards, userData.campName);

      // 创建下载链接
      const blob = new Blob([htmlContent], { type: 'text/html;charset=utf-8' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `habit-hero-cards-${new Date().toISOString().split('T')[0]}.html`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      alert('卡牌展示网页已导出成功！');
    } catch (error) {
      console.error('导出失败:', error);
      alert('导出失败，请重试');
    }
  };

  const stats = {
    tasks: userData.tasks.length,
    journalEntries: userData.journalEntries.length,
    armyCards: userData.armyCards.length,
    campUnits: userData.campUnits.length,
    points: userData.points,
    totalPointsEarned: userData.totalPointsEarned || 0
  };

  return (
    <div className="space-y-6">
      {/* 标题 */}
      <div className="text-center">
        <h1 className="text-3xl font-bold text-transparent bg-clip-text bg-gradient-to-r from-purple-400 to-pink-400">
          设置
        </h1>
        <p className="text-slate-400 mt-2">
          数据管理和应用设置
        </p>
      </div>

      {/* 数据统计 */}
      <div className="bg-slate-800/50 rounded-lg p-6 border border-slate-700">
        <h2 className="text-xl font-semibold text-sky-300 mb-4">数据统计</h2>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-sky-400">{stats.tasks}</div>
            <div className="text-sm text-slate-400">任务</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-purple-400">{stats.journalEntries}</div>
            <div className="text-sm text-slate-400">日志</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-400">{stats.armyCards}</div>
            <div className="text-sm text-slate-400">军队卡片</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-orange-400">{stats.campUnits}</div>
            <div className="text-sm text-slate-400">营地单位</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-400">{stats.points}</div>
            <div className="text-sm text-slate-400">当前积分</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-amber-400">{stats.totalPointsEarned}</div>
            <div className="text-sm text-slate-400">总获得积分</div>
          </div>
          <div className="text-center col-span-2 md:col-span-1">
            <div className="text-sm font-medium text-slate-300">
              {isUsingDatabase ? 'SQLite 数据库' : 'localStorage'}
            </div>
            <div className="text-xs text-slate-400">存储方式</div>
          </div>
        </div>
      </div>

      {/* 数据管理 */}
      <div className="bg-slate-800/50 rounded-lg p-6 border border-slate-700">
        <h2 className="text-xl font-semibold text-sky-300 mb-4">数据管理</h2>
        <div className="space-y-4">
          
          {/* 导出数据 */}
          <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg">
            <div>
              <h3 className="font-medium text-slate-200">导出数据</h3>
              <p className="text-sm text-slate-400">将所有数据导出为 JSON 文件</p>
            </div>
            <button
              onClick={onExportData}
              className="flex items-center space-x-2 px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
            >
              <DownloadIcon className="w-5 h-5" />
              <span>导出</span>
            </button>
          </div>

          {/* 导入数据 */}
          <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg">
            <div>
              <h3 className="font-medium text-slate-200">导入数据</h3>
              <p className="text-sm text-slate-400">从 JSON 文件导入数据（会覆盖现有数据）</p>
            </div>
            <button
              onClick={onImportData}
              className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
            >
              <UploadIcon className="w-5 h-5" />
              <span>导入</span>
            </button>
          </div>

          {/* 导出卡牌展示网页 */}
          <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg">
            <div>
              <h3 className="font-medium text-slate-200">导出卡牌展示网页</h3>
              <p className="text-sm text-slate-400">
                生成美观的HTML网页展示所有军队卡牌，便于分享和查看
              </p>
            </div>
            <button
              onClick={handleExportCardGallery}
              disabled={userData.armyCards.length === 0}
              className="flex items-center space-x-2 px-4 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors"
            >
              <span>🎴</span>
              <span>导出网页</span>
            </button>
          </div>

          {/* 清空数据库 */}
          <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg border border-red-500/20">
            <div>
              <h3 className="font-medium text-red-400">清空数据库</h3>
              <p className="text-sm text-slate-400">删除所有数据（不可恢复）</p>
            </div>
            <button
              onClick={handleClearDatabase}
              className={`flex items-center space-x-2 px-4 py-2 rounded-lg transition-colors ${
                showClearConfirm 
                  ? 'bg-red-600 hover:bg-red-700 text-white' 
                  : 'bg-red-600/20 hover:bg-red-600/30 text-red-400 border border-red-500/50'
              }`}
            >
              {showClearConfirm ? (
                <>
                  <ExclamationTriangleIcon className="w-5 h-5" />
                  <span>确认清空</span>
                </>
              ) : (
                <>
                  <TrashIcon className="w-5 h-5" />
                  <span>清空数据</span>
                </>
              )}
            </button>
          </div>

          {showClearConfirm && (
            <div className="p-4 bg-red-900/20 border border-red-500/50 rounded-lg">
              <div className="flex items-center space-x-2 text-red-400 mb-2">
                <ExclamationTriangleIcon className="w-5 h-5" />
                <span className="font-medium">警告</span>
              </div>
              <p className="text-sm text-red-300 mb-3">
                此操作将永久删除所有数据，包括任务、日志、军队卡片和营地单位。此操作不可撤销！
              </p>
              <div className="flex space-x-3">
                <button
                  onClick={() => setShowClearConfirm(false)}
                  className="px-3 py-1 bg-slate-600 hover:bg-slate-700 text-slate-200 rounded text-sm transition-colors"
                >
                  取消
                </button>
                <button
                  onClick={handleClearDatabase}
                  className="px-3 py-1 bg-red-600 hover:bg-red-700 text-white rounded text-sm transition-colors"
                >
                  确认清空
                </button>
              </div>
            </div>
          )}
        </div>
      </div>



      {/* 应用设置 */}
      <div className="bg-slate-800/50 rounded-lg p-6 border border-slate-700">
        <h2 className="text-xl font-semibold text-sky-300 mb-4">应用设置</h2>
        <div className="space-y-4">

          {/* 主题设置 */}
          <div className="p-6 bg-gradient-to-r from-slate-700/50 to-slate-600/50 rounded-xl border border-slate-600/50">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="font-semibold text-slate-200 text-lg">主题设置</h3>
                <p className="text-sm text-slate-400 mt-1">选择您喜欢的应用主题</p>
              </div>
              <div className="flex items-center space-x-2">
                {theme === 'dark' ? (
                  <MoonIcon className="w-6 h-6 text-slate-400" />
                ) : (
                  <SunIcon className="w-6 h-6 text-yellow-400" />
                )}
              </div>
            </div>

            <div className="flex space-x-3">
              <button
                onClick={() => setTheme('light')}
                className={`group flex-1 flex items-center justify-center space-x-2 p-4 rounded-xl transition-all duration-300 ${
                  theme === 'light'
                    ? 'bg-gradient-to-r from-yellow-500/20 to-orange-500/20 border-2 border-yellow-500/50 text-yellow-400 shadow-lg shadow-yellow-500/10'
                    : 'bg-slate-600/30 border-2 border-slate-600/50 text-slate-400 hover:bg-slate-600/50 hover:border-slate-500/50 hover:text-slate-300'
                }`}
              >
                <SunIcon className={`w-5 h-5 transition-transform duration-300 ${theme === 'light' ? 'scale-110' : 'group-hover:scale-110'}`} />
                <span className="font-medium">浅色</span>
              </button>

              <button
                onClick={() => setTheme('dark')}
                className={`group flex-1 flex items-center justify-center space-x-2 p-4 rounded-xl transition-all duration-300 ${
                  theme === 'dark'
                    ? 'bg-gradient-to-r from-slate-500/20 to-slate-600/20 border-2 border-slate-400/50 text-slate-300 shadow-lg shadow-slate-500/10'
                    : 'bg-slate-600/30 border-2 border-slate-600/50 text-slate-400 hover:bg-slate-600/50 hover:border-slate-500/50 hover:text-slate-300'
                }`}
              >
                <MoonIcon className={`w-5 h-5 transition-transform duration-300 ${theme === 'dark' ? 'scale-110' : 'group-hover:scale-110'}`} />
                <span className="font-medium">深色</span>
              </button>
            </div>

            <div className="mt-4 p-3 bg-slate-800/50 rounded-lg">
              <p className="text-xs text-slate-400 text-center">
                当前主题: <span className="text-sky-400 font-medium">{theme === 'dark' ? '深色模式' : '浅色模式'}</span>
              </p>
            </div>
          </div>

          {/* Gemini模型设置 */}
          <div className="p-6 bg-gradient-to-r from-slate-700/50 to-slate-600/50 rounded-xl border border-slate-600/50">
            <div className="flex items-center justify-between mb-4">
              <div>
                <h3 className="font-semibold text-slate-200 text-lg">AI模型设置</h3>
                <p className="text-sm text-slate-400 mt-1">选择用于军队卡片生成的Gemini模型</p>
              </div>
              <div className="flex items-center space-x-2">
                <span className="text-lg">🤖</span>
              </div>
            </div>

            <div className="space-y-4">
              {/* API Key 管理 */}
              <div className="p-4 bg-slate-700/30 rounded-lg border border-slate-600/50">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-slate-200">API 密钥管理</h4>
                  <div className="flex items-center space-x-2">
                    {settings.geminiApiKey ? (
                      <span className="text-xs text-green-400">✓ 已配置</span>
                    ) : (
                      <span className="text-xs text-yellow-400">⚠ 未配置</span>
                    )}
                  </div>
                </div>

                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-slate-300">
                      当前状态: {settings.geminiApiKey ? '使用自定义密钥' : '使用默认配置'}
                    </span>
                    {settings.geminiApiKey && (
                      <button
                        onClick={clearApiKey}
                        className="text-xs text-red-400 hover:text-red-300 transition-colors"
                      >
                        清除密钥
                      </button>
                    )}
                  </div>

                  {!showApiKeyInput ? (
                    <button
                      onClick={() => setShowApiKeyInput(true)}
                      className="w-full py-2 px-3 text-sm bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors"
                    >
                      {settings.geminiApiKey ? '更新 API 密钥' : '设置 API 密钥'}
                    </button>
                  ) : (
                    <div className="space-y-2">
                      <input
                        type="password"
                        value={tempApiKey}
                        onChange={(e) => setTempApiKey(e.target.value)}
                        placeholder="输入您的 Gemini API Key"
                        className="w-full p-2 text-sm bg-slate-600 text-white rounded border border-slate-500 focus:outline-none focus:ring-2 focus:ring-sky-500"
                      />
                      <div className="flex space-x-2">
                        <button
                          onClick={saveApiKey}
                          disabled={!tempApiKey.trim()}
                          className="flex-1 py-1 px-3 text-sm bg-green-600 hover:bg-green-700 disabled:bg-slate-600 disabled:cursor-not-allowed text-white rounded transition-colors"
                        >
                          保存
                        </button>
                        <button
                          onClick={() => {
                            setShowApiKeyInput(false);
                            setTempApiKey('');
                          }}
                          className="flex-1 py-1 px-3 text-sm bg-slate-600 hover:bg-slate-500 text-white rounded transition-colors"
                        >
                          取消
                        </button>
                      </div>
                    </div>
                  )}

                  <p className="text-xs text-slate-400">
                    💡 设置自己的API密钥可以避免配额限制。获取密钥:
                    <a
                      href="https://aistudio.google.com/app/apikey"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-sky-400 hover:text-sky-300 underline ml-1"
                    >
                      Google AI Studio
                    </a>
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <span className="text-sm text-slate-300">当前模型:</span>
                <span className="text-sm text-sky-400 font-medium">{settings.geminiModel}</span>
              </div>

              <div>
                <label htmlFor="gemini-model" className="block text-sm font-medium text-slate-300 mb-2">
                  选择模型
                </label>
                <select
                  id="gemini-model"
                  value={settings.geminiModel}
                  onChange={(e) => updateSetting('geminiModel', e.target.value)}
                  disabled={loadingModels}
                  className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {loadingModels ? (
                    <option>加载中...</option>
                  ) : (
                    availableModels.map(model => (
                      <option key={model} value={model}>
                        {model}
                      </option>
                    ))
                  )}
                </select>
              </div>

              {/* Gemini连接测试 */}
              <div className="space-y-3">
                <button
                  onClick={testGeminiConnection}
                  disabled={isTestingGemini}
                  className={`w-full py-2 px-4 rounded-lg font-medium text-white transition-all duration-200 text-sm ${
                    !isTestingGemini
                      ? 'bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 shadow-md hover:scale-105'
                      : 'bg-slate-600 cursor-not-allowed opacity-70'
                  }`}
                >
                  {isTestingGemini ? (
                    <div className="flex items-center justify-center space-x-2">
                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                      <span>测试连接中...</span>
                    </div>
                  ) : (
                    '🔗 测试Gemini连接'
                  )}
                </button>

                {/* 测试结果显示 */}
                {geminiTestResult && (
                  <div className={`p-3 rounded-lg text-sm ${
                    geminiTestResult.startsWith('✅')
                      ? 'bg-green-900/30 border border-green-500/30 text-green-300'
                      : 'bg-red-900/30 border border-red-500/30 text-red-300'
                  }`}>
                    {geminiTestResult}
                  </div>
                )}
              </div>

              {/* API配置管理 */}
              <div className="space-y-3">
                <button
                  onClick={onOpenApiConfig}
                  className="w-full py-3 px-4 rounded-lg font-medium text-white transition-all duration-200 text-sm bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 shadow-md hover:scale-105"
                >
                  🔧 高级API配置
                </button>
                <p className="text-xs text-slate-400 text-center">
                  配置多个API供应商，分别设置文本和图片生成来源
                </p>
              </div>

              <div className="mt-4 p-3 bg-slate-800/50 rounded-lg">
                <p className="text-xs text-slate-400">
                  💡 不同模型在生成质量和速度上可能有所差异。推荐使用 gemini-2.5-flash 获得最佳性能。
                </p>
              </div>

              {loadingModels && (
                <div className="flex items-center justify-center py-2">
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-sky-400"></div>
                  <span className="ml-2 text-sm text-slate-400">正在获取可用模型...</span>
                </div>
              )}
            </div>
          </div>

          {/* 通知设置 */}
          <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg">
            <div>
              <h3 className="font-medium text-slate-200">通知</h3>
              <p className="text-sm text-slate-400">启用任务提醒和完成通知</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.notifications}
                onChange={(e) => updateSetting('notifications', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          {/* 自动保存设置 */}
          <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg">
            <div>
              <h3 className="font-medium text-slate-200">自动保存</h3>
              <p className="text-sm text-slate-400">自动保存数据更改</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.autoSave}
                onChange={(e) => updateSetting('autoSave', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>

          {/* 动画设置 */}
          <div className="flex items-center justify-between p-4 bg-slate-700/50 rounded-lg">
            <div>
              <h3 className="font-medium text-slate-200">动画效果</h3>
              <p className="text-sm text-slate-400">启用界面动画和过渡效果</p>
            </div>
            <label className="relative inline-flex items-center cursor-pointer">
              <input
                type="checkbox"
                checked={settings.animationsEnabled}
                onChange={(e) => updateSetting('animationsEnabled', e.target.checked)}
                className="sr-only peer"
              />
              <div className="w-11 h-6 bg-slate-600 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-purple-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-purple-600"></div>
            </label>
          </div>
        </div>
      </div>

      {/* 高级设置 */}
      <div className="p-6 bg-gradient-to-r from-slate-700/50 to-slate-600/50 rounded-xl border border-slate-600/50">
        <div className="flex items-center justify-between mb-6">
          <div>
            <h3 className="font-semibold text-slate-200 text-lg">高级设置</h3>
            <p className="text-sm text-slate-400 mt-1">数据库和游戏参数配置</p>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-lg">⚙️</span>
          </div>
        </div>

        <div className="space-y-6">
          {/* 数据库路径设置 */}
          <div className="space-y-3">
            <label htmlFor="database-path" className="block text-sm font-medium text-slate-300">
              数据库路径 {isUsingDatabase && <span className="text-xs text-slate-500">(仅桌面版)</span>}
            </label>
            <div className="flex space-x-3">
              <input
                id="database-path"
                type="text"
                value={settings.databasePath}
                onChange={(e) => updateSetting('databasePath', e.target.value)}
                placeholder="留空使用默认路径"
                disabled={!isUsingDatabase}
                className="flex-1 p-3 bg-slate-700 border border-slate-600 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100 placeholder-slate-400 disabled:opacity-50 disabled:cursor-not-allowed"
              />
              {isUsingDatabase && (
                <button
                  onClick={() => {
                    setTempDatabasePath(settings.databasePath || '');
                    setShowPathInput(true);
                  }}
                  className="px-4 py-3 bg-sky-600 hover:bg-sky-700 text-white rounded-lg transition-colors text-sm font-medium"
                >
                  设置路径
                </button>
              )}
            </div>
            <p className="text-xs text-slate-400">
              {isUsingDatabase
                ? '自定义数据库存储位置。留空将使用默认路径。更改后需要重启应用。'
                : '浏览器版本使用 localStorage，无需配置数据库路径。'
              }
            </p>
          </div>

          {/* 召唤积分消耗设置 */}
          <div className="space-y-3">
            <label htmlFor="summon-cost" className="block text-sm font-medium text-slate-300">
              召唤消耗积分
            </label>
            <div className="flex items-center space-x-4">
              <input
                id="summon-cost"
                type="number"
                min="1"
                max="100"
                value={settings.summonCost}
                onChange={(e) => updateSetting('summonCost', Math.max(1, Math.min(100, parseInt(e.target.value) || 10)))}
                className="w-24 p-3 bg-slate-700 border border-slate-600 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100 text-center"
              />
              <span className="text-slate-300">积分/次</span>
              <div className="flex space-x-2">
                <button
                  onClick={() => updateSetting('summonCost', Math.max(1, settings.summonCost - 1))}
                  className="w-8 h-8 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors flex items-center justify-center"
                >
                  -
                </button>
                <button
                  onClick={() => updateSetting('summonCost', Math.min(100, settings.summonCost + 1))}
                  className="w-8 h-8 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors flex items-center justify-center"
                >
                  +
                </button>
              </div>
            </div>
            <p className="text-xs text-slate-400">
              设置每次召唤新兵需要消耗的积分数量 (1-100)。当前设置: {settings.summonCost} 积分
            </p>
          </div>

          {/* 战力评估积分消耗设置 */}
          <div className="space-y-3">
            <label htmlFor="assessment-cost" className="block text-sm font-medium text-slate-300">
              战力评估消耗积分
            </label>
            <div className="flex items-center space-x-4">
              <input
                id="assessment-cost"
                type="number"
                min="1"
                max="500"
                value={settings.assessmentCost}
                onChange={(e) => updateSetting('assessmentCost', Math.max(1, Math.min(500, parseInt(e.target.value) || 50)))}
                className="w-24 p-3 bg-slate-700 border border-slate-600 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100 text-center"
              />
              <span className="text-slate-300">积分/次</span>
              <div className="flex space-x-2">
                <button
                  onClick={() => updateSetting('assessmentCost', Math.max(1, settings.assessmentCost - 5))}
                  className="w-8 h-8 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors flex items-center justify-center"
                >
                  -5
                </button>
                <button
                  onClick={() => updateSetting('assessmentCost', Math.min(500, settings.assessmentCost + 5))}
                  className="w-8 h-8 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors flex items-center justify-center"
                >
                  +5
                </button>
              </div>
            </div>
            <p className="text-xs text-slate-400">
              设置每次AI战力评估需要消耗的积分数量 (1-500)。当前设置: {settings.assessmentCost} 积分
            </p>
          </div>

          {/* 重置按钮 */}
          <div className="pt-4 border-t border-slate-600">
            <button
              onClick={() => {
                if (confirm('确定要重置高级设置为默认值吗？')) {
                  updateSetting('databasePath', '');
                  updateSetting('summonCost', 10);
                  updateSetting('assessmentCost', 50);
                }
              }}
              className="px-4 py-2 bg-slate-600 hover:bg-slate-500 text-white rounded-lg transition-colors text-sm"
            >
              重置为默认值
            </button>
          </div>
        </div>
      </div>

      {/* 应用信息 */}
      <div className="bg-slate-800/50 rounded-lg p-6 border border-slate-700">
        <h2 className="text-xl font-semibold text-sky-300 mb-4">应用信息</h2>
        <div className="space-y-2 text-sm text-slate-400">
          <div>版本: 1.2.0</div>
          <div>环境: {isUsingDatabase ? 'Electron (桌面版)' : '浏览器版'}</div>
          <div>数据存储: {isUsingDatabase ? 'SQLite 数据库' : 'localStorage'}</div>
          {isUsingDatabase && (
            <div>数据库路径: {settings.databasePath || '默认路径'}</div>
          )}
          <div>主题: {settings.theme === 'dark' ? '深色' : settings.theme === 'light' ? '浅色' : '跟随系统'}</div>
          <div>通知: {settings.notifications ? '已启用' : '已禁用'}</div>
          <div>自动保存: {settings.autoSave ? '已启用' : '已禁用'}</div>
          <div>动画效果: {settings.animationsEnabled ? '已启用' : '已禁用'}</div>
          <div>召唤消耗: {settings.summonCost} 积分/次</div>
          <div>战力评估: {settings.assessmentCost} 积分/次</div>
        </div>
      </div>

      {/* 数据库路径设置模态框 */}
      {showPathInput && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-slate-800 rounded-xl p-6 max-w-md w-full mx-4 border border-slate-700">
            <h3 className="text-xl font-bold text-white mb-4">设置数据库路径</h3>
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-300 mb-2">
                  数据库文件路径
                </label>
                <input
                  type="text"
                  value={tempDatabasePath}
                  onChange={(e) => setTempDatabasePath(e.target.value)}
                  placeholder="例如: C:\MyData\habit-hero.db (留空使用默认路径)"
                  className="w-full p-3 bg-slate-700 border border-slate-600 rounded-lg focus:ring-2 focus:ring-sky-500 focus:border-sky-500 outline-none transition-colors text-slate-100 placeholder-slate-400"
                />
              </div>
              <div className="text-sm text-slate-400">
                <p>• 留空将使用默认路径</p>
                <p>• 路径应包含文件名（.db扩展名）</p>
                <p>• 更改后需要重启应用程序</p>
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => {
                    updateSetting('databasePath', tempDatabasePath.trim());
                    setShowPathInput(false);
                    setTempDatabasePath('');
                    if (tempDatabasePath.trim()) {
                      alert('数据库路径已更新。请重启应用程序以使更改生效。');
                    }
                  }}
                  className="flex-1 px-4 py-2 bg-sky-600 hover:bg-sky-700 text-white rounded-lg transition-colors font-medium"
                >
                  确定
                </button>
                <button
                  onClick={() => {
                    setShowPathInput(false);
                    setTempDatabasePath('');
                  }}
                  className="flex-1 px-4 py-2 bg-slate-600 hover:bg-slate-700 text-white rounded-lg transition-colors font-medium"
                >
                  取消
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default SettingsView;
