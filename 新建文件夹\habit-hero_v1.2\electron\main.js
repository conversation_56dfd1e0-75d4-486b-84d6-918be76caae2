import { app, BrowserWindow, ipc<PERSON><PERSON>, dialog, Tray, Menu, nativeImage } from 'electron'
import path from 'path'
import { fileURLToPath } from 'url'
import fs from 'fs'
import Database from 'better-sqlite3'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

let mainWindow = null
let tray = null
let db = null

// Get database path from settings or use default
function getDatabasePath() {
  try {
    // Try to read settings from a settings file
    const settingsPath = path.join(app.getPath('userData'), 'app-settings.json')
    if (fs.existsSync(settingsPath)) {
      const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
      if (settings.databasePath && settings.databasePath.trim()) {
        let customDbPath = path.resolve(settings.databasePath)

        // Check if the path is a directory, if so, append the default filename
        if (fs.existsSync(customDbPath) && fs.statSync(customDbPath).isDirectory()) {
          customDbPath = path.join(customDbPath, 'habit-hero.db')
        }

        // Ensure the directory exists
        const customDbDir = path.dirname(customDbPath)
        if (!fs.existsSync(customDbDir)) {
          fs.mkdirSync(customDbDir, { recursive: true })
        }
        return customDbPath
      }
    }
  } catch (error) {
    console.warn('Failed to read custom database path from settings:', error)
  }

  // Default path
  return path.join(app.getPath('userData'), 'habit-hero.db')
}

// Initialize SQLite database
function initializeDatabase() {
  try {
    const dbPath = getDatabasePath()
    console.log('Database path:', dbPath)

    // Ensure the directory exists
    const dbDir = path.dirname(dbPath)
    if (!fs.existsSync(dbDir)) {
      console.log('Creating database directory:', dbDir)
      fs.mkdirSync(dbDir, { recursive: true })
    }

    // Check if we're trying to open a directory instead of a file
    if (fs.existsSync(dbPath) && fs.statSync(dbPath).isDirectory()) {
      throw new Error(`Database path is a directory, not a file: ${dbPath}`)
    }

    db = new Database(dbPath)

    // Enable WAL mode for better performance
    db.pragma('journal_mode = WAL')
    db.pragma('foreign_keys = ON')

    // Create tables
    createTables()

    console.log('SQLite database initialized successfully')
  } catch (error) {
    console.error('Failed to initialize database:', error)

    // If database initialization fails, try to use default path as fallback
    if (error.code === 'SQLITE_CANTOPEN_ISDIR' || error.message.includes('directory')) {
      console.log('Attempting to use default database path as fallback...')
      try {
        const defaultDbPath = path.join(app.getPath('userData'), 'habit-hero.db')
        console.log('Fallback database path:', defaultDbPath)

        const defaultDbDir = path.dirname(defaultDbPath)
        if (!fs.existsSync(defaultDbDir)) {
          fs.mkdirSync(defaultDbDir, { recursive: true })
        }

        db = new Database(defaultDbPath)
        db.pragma('journal_mode = WAL')
        db.pragma('foreign_keys = ON')
        createTables()

        console.log('Fallback database initialized successfully')
        return
      } catch (fallbackError) {
        console.error('Fallback database initialization also failed:', fallbackError)
      }
    }

    throw error
  }
}

function createTables() {
  // User settings table
  db.exec(`
    CREATE TABLE IF NOT EXISTS user_settings (
      id INTEGER PRIMARY KEY,
      points INTEGER DEFAULT 100,
      army_style_prompt TEXT DEFAULT '',
      camp_name TEXT DEFAULT '',
      flag_url TEXT DEFAULT '',
      attribute_schema TEXT, -- JSON string for AttributeSchema
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // Add new columns if they don't exist (for existing databases)
  try {
    db.exec(`ALTER TABLE user_settings ADD COLUMN camp_name TEXT DEFAULT ''`)
  } catch (e) {
    // Column already exists
  }
  try {
    db.exec(`ALTER TABLE user_settings ADD COLUMN flag_url TEXT DEFAULT ''`)
  } catch (e) {
    // Column already exists
  }
  try {
    db.exec(`ALTER TABLE user_settings ADD COLUMN total_points_earned INTEGER DEFAULT 0`)
  } catch (e) {
    // Column already exists
  }

  // Ensure default user settings exist
  const existingSettings = db.prepare('SELECT id FROM user_settings WHERE id = 1').get()
  if (!existingSettings) {
    console.log('Creating default user settings...')
    db.prepare(`
      INSERT INTO user_settings (id, points, army_style_prompt, camp_name, flag_url, total_points_earned)
      VALUES (1, 100, '', '', '', 0)
    `).run()
    console.log('Default user settings created')
  }

  // Tasks table
  db.exec(`
    CREATE TABLE IF NOT EXISTS tasks (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL,
      description TEXT,
      emoji TEXT,
      points INTEGER DEFAULT 10,
      type TEXT DEFAULT 'daily',
      category TEXT,
      is_highlighted INTEGER DEFAULT 0,
      due_date TEXT,
      is_completed INTEGER DEFAULT 0,
      actual_completion_date TEXT,
      days_of_week TEXT,
      completed_dates TEXT,
      times_per_week INTEGER,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // Add missing columns to tasks table for existing databases
  try {
    db.exec(`ALTER TABLE tasks ADD COLUMN due_date TEXT`)
  } catch (e) {
    // Column already exists
  }
  try {
    db.exec(`ALTER TABLE tasks ADD COLUMN is_completed INTEGER DEFAULT 0`)
  } catch (e) {
    // Column already exists
  }
  try {
    db.exec(`ALTER TABLE tasks ADD COLUMN actual_completion_date TEXT`)
  } catch (e) {
    // Column already exists
  }
  try {
    db.exec(`ALTER TABLE tasks ADD COLUMN times_per_week INTEGER`)
  } catch (e) {
    // Column already exists
  }
  try {
    db.exec(`ALTER TABLE tasks ADD COLUMN is_highlighted INTEGER DEFAULT 0`)
  } catch (e) {
    // Column already exists
  }

  // Journal entries table
  db.exec(`
    CREATE TABLE IF NOT EXISTS journal_entries (
      id TEXT PRIMARY KEY,
      date TEXT NOT NULL,
      content TEXT NOT NULL,
      icon TEXT,
      type TEXT DEFAULT 'output',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // Add missing columns to journal_entries table for existing databases
  try {
    db.exec(`ALTER TABLE journal_entries ADD COLUMN icon TEXT`)
  } catch (e) {
    // Column already exists
  }
  try {
    db.exec(`ALTER TABLE journal_entries ADD COLUMN type TEXT DEFAULT 'output'`)
  } catch (e) {
    // Column already exists
  }

  // Weight records table
  db.exec(`
    CREATE TABLE IF NOT EXISTS weight_records (
      id TEXT PRIMARY KEY,
      weight REAL NOT NULL,
      date TEXT NOT NULL,
      note TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // Army cards table
  db.exec(`
    CREATE TABLE IF NOT EXISTS army_cards (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      character_class TEXT NOT NULL,
      description TEXT NOT NULL,
      rarity_level TEXT NOT NULL,
      rarity_color TEXT NOT NULL,
      rarity_glow_color TEXT NOT NULL,
      rarity_probability REAL NOT NULL,
      image_data BLOB,
      image_url TEXT,
      skills TEXT,
      story TEXT,
      style_prompt TEXT,
      attributes TEXT, -- JSON string for CardAttributes
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // Database version management
  db.exec(`
    CREATE TABLE IF NOT EXISTS db_version (
      version INTEGER PRIMARY KEY,
      applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // Check current database version
  const currentVersion = db.prepare('SELECT MAX(version) as version FROM db_version').get()
  const dbVersion = currentVersion?.version || 0

  console.log('Current database version:', dbVersion)

  // Apply migrations
  if (dbVersion < 1) {
    try {
      // Migration 1: Add style_prompt column
      console.log('Applying migration 1: Adding style_prompt column...')

      // Check if column exists first
      const tableInfo = db.prepare("PRAGMA table_info(army_cards)").all()
      const hasStylePrompt = tableInfo.some(col => col.name === 'style_prompt')

      if (!hasStylePrompt) {
        db.exec(`ALTER TABLE army_cards ADD COLUMN style_prompt TEXT`)
        console.log('Successfully added style_prompt column to army_cards table')
      } else {
        console.log('style_prompt column already exists')
      }

      // Record migration as completed
      db.exec(`INSERT OR IGNORE INTO db_version (version) VALUES (1)`)
      console.log('Migration 1 completed')
    } catch (error) {
      console.error('Error applying migration 1:', error.message)
      // Try to record the migration anyway if it's just a duplicate column error
      if (error.message.includes('duplicate column name')) {
        db.exec(`INSERT OR IGNORE INTO db_version (version) VALUES (1)`)
      }
    }
  }

  // Camp units table
  db.exec(`
    CREATE TABLE IF NOT EXISTS camp_units (
      id TEXT PRIMARY KEY,
      army_card_id TEXT NOT NULL,
      deployment_id TEXT NOT NULL,
      position_top TEXT NOT NULL,
      position_left TEXT NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      FOREIGN KEY (army_card_id) REFERENCES army_cards (id) ON DELETE CASCADE
    )
  `)

  // Custom character classes table
  db.exec(`
    CREATE TABLE IF NOT EXISTS custom_character_classes (
      id TEXT PRIMARY KEY,
      name TEXT NOT NULL,
      description TEXT,
      faction TEXT,
      cost INTEGER DEFAULT 10,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `)

  // Initialize user settings if not exists
  const userExists = db.prepare('SELECT COUNT(*) as count FROM user_settings').get()
  if (userExists.count === 0) {
    db.prepare('INSERT INTO user_settings (points) VALUES (100)').run()
  }
}

// 创建托盘图标
function createTray() {
  try {
    console.log('开始创建托盘图标...')

    // 使用绝对路径来确保图标能被正确加载
    const iconPath = path.join(__dirname, './assets/tray-icon.png')
    console.log('托盘图标路径:', iconPath)

    // 检查文件是否存在
    if (!fs.existsSync(iconPath)) {
      console.error('托盘图标文件不存在:', iconPath)
      return
    }
    console.log('托盘图标文件存在')

    // 创建图标
    const icon = nativeImage.createFromPath(iconPath)
    if (icon.isEmpty()) {
      console.error('无法加载托盘图标')
      return
    }
    console.log('托盘图标加载成功')

    // 创建托盘
    tray = new Tray(icon)
    console.log('托盘创建成功')

    // 创建上下文菜单
    const contextMenu = Menu.buildFromTemplate([
      {
        label: '打开主界面',
        click: () => {
          if (mainWindow) {
            mainWindow.show()
            mainWindow.focus()
          }
        }
      },
      {
        label: '添加日志',
        click: () => {
          if (mainWindow) {
            mainWindow.show()
            mainWindow.focus()
            mainWindow.webContents.send('open-add-journal-modal')
          }
        }
      },
      { type: 'separator' },
      {
        label: '退出',
        click: () => {
          app.isQuiting = true
          app.quit()
        }
      }
    ])

    tray.setToolTip('Habit Hero - 习惯英雄')
    tray.setContextMenu(contextMenu)

    // 添加双击事件
    tray.on('double-click', () => {
      if (mainWindow) {
        mainWindow.show()
        mainWindow.focus()
      }
    })

    console.log('托盘图标设置完成')

  } catch (error) {
    console.error('创建托盘图标时出错:', error)
  }
  
  tray.on('click', () => {
    mainWindow.show()
  })
}

async function createWindow() {
  // 开发环境检测
  const isDev = process.env.NODE_ENV === 'development' || !app.isPackaged;
  console.log('Environment check - isDev:', isDev, 'NODE_ENV:', process.env.NODE_ENV, 'isPackaged:', app.isPackaged);

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      webSecurity: isDev ? false : true, // 只在开发环境下禁用web安全策略
      allowRunningInsecureContent: isDev ? true : false, // 只在开发环境下允许不安全内容
      preload: path.join(__dirname, 'preload.js')
    },
    show: false // 先不显示窗口，等加载完成后再显示
  })

  // 只在生产环境下隐藏菜单，开发环境保留菜单以便调试
  if (process.env.NODE_ENV !== 'development') {
    mainWindow.setMenu(null);
  }
  
  // 设置Windows任务栏缩略图按钮
  if (process.platform === 'win32') {
    try {
      // 使用托盘图标作为任务栏按钮图标
      const journalIconPath = path.join(__dirname, './assets/tray-icon.png');

      if (fs.existsSync(journalIconPath)) {
        const addJournalIcon = nativeImage.createFromPath(journalIconPath);

        if (!addJournalIcon.isEmpty()) {
          mainWindow.setThumbarButtons([
            {
              tooltip: '添加日志',
              icon: addJournalIcon,
              click() {
                mainWindow.webContents.send('open-add-journal-modal');
              }
            }
          ]);
          console.log('Windows任务栏按钮设置成功');
        } else {
          console.log('无法加载任务栏按钮图标');
        }
      } else {
        console.log('任务栏按钮图标文件不存在:', journalIconPath);
      }
    } catch (error) {
      console.error('设置Windows任务栏按钮时出错:', error);
    }
  }
  
  // 开发环境下设置菜单
  if (isDev) {
    const template = [
      {
        label: '开发',
        submenu: [
          {
            label: '打开开发者工具',
            accelerator: 'F12',
            click: () => {
              mainWindow.webContents.openDevTools();
            }
          },
          {
            label: '重新加载',
            accelerator: 'CmdOrCtrl+R',
            click: () => {
              mainWindow.webContents.reload();
            }
          },
          {
            label: '强制重新加载',
            accelerator: 'CmdOrCtrl+Shift+R',
            click: () => {
              mainWindow.webContents.reloadIgnoringCache();
            }
          }
        ]
      }
    ];

    const menu = Menu.buildFromTemplate(template);
    Menu.setApplicationMenu(menu);
  }

  // 开发环境下连接到Vite服务器
  if (isDev) {
    // 尝试多个端口，因为Vite可能会自动切换端口
    const tryPorts = [5173, 5174, 5175, 5176];
    let loaded = false;

    for (const port of tryPorts) {
      try {
        console.log(`Trying to connect to http://localhost:${port}/`);
        await mainWindow.loadURL(`http://localhost:${port}/`);
        console.log(`Successfully connected to Vite server on port ${port}`);
        loaded = true;
        break;
      } catch (error) {
        console.log(`Failed to connect to port ${port}:`, error.message);
      }
    }

    if (!loaded) {
      console.error('Failed to connect to any Vite server port');
      // 显示错误页面
      mainWindow.loadURL('data:text/html,<h1>Development Server Not Found</h1><p>Please make sure Vite dev server is running on one of these ports: ' + tryPorts.join(', ') + '</p><p>Run: npm run dev</p>');
      mainWindow.show();
    }

    // 强制打开开发者工具
    console.log('Opening DevTools...');

    // 延迟打开开发者工具，确保窗口已经准备好
    setTimeout(() => {
      try {
        mainWindow.webContents.openDevTools();
        console.log('DevTools opened successfully');
      } catch (error) {
        console.error('Failed to open DevTools:', error);
      }
    }, 1000);

    // 确保开发者工具在窗口准备好后打开
    mainWindow.webContents.once('dom-ready', () => {
      console.log('DOM ready, opening DevTools again...');
      setTimeout(() => {
        try {
          mainWindow.webContents.openDevTools();
        } catch (error) {
          console.error('Failed to open DevTools on dom-ready:', error);
        }
      }, 500);
      mainWindow.show(); // DOM准备好后显示窗口
    });

    // 添加快捷键来打开开发者工具
    mainWindow.webContents.on('before-input-event', (event, input) => {
      // Ctrl+Shift+I
      if (input.control && input.shift && input.key.toLowerCase() === 'i') {
        mainWindow.webContents.openDevTools();
      }
      // F12
      if (input.key.toLowerCase() === 'f12') {
        mainWindow.webContents.openDevTools();
      }
      // Ctrl+Shift+J (Chrome DevTools shortcut)
      if (input.control && input.shift && input.key.toLowerCase() === 'j') {
        mainWindow.webContents.openDevTools();
      }
    });

  } else {
    // 生产环境下加载打包后的文件
    const indexPath = path.join(__dirname, '../dist/index.html');
    console.log('Loading production file from:', indexPath);

    mainWindow.loadFile(indexPath).catch(error => {
      console.error('Failed to load production file:', error);
      // 如果加载失败，尝试显示错误页面
      mainWindow.loadURL('data:text/html,<h1>Failed to load application</h1><p>Error: ' + error.message + '</p>');
      mainWindow.show();
    });

    // 生产环境下也等待DOM准备好后显示窗口
    mainWindow.webContents.once('dom-ready', () => {
      console.log('Production DOM ready, showing window...');
      mainWindow.show();
    });
  }

  // 添加错误处理
  mainWindow.webContents.on('did-fail-load', (event, errorCode, errorDescription, validatedURL) => {
    console.error('Failed to load:', errorCode, errorDescription, validatedURL);
    mainWindow.show(); // 即使加载失败也显示窗口
  });

  mainWindow.webContents.on('crashed', (event) => {
    console.error('Renderer process crashed');
  });

  mainWindow.webContents.on('unresponsive', () => {
    console.error('Renderer process became unresponsive');
  });

  mainWindow.webContents.on('responsive', () => {
    console.log('Renderer process became responsive again');
  });

  // 添加控制台消息监听
  mainWindow.webContents.on('console-message', (event, level, message, line, sourceId) => {
    console.log(`Console [${level}]: ${message}`);
  });

  // 窗口关闭时隐藏到托盘而不是退出
  mainWindow.on('close', (event) => {
    if (!app.isQuiting) {
      event.preventDefault();
      mainWindow.hide();

      // 可选：显示托盘通知（仅Windows支持displayBalloon）
      if (tray && process.platform === 'win32') {
        try {
          tray.displayBalloon({
            iconType: 'info',
            title: 'Habit Hero',
            content: '应用已最小化到托盘，右键托盘图标可以退出应用'
          });
        } catch (error) {
          console.log('托盘通知不支持:', error.message);
        }
      }
    }
  });

  // 窗口关闭事件（真正关闭时清理）
  mainWindow.on('closed', () => {
    mainWindow = null;
  });
}

// 设置所有IPC处理器
function setupIpcHandlers() {
  ipcMain.handle('db-get-user-data', async () => {
    try {
      console.log('Main process: Getting user data from database...')

      if (!db) {
        console.error('Main process: Database not initialized!')
        throw new Error('Database not initialized')
      }

      const userSettings = db.prepare('SELECT * FROM user_settings WHERE id = 1').get() || {
        points: 100,
        army_style_prompt: '',
        camp_name: '',
        flag_url: '',
        total_points_earned: 0,
        attribute_schema: null
      }
      console.log('Main process: User settings:', userSettings)

      const tasks = db.prepare('SELECT * FROM tasks').all()
      console.log('Main process: Found', tasks.length, 'tasks')

      const journalEntries = db.prepare('SELECT * FROM journal_entries ORDER BY date DESC').all()
      console.log('Main process: Found', journalEntries.length, 'journal entries')

      const weightRecords = db.prepare('SELECT * FROM weight_records ORDER BY date DESC').all()
      console.log('Main process: Found', weightRecords.length, 'weight records')

      // 只加载前50个角色卡片以提高性能
      const armyCards = db.prepare('SELECT * FROM army_cards ORDER BY created_at DESC LIMIT 50').all()
      console.log('Main process: Found', armyCards.length, 'army cards (limited to 50 for performance)')

      const campUnits = db.prepare(`
        SELECT cu.*, ac.* FROM camp_units cu
        JOIN army_cards ac ON cu.army_card_id = ac.id
        ORDER BY cu.created_at DESC
      `).all()
      console.log('Main process: Found', campUnits.length, 'camp units')

      const result = {
        points: userSettings.points,
        totalPointsEarned: userSettings.total_points_earned || 0,
        tasks: tasks.map(mapTaskFromDB),
        journalEntries: journalEntries.map(mapJournalEntryFromDB),
        weightRecords: weightRecords.map(mapWeightRecordFromDB),
        armyCards: armyCards.map(mapArmyCardFromDB),
        campUnits: campUnits.map(mapCampUnitFromDB),
        armyStylePrompt: userSettings.army_style_prompt || '',
        campName: userSettings.camp_name || '',
        flagUrl: userSettings.flag_url || '',
        attributeSchema: userSettings.attribute_schema ? JSON.parse(userSettings.attribute_schema) : undefined,
      }

      console.log('Main process: Returning user data:', {
        points: result.points,
        tasksCount: result.tasks.length,
        journalEntriesCount: result.journalEntries.length,
        armyCardsCount: result.armyCards.length,
        campUnitsCount: result.campUnits.length,
        campName: result.campName,
        flagUrl: result.flagUrl ? 'present' : 'empty'
      })

      return result
    } catch (error) {
      console.error('Main process: Error getting user data:', error)
      throw error
    }
  })

  ipcMain.handle('db-save-army-card', async (event, card) => {
    try {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO army_cards (
          id, name, character_class, description,
          rarity_level, rarity_color, rarity_glow_color, rarity_probability,
          image_url, skills, story, style_prompt, attributes
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      stmt.run(
        card.id, card.name, card.characterClass, card.description,
        card.rarity.level, card.rarity.color, card.rarity.glowColor, card.rarity.probability,
        card.imageUrl, JSON.stringify(card.skills), card.story, card.stylePrompt || null,
        card.attributes ? JSON.stringify(card.attributes) : null
      )
    } catch (error) {
      console.error('Error saving army card:', error)
      throw error
    }
  })

  ipcMain.handle('db-save-camp-unit', async (event, unit) => {
    try {
      // First save the army card
      const armyCardStmt = db.prepare(`
        INSERT OR REPLACE INTO army_cards (
          id, name, character_class, description,
          rarity_level, rarity_color, rarity_glow_color, rarity_probability,
          image_url, skills, story, style_prompt
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      armyCardStmt.run(
        unit.id, unit.name, unit.characterClass, unit.description,
        unit.rarity.level, unit.rarity.color, unit.rarity.glowColor, unit.rarity.probability,
        unit.imageUrl, JSON.stringify(unit.skills), unit.story, unit.stylePrompt || null
      )

      const stmt = db.prepare(`
        INSERT OR REPLACE INTO camp_units (
          id, army_card_id, deployment_id, position_top, position_left
        ) VALUES (?, ?, ?, ?, ?)
      `)

      stmt.run(unit.deploymentId, unit.id, unit.deploymentId, unit.position.top, unit.position.left)
    } catch (error) {
      console.error('Error saving camp unit:', error)
      throw error
    }
  })

  ipcMain.handle('db-update-user-points', async (event, points) => {
    try {
      const stmt = db.prepare('UPDATE user_settings SET points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1')
      stmt.run(points)
    } catch (error) {
      console.error('Error updating user points:', error)
      throw error
    }
  })

  // 增加总积分（当用户获得积分时调用）
  ipcMain.handle('db-add-total-points', async (event, pointsToAdd) => {
    try {
      const stmt = db.prepare(`
        UPDATE user_settings
        SET total_points_earned = COALESCE(total_points_earned, 0) + ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = 1
      `)
      stmt.run(pointsToAdd)
    } catch (error) {
      console.error('Error adding total points:', error)
      throw error
    }
  })

  // 设置总积分（用于数据导入）
  ipcMain.handle('db-set-total-points', async (event, totalPoints) => {
    try {
      const stmt = db.prepare(`
        UPDATE user_settings
        SET total_points_earned = ?,
            updated_at = CURRENT_TIMESTAMP
        WHERE id = 1
      `)
      stmt.run(totalPoints)
    } catch (error) {
      console.error('Error setting total points:', error)
      throw error
    }
  })

  ipcMain.handle('db-update-army-style-prompt', async (event, prompt) => {
    try {
      const stmt = db.prepare('UPDATE user_settings SET army_style_prompt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1')
      stmt.run(prompt)
    } catch (error) {
      console.error('Error updating army style prompt:', error)
      throw error
    }
  })

  // 保存营地信息
  ipcMain.handle('db-save-camp-info', async (event, campName, flagUrl) => {
    try {
      console.log('Main process: Saving camp info to database:', { campName, flagUrl })

      // 确保有一行数据存在
      const existingRow = db.prepare('SELECT id FROM user_settings WHERE id = 1').get()
      if (!existingRow) {
        console.log('Main process: Creating initial user_settings row')
        db.prepare(`
          INSERT INTO user_settings (id, points, army_style_prompt, camp_name, flag_url)
          VALUES (1, 100, '', ?, ?)
        `).run(campName || '', flagUrl || '')
      } else {
        console.log('Main process: Updating existing user_settings row')
        const stmt = db.prepare(`
          UPDATE user_settings
          SET camp_name = ?, flag_url = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = 1
        `)
        stmt.run(campName || '', flagUrl || '')
      }

      // 验证保存是否成功
      const savedData = db.prepare('SELECT camp_name, flag_url FROM user_settings WHERE id = 1').get()
      console.log('Main process: Verified saved data:', savedData)

      return { success: true }
    } catch (error) {
      console.error('Error saving camp info:', error)
      throw error
    }
  })

  ipcMain.handle('db-delete-army-card', async (event, cardId) => {
    try {
      const stmt = db.prepare('DELETE FROM army_cards WHERE id = ?')
      stmt.run(cardId)
    } catch (error) {
      console.error('Error deleting army card:', error)
      throw error
    }
  })

  // 保存属性模式
  ipcMain.handle('db-save-attribute-schema', async (event, schema) => {
    try {
      console.log('Main process: Saving attribute schema to database')

      // 确保有一行数据存在
      const existingRow = db.prepare('SELECT id FROM user_settings WHERE id = 1').get()
      if (!existingRow) {
        db.prepare(`
          INSERT INTO user_settings (id, points, attribute_schema)
          VALUES (1, 100, ?)
        `).run(JSON.stringify(schema))
      } else {
        const stmt = db.prepare(`
          UPDATE user_settings
          SET attribute_schema = ?, updated_at = CURRENT_TIMESTAMP
          WHERE id = 1
        `)
        stmt.run(JSON.stringify(schema))
      }

      console.log('Main process: Attribute schema saved successfully')
      return { success: true }
    } catch (error) {
      console.error('Error saving attribute schema:', error)
      throw error
    }
  })

  // Custom character classes handlers
  ipcMain.handle('db-save-custom-class', async (event, customClass) => {
    try {
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO custom_character_classes (
          id, name, description, faction, cost, created_at
        ) VALUES (?, ?, ?, ?, ?, ?)
      `)
      stmt.run(
        customClass.id,
        customClass.name,
        customClass.description,
        customClass.faction,
        customClass.cost,
        customClass.createdAt
      )
    } catch (error) {
      console.error('Error saving custom class:', error)
      throw error
    }
  })

  ipcMain.handle('db-get-custom-classes', async (event) => {
    try {
      const stmt = db.prepare('SELECT * FROM custom_character_classes ORDER BY created_at DESC')
      return stmt.all()
    } catch (error) {
      console.error('Error getting custom classes:', error)
      throw error
    }
  })

  ipcMain.handle('db-delete-custom-class', async (event, classId) => {
    try {
      const stmt = db.prepare('DELETE FROM custom_character_classes WHERE id = ?')
      stmt.run(classId)
    } catch (error) {
      console.error('Error deleting custom class:', error)
      throw error
    }
  })

  ipcMain.handle('db-save-task', async (event, task) => {
    try {
      console.log('Saving task to database:', task)
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO tasks (
          id, title, description, emoji, points, type, category, is_highlighted,
          due_date, is_completed, actual_completion_date,
          days_of_week, completed_dates, times_per_week, created_at
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      const result = stmt.run(
        task.id,
        task.title,
        task.description || '',
        task.emoji || '',
        task.points || 10,
        task.type,
        task.category || '',
        task.isHighlighted ? 1 : 0,
        task.dueDate || null,
        task.isCompleted ? 1 : 0,
        task.actualCompletionDate || null,
        JSON.stringify(task.daysOfWeek || []),
        JSON.stringify(task.completedDates || []),
        task.timesPerWeek || null,
        task.createdAt || new Date().toISOString()
      )
      console.log('Task saved successfully, changes:', result.changes)
    } catch (error) {
      console.error('Error saving task:', error)
      console.error('Task data:', task)
      throw error
    }
  })

  ipcMain.handle('db-delete-task', async (event, taskId) => {
    try {
      const stmt = db.prepare('DELETE FROM tasks WHERE id = ?')
      stmt.run(taskId)
    } catch (error) {
      console.error('Error deleting task:', error)
      throw error
    }
  })

  ipcMain.handle('db-save-journal-entry', async (event, entry) => {
    try {
      console.log('Saving journal entry to database:', entry)
      const stmt = db.prepare(`
        INSERT OR REPLACE INTO journal_entries (id, date, content, icon, type, created_at)
        VALUES (?, ?, ?, ?, ?, ?)
      `)

      const result = stmt.run(
        entry.id,
        entry.date,
        entry.content,
        entry.icon || null,
        entry.type || 'output',
        entry.createdAt || new Date().toISOString()
      )
      console.log('Journal entry saved successfully, changes:', result.changes)
    } catch (error) {
      console.error('Error saving journal entry:', error)
      console.error('Journal entry data:', entry)
      throw error
    }
  })

  ipcMain.handle('db-delete-journal-entry', async (event, entryId) => {
    try {
      const stmt = db.prepare('DELETE FROM journal_entries WHERE id = ?')
      stmt.run(entryId)
    } catch (error) {
      console.error('Error deleting journal entry:', error)
      throw error
    }
  })

  // 体重管理处理器
  ipcMain.handle('db-save-weight-record', async (event, record) => {
    try {
      console.log('Saving weight record:', record)

      // 先删除同一日期的记录（如果存在）
      const deleteStmt = db.prepare('DELETE FROM weight_records WHERE date = ?')
      deleteStmt.run(record.date)

      // 插入新记录
      const insertStmt = db.prepare(`
        INSERT INTO weight_records (id, weight, date, created_at, note)
        VALUES (?, ?, ?, ?, ?)
      `)

      insertStmt.run(
        record.id,
        record.weight,
        record.date,
        record.createdAt || new Date().toISOString(),
        record.note || null
      )

      console.log('Weight record saved successfully')
    } catch (error) {
      console.error('Error saving weight record:', error)
      console.error('Weight record data:', record)
      throw error
    }
  })

  ipcMain.handle('db-delete-weight-record', async (event, id) => {
    try {
      const stmt = db.prepare('DELETE FROM weight_records WHERE id = ?')
      stmt.run(id)
    } catch (error) {
      console.error('Error deleting weight record:', error)
      throw error
    }
  })

  ipcMain.handle('db-delete-camp-unit', async (event, unitId) => {
    try {
      const stmt = db.prepare('DELETE FROM camp_units WHERE deployment_id = ?')
      stmt.run(unitId)
    } catch (error) {
      console.error('Error deleting camp unit:', error)
      throw error
    }
  })

  // 隐藏窗口
  ipcMain.handle('hide-window', async () => {
    try {
      if (mainWindow) {
        mainWindow.hide();
      }
    } catch (error) {
      console.error('Error hiding window:', error);
      throw error;
    }
  })

  // 导出数据
  ipcMain.handle('export-data', async (event, data) => {
    try {
      const result = await dialog.showSaveDialog(mainWindow, {
        title: '导出数据',
        defaultPath: `habit-hero-backup-${new Date().toISOString().split('T')[0]}.json`,
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ]
      })

      if (!result.canceled && result.filePath) {
        fs.writeFileSync(result.filePath, data, 'utf8')
        return { success: true, path: result.filePath }
      } else {
        return { success: false, error: 'User cancelled' }
      }
    } catch (error) {
      console.error('Error exporting data:', error)
      return { success: false, error: error.message }
    }
  })

  // 导入数据
  ipcMain.handle('import-data', async (event) => {
    try {
      const result = await dialog.showOpenDialog(mainWindow, {
        title: '导入数据',
        filters: [
          { name: 'JSON Files', extensions: ['json'] },
          { name: 'All Files', extensions: ['*'] }
        ],
        properties: ['openFile']
      })

      if (!result.canceled && result.filePaths.length > 0) {
        const data = fs.readFileSync(result.filePaths[0], 'utf8')
        return { success: true, data: data }
      } else {
        return { success: false, error: 'User cancelled' }
      }
    } catch (error) {
      console.error('Error importing data:', error)
      return { success: false, error: error.message }
    }
  })

  // 分页加载角色卡片
  ipcMain.handle('db-get-army-cards-paginated', async (event, page = 0, limit = 50) => {
    try {
      const offset = page * limit;
      const stmt = db.prepare('SELECT * FROM army_cards ORDER BY created_at DESC LIMIT ? OFFSET ?');
      const cards = stmt.all(limit, offset);

      // 获取总数
      const countStmt = db.prepare('SELECT COUNT(*) as total FROM army_cards');
      const { total } = countStmt.get();

      return {
        cards: cards.map(mapArmyCardFromDB),
        total,
        page,
        limit,
        hasMore: offset + cards.length < total
      };
    } catch (error) {
      console.error('Error getting paginated army cards:', error);
      throw error;
    }
  });

  ipcMain.handle('db-get-stats', async () => {
    try {
      const armyCards = db.prepare('SELECT COUNT(*) as count FROM army_cards').get()
      const campUnits = db.prepare('SELECT COUNT(*) as count FROM camp_units').get()
      const tasks = db.prepare('SELECT COUNT(*) as count FROM tasks').get()
      const journalEntries = db.prepare('SELECT COUNT(*) as count FROM journal_entries').get()
      const weightRecords = db.prepare('SELECT COUNT(*) as count FROM weight_records').get()

      return {
        armyCards: armyCards.count,
        campUnits: campUnits.count,
        tasks: tasks.count,
        journalEntries: journalEntries.count,
        weightRecords: weightRecords.count,
        dbSize: 0, // Could implement file size check
      }
    } catch (error) {
      console.error('Error getting stats:', error)
      throw error
    }
  })



  // 保存应用设置到文件系统
  ipcMain.handle('save-app-settings', async (event, settings) => {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'app-settings.json')
      fs.writeFileSync(settingsPath, JSON.stringify(settings, null, 2), 'utf8')
      console.log('App settings saved to:', settingsPath)
      return { success: true }
    } catch (error) {
      console.error('Error saving app settings:', error)
      return { success: false, error: error.message }
    }
  })

  // 读取应用设置
  ipcMain.handle('load-app-settings', async () => {
    try {
      const settingsPath = path.join(app.getPath('userData'), 'app-settings.json')
      if (fs.existsSync(settingsPath)) {
        const settings = JSON.parse(fs.readFileSync(settingsPath, 'utf8'))
        return { success: true, settings }
      } else {
        return { success: true, settings: null }
      }
    } catch (error) {
      console.error('Error loading app settings:', error)
      return { success: false, error: error.message }
    }
  })

  // 获取当前数据库路径
  ipcMain.handle('get-current-database-path', async () => {
    try {
      const currentPath = getDatabasePath()
      return { success: true, path: currentPath }
    } catch (error) {
      console.error('Error getting current database path:', error)
      return { success: false, error: error.message }
    }
  })

}

// Database mapping functions
function mapTaskFromDB(row) {
  return {
    id: row.id,
    title: row.title,
    description: row.description,
    emoji: row.emoji,
    points: row.points,
    type: row.type,
    category: row.category,
    isHighlighted: row.is_highlighted === 1,
    createdAt: row.created_at,
    dueDate: row.due_date,
    isCompleted: row.is_completed === 1,
    actualCompletionDate: row.actual_completion_date,
    daysOfWeek: row.days_of_week ? JSON.parse(row.days_of_week) : [],
    completedDates: row.completed_dates ? JSON.parse(row.completed_dates) : [],
    timesPerWeek: row.times_per_week,
  }
}

function mapJournalEntryFromDB(row) {
  return {
    id: row.id,
    date: row.date,
    content: row.content,
    icon: row.icon,
    type: row.type || 'output',
    createdAt: row.created_at,
  }
}

function mapWeightRecordFromDB(row) {
  return {
    id: row.id,
    weight: row.weight,
    date: row.date,
    note: row.note,
    createdAt: row.created_at,
  }
}

function mapArmyCardFromDB(row) {
  return {
    id: row.id,
    name: row.name,
    characterClass: row.character_class,
    description: row.description,
    rarity: {
      level: row.rarity_level,
      color: row.rarity_color,
      glowColor: row.rarity_glow_color,
      probability: row.rarity_probability,
    },
    imageUrl: row.image_url,
    skills: row.skills ? JSON.parse(row.skills) : [],
    story: row.story,
    stylePrompt: row.style_prompt,
    attributes: row.attributes ? JSON.parse(row.attributes) : undefined,
    createdAt: row.created_at,
  }
}

function mapCampUnitFromDB(row) {
  const armyCard = mapArmyCardFromDB(row)

  // 确保位置格式正确（像素值）
  let top = row.position_top || '0px'
  let left = row.position_left || '0px'

  // 如果没有单位，添加px
  if (!top.includes('px') && !top.includes('%')) {
    top = `${top}px`
  }
  if (!left.includes('px') && !left.includes('%')) {
    left = `${left}px`
  }

  return {
    ...armyCard,
    deploymentId: row.deployment_id,
    position: {
      top,
      left,
    },
  }
}

app.whenReady().then(async () => {
  console.log('App is ready, initializing...')
  initializeDatabase()
  setupIpcHandlers() // 设置IPC处理器
  await createWindow()
  createTray()
  console.log('Electron app fully initialized')

  app.on('activate', async function () {
    // macOS: 点击dock图标时恢复窗口
    if (BrowserWindow.getAllWindows().length === 0) {
      await createWindow()
    } else if (mainWindow) {
      mainWindow.show()
      mainWindow.focus()
    }
  })
})

// 确保应用退出时清理托盘图标和数据库连接
app.on('before-quit', () => {
  app.isQuiting = true

  // 清理托盘图标
  if (tray) {
    tray.destroy()
    tray = null
    console.log('Tray icon destroyed')
  }

  // 关闭数据库连接
  if (db) {
    db.close()
    console.log('Database connection closed')
  }
})

app.on('window-all-closed', function () {
  // 不要在所有窗口关闭时退出应用，让应用继续在托盘中运行
  // 只有在明确退出时才真正退出
  if (process.platform !== 'darwin' && app.isQuiting) {
    app.quit()
  }
})






