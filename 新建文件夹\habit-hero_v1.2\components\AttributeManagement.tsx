import React, { useState, useMemo, useEffect } from 'react';
import { ArmyCardData, CardAttributes, AttributeSchema } from '../types';
import { AttributeGenerator } from './AttributeGenerator';
import { AttributeDisplay, AttributeComparison } from './AttributeDisplay';
import { ArmyCardView } from './ArmyCardView';

interface AttributeManagementProps {
  armyCards: ArmyCardData[];
  onCardUpdate: (card: ArmyCardData) => void;
  userPoints: number;
  onPointsChange: (points: number) => void;
  currentSchema?: AttributeSchema;
  onSchemaUpdate: (schema: AttributeSchema) => void;
  onRefreshData?: () => void; // 添加刷新数据的回调
}

export const AttributeManagement: React.FC<AttributeManagementProps> = ({
  armyCards,
  onCardUpdate,
  userPoints,
  onPointsChange,
  currentSchema,
  onSchemaUpdate,
  onRefreshData
}) => {
  const [activeTab, setActiveTab] = useState<'generator' | 'gallery' | 'comparison'>('generator');
  const [selectedCards, setSelectedCards] = useState<string[]>([]);
  const [filterClass, setFilterClass] = useState<string>('all');
  const [filterRarity, setFilterRarity] = useState<string>('all');
  const [showAttributesOnly, setShowAttributesOnly] = useState(false);
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(20); // 每页显示数量

  // 统计信息
  const stats = useMemo(() => {
    const total = armyCards.length;
    const withAttributes = armyCards.filter(card => card.attributes).length;
    const withoutAttributes = total - withAttributes;
    
    const classCounts = armyCards.reduce((acc, card) => {
      acc[card.characterClass] = (acc[card.characterClass] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const attributeClassCounts = armyCards
      .filter(card => card.attributes)
      .reduce((acc, card) => {
        acc[card.characterClass] = (acc[card.characterClass] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    return {
      total,
      withAttributes,
      withoutAttributes,
      classCounts,
      attributeClassCounts,
      completionRate: total > 0 ? (withAttributes / total * 100).toFixed(1) : '0'
    };
  }, [armyCards]);

  // 获取所有职业和稀有度
  const allClasses = useMemo(() => {
    const classes = new Set(armyCards.map(card => card.characterClass));
    return Array.from(classes).sort();
  }, [armyCards]);

  const allRarities = useMemo(() => {
    const rarities = new Set(armyCards.map(card => card.rarity.level));
    return Array.from(rarities).sort();
  }, [armyCards]);

  // 过滤后的卡牌
  const filteredCards = useMemo(() => {
    return armyCards.filter(card => {
      if (filterClass !== 'all' && card.characterClass !== filterClass) return false;
      if (filterRarity !== 'all' && card.rarity.level !== filterRarity) return false;
      if (showAttributesOnly && !card.attributes) return false;
      return true;
    });
  }, [armyCards, filterClass, filterRarity, showAttributesOnly]);

  // 分页数据
  const paginatedCards = useMemo(() => {
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return filteredCards.slice(startIndex, endIndex);
  }, [filteredCards, currentPage, itemsPerPage]);

  const totalPages = Math.ceil(filteredCards.length / itemsPerPage);

  // 重置页码当过滤条件或每页数量改变时
  useEffect(() => {
    setCurrentPage(1);
  }, [filterClass, filterRarity, showAttributesOnly, itemsPerPage]);

  // 处理属性生成完成
  const handleAttributesGenerated = (cardId: string, attributes: CardAttributes) => {
    console.log('AttributeManagement: 处理属性生成完成', { cardId, attributes });
    const card = armyCards.find(c => c.id === cardId);
    if (card) {
      const updatedCard = { ...card, attributes };
      console.log('AttributeManagement: 准备更新卡片', updatedCard);
      onCardUpdate(updatedCard);
      console.log('AttributeManagement: 卡片更新调用完成');
    } else {
      console.error('AttributeManagement: 未找到卡片', cardId);
    }
  };

  // 切换卡牌选择
  const toggleCardSelection = (cardId: string) => {
    setSelectedCards(prev => 
      prev.includes(cardId) 
        ? prev.filter(id => id !== cardId)
        : [...prev, cardId].slice(0, 5) // 最多选择5张卡牌
    );
  };

  // 获取选中的卡牌用于对比
  const selectedCardsForComparison = useMemo(() => {
    return selectedCards
      .map(id => armyCards.find(card => card.id === id))
      .filter(card => card && card.attributes)
      .map(card => ({
        name: card!.name,
        attributes: card!.attributes!
      }));
  }, [selectedCards, armyCards]);

  return (
    <div className="p-6 bg-slate-900 min-h-screen">
      <div className="max-w-7xl mx-auto">
        {/* 标题和统计 */}
        <div className="mb-6">
          <h1 className="text-3xl font-bold text-white mb-4">角色属性管理系统</h1>
          
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
            <div className="bg-slate-800 p-4 rounded-lg">
              <div className="text-slate-300 text-sm">总角色数</div>
              <div className="text-2xl font-bold text-white">{stats.total}</div>
            </div>
            <div className="bg-slate-800 p-4 rounded-lg">
              <div className="text-slate-300 text-sm">已生成属性</div>
              <div className="text-2xl font-bold text-green-400">{stats.withAttributes}</div>
            </div>
            <div className="bg-slate-800 p-4 rounded-lg">
              <div className="text-slate-300 text-sm">待生成属性</div>
              <div className="text-2xl font-bold text-orange-400">{stats.withoutAttributes}</div>
            </div>
            <div className="bg-slate-800 p-4 rounded-lg">
              <div className="text-slate-300 text-sm">完成率</div>
              <div className="text-2xl font-bold text-blue-400">{stats.completionRate}%</div>
            </div>
          </div>

          {/* 进度条 */}
          <div className="bg-slate-800 rounded-full h-3 mb-6">
            <div 
              className="bg-gradient-to-r from-green-500 to-blue-500 h-3 rounded-full transition-all duration-500"
              style={{ width: `${stats.completionRate}%` }}
            />
          </div>
        </div>

        {/* 标签页 */}
        <div className="flex space-x-1 mb-6">
          {[
            { key: 'generator', label: '属性生成器', icon: '🔧' },
            { key: 'gallery', label: '属性画廊', icon: '🖼️' },
            { key: 'comparison', label: '属性对比', icon: '⚖️' }
          ].map(tab => (
            <button
              key={tab.key}
              onClick={() => setActiveTab(tab.key as any)}
              className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                activeTab === tab.key
                  ? 'bg-blue-600 text-white'
                  : 'bg-slate-700 text-slate-300 hover:bg-slate-600'
              }`}
            >
              <span className="mr-2">{tab.icon}</span>
              {tab.label}
            </button>
          ))}
        </div>

        {/* 内容区域 */}
        {activeTab === 'generator' && (
          <AttributeGenerator
            armyCards={armyCards}
            onAttributesGenerated={handleAttributesGenerated}
            userPoints={userPoints}
            onPointsChange={onPointsChange}
            currentSchema={currentSchema}
            onSchemaUpdate={onSchemaUpdate}
          />
        )}

        {activeTab === 'gallery' && (
          <div>
            {/* 过滤器 */}
            <div className="flex flex-wrap gap-4 mb-6 p-4 bg-slate-800 rounded-lg">
              <div className="flex items-center gap-2">
                <label className="text-slate-300 text-sm">职业:</label>
                <select
                  value={filterClass}
                  onChange={(e) => setFilterClass(e.target.value)}
                  className="bg-slate-700 text-white px-3 py-1 rounded border border-slate-600"
                >
                  <option value="all">全部</option>
                  {allClasses.map(cls => (
                    <option key={cls} value={cls}>{cls}</option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-center gap-2">
                <label className="text-slate-300 text-sm">稀有度:</label>
                <select
                  value={filterRarity}
                  onChange={(e) => setFilterRarity(e.target.value)}
                  className="bg-slate-700 text-white px-3 py-1 rounded border border-slate-600"
                >
                  <option value="all">全部</option>
                  {allRarities.map(rarity => (
                    <option key={rarity} value={rarity}>{rarity}</option>
                  ))}
                </select>
              </div>

              <div className="flex items-center gap-2">
                <input
                  type="checkbox"
                  id="showAttributesOnly"
                  checked={showAttributesOnly}
                  onChange={(e) => setShowAttributesOnly(e.target.checked)}
                  className="rounded"
                />
                <label htmlFor="showAttributesOnly" className="text-slate-300 text-sm">
                  仅显示已生成属性
                </label>
              </div>

              <div className="flex items-center gap-2">
                <label className="text-slate-300 text-sm">每页显示:</label>
                <select
                  value={itemsPerPage}
                  onChange={(e) => setItemsPerPage(parseInt(e.target.value))}
                  className="bg-slate-700 text-white px-3 py-1 rounded border border-slate-600"
                >
                  <option value={12}>12个</option>
                  <option value={20}>20个</option>
                  <option value={36}>36个</option>
                  <option value={50}>50个</option>
                </select>
              </div>

              <div className="text-slate-400 text-sm">
                显示 {filteredCards.length} / {armyCards.length} 个角色
                {filteredCards.length > itemsPerPage && (
                  <span className="ml-2">
                    (第 {currentPage} / {totalPages} 页)
                  </span>
                )}
                <div className="mt-1 text-xs">
                  可用职业: {allClasses.length}个 | 可用稀有度: {allRarities.length}个
                </div>
              </div>

              {/* 临时调试按钮 */}
              {onRefreshData && (
                <button
                  onClick={onRefreshData}
                  className="px-3 py-1 bg-yellow-600 text-white rounded hover:bg-yellow-700 text-sm"
                >
                  🔄 刷新数据
                </button>
              )}
            </div>

            {/* 分页控件 */}
            {totalPages > 1 && (
              <div className="flex justify-center items-center gap-4 mb-6 p-4 bg-slate-800 rounded-lg">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                  className="px-3 py-1 bg-slate-600 text-white rounded hover:bg-slate-500 disabled:bg-slate-700 disabled:text-slate-500"
                >
                  上一页
                </button>

                <div className="flex items-center gap-2">
                  {/* 页码按钮 */}
                  {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                    let pageNum;
                    if (totalPages <= 5) {
                      pageNum = i + 1;
                    } else if (currentPage <= 3) {
                      pageNum = i + 1;
                    } else if (currentPage >= totalPages - 2) {
                      pageNum = totalPages - 4 + i;
                    } else {
                      pageNum = currentPage - 2 + i;
                    }

                    return (
                      <button
                        key={pageNum}
                        onClick={() => setCurrentPage(pageNum)}
                        className={`px-3 py-1 rounded ${
                          currentPage === pageNum
                            ? 'bg-blue-600 text-white'
                            : 'bg-slate-600 text-slate-300 hover:bg-slate-500'
                        }`}
                      >
                        {pageNum}
                      </button>
                    );
                  })}
                </div>

                <button
                  onClick={() => setCurrentPage(prev => Math.min(totalPages, prev + 1))}
                  disabled={currentPage === totalPages}
                  className="px-3 py-1 bg-slate-600 text-white rounded hover:bg-slate-500 disabled:bg-slate-700 disabled:text-slate-500"
                >
                  下一页
                </button>

                <div className="flex items-center gap-2 ml-4">
                  <span className="text-slate-400 text-sm">跳转到</span>
                  <input
                    type="number"
                    min="1"
                    max={totalPages}
                    value={currentPage}
                    onChange={(e) => {
                      const page = parseInt(e.target.value);
                      if (page >= 1 && page <= totalPages) {
                        setCurrentPage(page);
                      }
                    }}
                    className="w-16 px-2 py-1 bg-slate-600 text-white rounded text-sm"
                  />
                  <span className="text-slate-400 text-sm">页</span>
                </div>

                <div className="text-slate-400 text-sm ml-4">
                  共 {filteredCards.length} 个角色
                </div>
              </div>
            )}

            {/* 加载状态 */}
            {paginatedCards.length === 0 && filteredCards.length === 0 ? (
              <div className="text-center py-12 bg-slate-800 rounded-lg">
                <div className="text-6xl mb-4">🎴</div>
                <div className="text-xl text-slate-300 mb-2">暂无角色数据</div>
                <div className="text-slate-400">
                  请先在军队系统中招募一些角色
                </div>
              </div>
            ) : paginatedCards.length === 0 ? (
              <div className="text-center py-12 bg-slate-800 rounded-lg">
                <div className="text-6xl mb-4">🔍</div>
                <div className="text-xl text-slate-300 mb-2">没有符合条件的角色</div>
                <div className="text-slate-400">
                  尝试调整筛选条件
                </div>
              </div>
            ) : (
              /* 卡牌网格 */
              <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 2xl:grid-cols-6 gap-4">
                {paginatedCards.map(card => (
                <div key={card.id} className="relative">
                  <div
                    className={`cursor-pointer transition-all duration-200 bg-slate-800 rounded-lg overflow-hidden border-2 ${
                      selectedCards.includes(card.id)
                        ? 'ring-2 ring-blue-500 transform scale-105 border-blue-500'
                        : 'hover:transform hover:scale-102 border-slate-600 hover:border-slate-500'
                    }`}
                    onClick={() => toggleCardSelection(card.id)}
                  >
                    {/* 卡牌图片 */}
                    <div className="w-full h-48 relative">
                      <img
                        src={card.imageUrl}
                        alt={card.name}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          const target = e.target as HTMLImageElement;
                          target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMTAwJSIgaGVpZ2h0PSIxMDAlIiBmaWxsPSIjNjM2MzYzIi8+PHRleHQgeD0iNTAlIiB5PSI1MCUiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNCIgZmlsbD0iI2ZmZiIgdGV4dC1hbmNob3I9Im1pZGRsZSIgZHk9Ii4zZW0iPuaXoOazleWKoOi9vTwvdGV4dD48L3N2Zz4=';
                        }}
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-slate-800 via-transparent to-transparent"></div>

                      {/* 稀有度标识 */}
                      <div
                        className="absolute top-2 right-2 px-2 py-1 rounded text-xs font-bold"
                        style={{
                          backgroundColor: card.rarity.glowColor + '40',
                          color: card.rarity.color,
                          border: `1px solid ${card.rarity.glowColor}`
                        }}
                      >
                        {card.rarity.level}
                      </div>
                    </div>

                    {/* 卡牌信息 */}
                    <div className="p-3">
                      <h3 className="text-white font-bold text-lg mb-1 truncate">{card.name}</h3>
                      <p className="text-slate-400 text-sm mb-2">{card.characterClass}</p>
                      <p className="text-slate-300 text-xs line-clamp-2">{card.description}</p>
                    </div>
                  </div>

                  {/* 属性显示 */}
                  {card.attributes && (
                    <div className="mt-2">
                      <AttributeDisplay attributes={card.attributes} compact />
                    </div>
                  )}

                  {!card.attributes && (
                    <div className="mt-2 p-2 bg-orange-900 bg-opacity-50 rounded text-center">
                      <span className="text-orange-300 text-sm">未生成属性</span>
                    </div>
                  )}
                </div>
              ))}
              </div>
            )}
          </div>
        )}

        {activeTab === 'comparison' && (
          <div>
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-white mb-3">
                属性对比 ({selectedCardsForComparison.length}/5)
              </h3>
              <p className="text-slate-400 text-sm">
                在属性画廊中点击卡牌来选择要对比的角色（最多5个）
              </p>
            </div>

            {selectedCardsForComparison.length > 1 ? (
              <AttributeComparison cards={selectedCardsForComparison} />
            ) : (
              <div className="text-center py-12 bg-slate-800 rounded-lg">
                <div className="text-6xl mb-4">⚖️</div>
                <div className="text-xl text-slate-300 mb-2">选择角色进行对比</div>
                <div className="text-slate-400">
                  请在属性画廊中选择至少2个已生成属性的角色
                </div>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
