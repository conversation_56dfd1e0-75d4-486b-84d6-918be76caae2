
import React, { useState, useMemo, useEffect } from 'react';
import { ChevronLeftIcon, ChevronRightIcon } from './icons';
import { DateCell, Task, TaskStatus } from '../types';
import { getFormattedDate, getTasksForDate, isTaskCompletedOnDate } from '../utils/dateUtils';

interface CalendarViewProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  tasks: Task[]; 
}

const CalendarView: React.FC<CalendarViewProps> = ({ selectedDate, onDateChange, tasks }) => {
  const [currentMonthDate, setCurrentMonthDate] = useState(new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1));

  // Effect to synchronize currentMonthDate with selectedDate's month (only when selectedDate changes)
  useEffect(() => {
    const newCurrentMonthBase = new Date(selectedDate.getFullYear(), selectedDate.getMonth(), 1);
    newCurrentMonthBase.setHours(0,0,0,0); // Normalize

    const currentMonthDateBase = new Date(currentMonthDate.getFullYear(), currentMonthDate.getMonth(), 1);
    currentMonthDateBase.setHours(0,0,0,0); // Normalize

    if (newCurrentMonthBase.getTime() !== currentMonthDateBase.getTime()) {
      setCurrentMonthDate(newCurrentMonthBase);
    }
  }, [selectedDate]); // 只依赖 selectedDate，避免无限循环

  const daysInMonth = (year: number, month: number): number => new Date(year, month + 1, 0).getDate();
  const firstDayOfMonth = (year: number, month: number): number => new Date(year, month, 1).getDay(); // 0 (Sun) - 6 (Sat)

  const getTaskStatusForDate = (date: Date): TaskStatus => {
    const tasksForDay = getTasksForDate(tasks, date);
    if (tasksForDay.length === 0) return 'none';
    
    const allCompleted = tasksForDay.every(task => isTaskCompletedOnDate(task, date));
    return allCompleted ? 'completed' : 'pending';
  };

  const generateCalendarGrid = (): DateCell[][] => {
    const year = currentMonthDate.getFullYear();
    const month = currentMonthDate.getMonth();
    const numDays = daysInMonth(year, month);
    const firstDay = firstDayOfMonth(year, month);

    const today = new Date();
    today.setHours(0,0,0,0); // Normalize today for comparison
    
    // Ensure selectedDate for comparison is also normalized to midnight
    const normalizedSelectedDate = new Date(selectedDate); 
    normalizedSelectedDate.setHours(0,0,0,0);

    const grid: DateCell[][] = [];
    let dayCounter = 1;
    let cellsInRow: DateCell[] = [];

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDay; i++) {
      const prevMonthDays = daysInMonth(year, month - 1);
      const prevMonthYear = month === 0 ? year -1 : year;
      const prevMonthVal = month === 0 ? 11 : month -1; // Renamed to avoid conflict
      const date = new Date(prevMonthYear, prevMonthVal, prevMonthDays - firstDay + i + 1);
      date.setHours(0,0,0,0);
      cellsInRow.push({ date, isCurrentMonth: false, isToday: false, isSelected: false, taskStatus: 'none' });
    }

    while (dayCounter <= numDays) {
      if (cellsInRow.length === 7) {
        grid.push(cellsInRow);
        cellsInRow = [];
      }
      const date = new Date(year, month, dayCounter);
      date.setHours(0,0,0,0); // Normalize cell date to midnight for consistent comparison

      cellsInRow.push({
        date,
        isCurrentMonth: true,
        isToday: getFormattedDate(date) === getFormattedDate(today),
        isSelected: getFormattedDate(date) === getFormattedDate(normalizedSelectedDate),
        taskStatus: getTaskStatusForDate(date),
      });
      dayCounter++;
    }
    
    // Add empty cells for days after the last day of the month
    const remainingCells = 7 - cellsInRow.length;
    if (remainingCells > 0 && remainingCells < 7) {
        for (let i = 0; i < remainingCells; i++) {
            const nextMonthYear = month === 11 ? year + 1 : year;
            const nextMonthVal = month === 11 ? 0 : month + 1; // Renamed
            const date = new Date(nextMonthYear, nextMonthVal, i + 1);
            date.setHours(0,0,0,0);
            cellsInRow.push({ date, isCurrentMonth: false, isToday: false, isSelected: false, taskStatus: 'none' });
        }
    }

    if (cellsInRow.length > 0) {
      grid.push(cellsInRow);
    }
    
    return grid;
  };

  const calendarGrid = useMemo(() => generateCalendarGrid(), [currentMonthDate, selectedDate, tasks]);

  const handlePrevMonth = () => {
    setCurrentMonthDate(prev => {
        const newDate = new Date(prev.getFullYear(), prev.getMonth() - 1, 1);
        newDate.setHours(0,0,0,0);
        return newDate;
    });
  };

  const handleNextMonth = () => {
    setCurrentMonthDate(prev => {
        const newDate = new Date(prev.getFullYear(), prev.getMonth() + 1, 1);
        newDate.setHours(0,0,0,0);
        return newDate;
    });
  };

  const handleDayClick = (date: Date) => {
    // date object from cell is already normalized to 00:00:00 by generateCalendarGrid
    onDateChange(date);
  };

  const dayNames = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];

  return (
    <div className="p-4 bg-slate-800 rounded-lg shadow-lg">
      <div className="flex items-center justify-between mb-4">
        <button onClick={handlePrevMonth} className="p-2 rounded-full hover:bg-slate-700 transition-colors" aria-label="上个月">
          <ChevronLeftIcon className="w-6 h-6 text-sky-400" />
        </button>
        <h3 className="text-xl font-semibold text-sky-400" aria-live="polite">
          {currentMonthDate.getFullYear()}年 {currentMonthDate.getMonth() + 1}月
        </h3>
        <button onClick={handleNextMonth} className="p-2 rounded-full hover:bg-slate-700 transition-colors" aria-label="下个月">
          <ChevronRightIcon className="w-6 h-6 text-sky-400" />
        </button>
      </div>
      <div className="grid grid-cols-7 gap-1 text-center">
        {dayNames.map(day => (
          <div key={day} className="text-xs font-medium text-slate-400 py-2" aria-hidden="true">{day}</div>
        ))}
        {calendarGrid.flat().map((cell, index) => (
          <button
            key={index}
            onClick={() => handleDayClick(cell.date)}
            className={`p-1 h-12 w-full flex flex-col items-center justify-center rounded-lg text-sm transition-all duration-150 relative
              ${!cell.isCurrentMonth ? 'text-slate-600 cursor-default' : 'text-slate-200'}
              ${cell.isToday && cell.isCurrentMonth ? 'bg-sky-700 font-semibold border border-sky-500' : ''}
              ${cell.isSelected && cell.isCurrentMonth ? 'ring-2 ring-sky-300 bg-sky-500 text-white font-bold scale-105 shadow-lg' : ''}
              ${cell.isCurrentMonth && !cell.isSelected && !cell.isToday ? 'hover:bg-slate-700' : ''}
              ${!cell.isCurrentMonth && !cell.isSelected && !cell.isToday ? 'opacity-50' : ''}
            `}
            disabled={!cell.isCurrentMonth} 
            aria-pressed={cell.isSelected && cell.isCurrentMonth}
            aria-label={`${cell.date.getFullYear()}年${cell.date.getMonth()+1}月${cell.date.getDate()}日 ${cell.isToday && cell.isCurrentMonth ? '(今天)' : ''} ${cell.isSelected && cell.isCurrentMonth ? '(已选择)' : ''}`}
          >
            <span>{cell.date.getDate()}</span>
            {cell.isCurrentMonth && cell.taskStatus && cell.taskStatus !== 'none' && (
                <div className={`mt-0.5 w-1.5 h-1.5 rounded-full ${cell.taskStatus === 'completed' ? 'bg-green-400' : 'bg-yellow-400'}`}></div>
            )}
          </button>
        ))}
      </div>
    </div>
  );
};

export default CalendarView;
