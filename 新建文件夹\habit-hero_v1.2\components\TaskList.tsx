
import React from 'react';
import { Task, TaskType } from '../types';
import TaskItem from './TaskItem';
import { getFormattedDate, isTaskCompletedOnDate, getTasksForDate } from '../utils/dateUtils';

interface TaskListProps {
  tasks: Task[];
  selectedDate: Date;
  onToggleComplete: (taskId: string, date: Date) => void;
  onDeleteTask: (taskId: string) => void;
  onToggleHighlight?: (taskId: string) => void; // 新增：切换重要标记
}

const TaskList: React.FC<TaskListProps> = ({ tasks, selectedDate, onToggleComplete, onDeleteTask, onToggleHighlight }) => {
  // getTasksForDate already filters by creationDate internally
  const tasksForSelectedDate = getTasksForDate(tasks, selectedDate);

  // 按重要性排序：重要任务在前，然后按创建时间排序
  const sortedTasks = [...tasksForSelectedDate].sort((a, b) => {
    // 首先按重要性排序
    if (a.isHighlighted && !b.isHighlighted) return -1;
    if (!a.isHighlighted && b.isHighlighted) return 1;

    // 如果重要性相同，按创建时间排序（新的在前）
    const aTime = new Date(a.createdAt || 0).getTime();
    const bTime = new Date(b.createdAt || 0).getTime();
    return bTime - aTime;
  });

  const formattedSelectedDate = getFormattedDate(selectedDate);
  
  if (sortedTasks.length === 0) {
    return <p className="text-center text-slate-400 py-8">在 {formattedSelectedDate} 没有计划任务哦 ✨</p>;
  }

  return (
    <div className="space-y-4">
      {sortedTasks.map((task, index) => {
        // isTaskCompletedOnDate also considers creationDate
        const completedStatus = isTaskCompletedOnDate(task, selectedDate);
        return (
          <div
            key={task.id}
            className="animate-fadeInUp"
            style={{
              animationDelay: `${index * 100}ms`,
              animationFillMode: 'both'
            }}
          >
            <TaskItem
              task={task}
              selectedDate={selectedDate}
              onToggleComplete={onToggleComplete}
              onDelete={onDeleteTask}
              onToggleHighlight={onToggleHighlight}
              isCompleted={completedStatus}
            />
          </div>
        );
      })}
    </div>
  );
};

export default TaskList;
