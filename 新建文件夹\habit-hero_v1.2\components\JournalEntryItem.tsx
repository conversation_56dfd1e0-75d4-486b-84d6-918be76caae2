
import React from 'react';
import { JournalEntry, JournalType } from '../types';
import { TrashIcon } from './icons';

interface JournalEntryItemProps {
  entry: JournalEntry;
  onDelete: (entryId: string) => void;
}

const JournalEntryItem: React.FC<JournalEntryItemProps> = ({ entry, onDelete }) => {
  // 获取日志类型信息
  const journalType = entry.type || JournalType.OUTPUT; // 默认为输出型
  const typeInfo = journalType === JournalType.OUTPUT
    ? { label: '输出', icon: '📝', color: 'bg-blue-500/20 text-blue-300 border-blue-500/30' }
    : { label: '输入', icon: '💭', color: 'bg-purple-500/20 text-purple-300 border-purple-500/30' };

  return (
    <div className="flex items-start justify-between p-4 rounded-lg shadow-md bg-slate-700/70 border border-slate-600/50 hover:bg-slate-600/50 transition-colors duration-200">
      <div className="flex items-start space-x-3 flex-grow min-w-0">
        {entry.icon && <span className="text-2xl mt-0.5">{entry.icon}</span>}
        <div className="flex-grow min-w-0">
          <div className="flex items-start justify-between mb-2">
            <p className="text-slate-100 whitespace-pre-wrap break-words flex-grow">{entry.content}</p>
            <span className={`ml-3 px-2 py-1 rounded-full text-xs font-medium border flex-shrink-0 ${typeInfo.color}`}>
              {typeInfo.icon} {typeInfo.label}
            </span>
          </div>
          <p className="text-xs text-purple-300 opacity-80">
            记录于: {new Date(entry.createdAt).toLocaleDateString('zh-CN', { month: 'short', day: 'numeric', hour:'2-digit', minute:'2-digit' })}
          </p>
        </div>
      </div>
      <button 
        onClick={() => onDelete(entry.id)} 
        className="text-slate-500 hover:text-red-500 transition-colors p-1 rounded-full flex-shrink-0 ml-2"
        aria-label="删除日志"
      >
        <TrashIcon className="w-5 h-5" />
      </button>
    </div>
  );
};

export default JournalEntryItem;
