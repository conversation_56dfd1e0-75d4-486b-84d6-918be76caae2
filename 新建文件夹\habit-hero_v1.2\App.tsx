
import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Task, UserData, TaskType, ViewMode, JournalEntry, WeightRecord, ArmyCardData, DeployedCard, CustomCharacterClass } from './types';
import { useDatabase } from './hooks/useDatabase';
import { getFormattedDate, isTaskCompletedOnDate, getTasksForDate, getJournalEntriesForDate } from './utils/dateUtils';
import { importUserData, validateUserData } from './utils/dataMigration';
import AddTaskForm from './components/AddTaskForm';
import TaskList from './components/TaskList';
import Clock from './components/Clock';
import ProgressBar from './components/ProgressBar';
import CalendarView from './components/CalendarView';
import Modal from './components/Modal';
import { ListIcon, CalendarIcon, SparklesIcon, PlusIcon, PencilSquareIcon, QueueListIcon, DownloadIcon, UploadIcon, CogIcon } from './components/icons';
import AddJournalEntryForm from './components/AddJournalEntryForm';
import JournalEntryItem from './components/JournalEntryItem';
import TimelineView from './components/TimelineView';
import StatisticsView from './components/StatisticsView';
import SettingsView from './components/SettingsView';
// Army system imports
import { ArmyGachaSystem } from './components/ArmyGachaSystem';
import { ArmyGallery } from './components/ArmyGallery';
import { ArmyCardView } from './components/ArmyCardView';
import PowerAssessment from './components/PowerAssessment';
import WeightManagement from './components/WeightManagement';
import DateNavigation from './components/DateNavigation';
import { AttributeManagement } from './components/AttributeManagement';
import { AttributeDisplay } from './components/AttributeDisplay';
import { ThemeProvider, useTheme, getThemeClasses } from './contexts/ThemeContext';
import ApiConfigManager from './components/ApiConfigManager';
import './styles/animations.css';

const getStartOfDay = (date: Date): Date => {
  const newDate = new Date(date);
  newDate.setHours(0, 0, 0, 0);
  return newDate;
};

const AppContent: React.FC = () => {
  const { theme } = useTheme();
  const themeClasses = getThemeClasses(theme);
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [previousViewMode, setPreviousViewMode] = useState<ViewMode>('todo');
  const {
    userData,
    isLoaded,
    saveArmyCard,
    updateUserPoints,
    addTotalPoints,
    setTotalPoints,
    updateArmyStylePrompt,
    deleteArmyCard,
    saveTask,
    deleteTask,
    saveJournalEntry,
    deleteJournalEntry,
    saveWeightRecord,
    deleteWeightRecord,
    getStats,
    saveAttributeSchema,
    isUsingDatabase,
    // 分页相关
    loadMoreArmyCards,
    armyCardsHasMore,
    isLoadingMoreCards,
    armyCardsTotal,
  } = useDatabase();

  const [selectedDate, setSelectedDate] = useState(getStartOfDay(new Date()));
  const [viewMode, setViewMode] = useState<ViewMode>('todo');
  const [isAddTaskModalOpen, setIsAddTaskModalOpen] = useState(false);

  // 视图切换动画处理
  const handleViewModeChange = (newViewMode: ViewMode) => {
    if (newViewMode === viewMode) return;

    setIsTransitioning(true);
    setPreviousViewMode(viewMode);

    setTimeout(() => {
      setViewMode(newViewMode);
      setTimeout(() => {
        setIsTransitioning(false);
      }, 50);
    }, 150);
  };
  const [isAddJournalModalOpen, setIsAddJournalModalOpen] = useState(false);
  const [feedbackMessage, setFeedbackMessage] = useState<string | null>(null);

  // Army system state
  const [selectedArmyCard, setSelectedArmyCard] = useState<ArmyCardData | null>(null);
  const [armyCardDetailModal, setArmyCardDetailModal] = useState<ArmyCardData | null>(null);
  const [customClassRefreshTrigger, setCustomClassRefreshTrigger] = useState(0);

  // API配置管理器状态
  const [isApiConfigOpen, setIsApiConfigOpen] = useState(false);

  // 处理自定义职业变化
  const handleCustomClassChange = useCallback(() => {
    setCustomClassRefreshTrigger(prev => prev + 1);
  }, []);

  useEffect(() => {
    if (feedbackMessage) {
      const timer = setTimeout(() => setFeedbackMessage(null), 3000);
      return () => clearTimeout(timer);
    }
  }, [feedbackMessage]);
  
  const handleSelectedDateChange = (date: Date) => {
    setSelectedDate(getStartOfDay(date));
  };

  const handleAddTask = async (task: Task) => {
    try {
      await saveTask(task);
      setFeedbackMessage(`任务 "${task.title}" 已添加！`);
      setIsAddTaskModalOpen(false);
    } catch (error) {
      console.error('Error adding task:', error);
      setFeedbackMessage('添加任务失败');
    }
  };

  const handleDeleteTask = async (taskId: string) => {
    try {
      await deleteTask(taskId);
      setFeedbackMessage("任务已删除。");
    } catch (error) {
      console.error('Error deleting task:', error);
      setFeedbackMessage("删除任务失败。");
    }
  };

  const handleToggleHighlight = async (taskId: string) => {
    try {
      const task = userData.tasks.find(t => t.id === taskId);
      if (!task) return;

      const updatedTask = { ...task, isHighlighted: !task.isHighlighted };
      await saveTask(updatedTask);
      setFeedbackMessage(updatedTask.isHighlighted ? "任务已标记为重要" : "任务已取消重要标记");
    } catch (error) {
      console.error('Error toggling task highlight:', error);
      setFeedbackMessage("更新任务重要标记失败");
    }
  };

  const handleToggleComplete = async (taskId: string, date: Date) => {
    const task = userData.tasks.find(t => t.id === taskId);
    if (!task) return;

    const dateStr = getFormattedDate(date); // YYYY-MM-DD for the day in question
    let pointsChange = 0;
    let taskCompletedNow = false;
    let taskUncompletedNow = false;

    const updatedTask = { ...task };
    let newCompletedDatesISO = task.completedDates ? [...task.completedDates] : [];
    let newIsCompletedForOnceTask = task.isCompleted;
    let newActualCompletionDate = task.actualCompletionDate;

    if (task.type === TaskType.ONCE) {
      if (!task.isCompleted) { // Completing a ONCE task
        newIsCompletedForOnceTask = true;
        newActualCompletionDate = new Date().toISOString(); // Precise completion timestamp
        pointsChange = task.points;
        taskCompletedNow = true;
      } else { // Un-completing a ONCE task
        newIsCompletedForOnceTask = false;
        newActualCompletionDate = undefined;
        pointsChange = -task.points;
        taskUncompletedNow = true;
      }
    } else { // DAILY or WEEKLY tasks
      // Check if there's already a completion entry for this specific day
      const existingCompletionForDay = newCompletedDatesISO.find(isoStr => getFormattedDate(new Date(isoStr)) === dateStr);

      if (existingCompletionForDay) { // Task was completed today, now un-completing for this day
        newCompletedDatesISO = newCompletedDatesISO.filter(isoStr => getFormattedDate(new Date(isoStr)) !== dateStr);
        pointsChange = -task.points;
        taskUncompletedNow = true;
      } else { // Task was not completed today (or for this day), now completing
        newCompletedDatesISO.push(new Date().toISOString()); // Add precise completion timestamp
        pointsChange = task.points;
        taskCompletedNow = true;
      }
    }

    updatedTask.completedDates = newCompletedDatesISO;
    updatedTask.isCompleted = newIsCompletedForOnceTask;
    updatedTask.actualCompletionDate = newActualCompletionDate;

    try {
      await saveTask(updatedTask);
      await updateUserPoints(Math.max(0, userData.points + pointsChange));

      // 如果获得了积分，增加总积分统计
      if (pointsChange > 0) {
        await addTotalPoints(pointsChange);
      }

      if (taskCompletedNow) {
        setFeedbackMessage(`任务 "${task.title}" 完成！+${pointsChange} 积分。`);
      } else if (taskUncompletedNow) {
        setFeedbackMessage(`任务 "${task.title}" 标记为未完成。`);
      }
    } catch (error) {
      console.error('Error updating task completion:', error);
      setFeedbackMessage('更新任务状态失败');
    }
  };
  
  const handleAddJournalEntry = async (entry: JournalEntry) => {
    try {
      await saveJournalEntry(entry);
      setFeedbackMessage(`日志 "${entry.content.substring(0, 20)}..." 已添加！`);
      setIsAddJournalModalOpen(false);

      // 如果是从托盘打开的，添加日志后自动关闭窗口
      if (isOpenedFromTray) {
        setIsOpenedFromTray(false); // 重置标记
        // 延迟一下让用户看到反馈消息，然后关闭窗口
        setTimeout(() => {
          if (window.electronAPI?.hideWindow) {
            window.electronAPI.hideWindow();
          }
        }, 1500);
      }
    } catch (error) {
      console.error('Error adding journal entry:', error);
      setFeedbackMessage('添加日志失败');
    }
  };

  const handleDeleteJournalEntry = async (entryId: string) => {
    try {
      await deleteJournalEntry(entryId);
      setFeedbackMessage("日志已删除。");
    } catch (error) {
      console.error('Error deleting journal entry:', error);
      setFeedbackMessage("删除日志失败。");
    }
  };

  // 体重管理处理函数
  const handleSaveWeight = async (record: WeightRecord) => {
    try {
      await saveWeightRecord(record);
      setFeedbackMessage(`体重记录已保存：${record.weight}kg`);
    } catch (error) {
      console.error('Error saving weight record:', error);
      setFeedbackMessage('保存体重记录失败');
    }
  };

  const handleDeleteWeight = async (id: string) => {
    try {
      await deleteWeightRecord(id);
      setFeedbackMessage('体重记录已删除');
    } catch (error) {
      console.error('Error deleting weight record:', error);
      setFeedbackMessage('删除体重记录失败');
    }
  };





  // Army system handlers
  const handleArmyCardGenerated = async (card: ArmyCardData) => {
    console.log('Generated army card:', card);
    try {
      await saveArmyCard(card);
      setSelectedArmyCard(card);
      setFeedbackMessage(`成功招募 ${card.name}！`);
      console.log('Army card saved successfully');
    } catch (error) {
      console.error('Error saving army card:', error);
      setFeedbackMessage('保存军队卡片失败');
    }
  };



  const handleArmyStylePromptChange = async (prompt: string) => {
    try {
      await updateArmyStylePrompt(prompt);
    } catch (error) {
      console.error('Error updating army style prompt:', error);
    }
  };



  const handleParadeRecord = async (description: string) => {
    try {
      const journalEntry = {
        id: Date.now().toString(),
        date: getFormattedDate(new Date()), // YYYY-MM-DD format
        content: description,
        icon: '🎖️', // 阅兵图标
        createdAt: new Date().toISOString()
      };

      console.log('Recording parade to timeline:', journalEntry);
      await saveJournalEntry(journalEntry);
      console.log('Parade recorded successfully');
    } catch (error) {
      console.error('Failed to record parade:', error);
    }
  };

  const handleArmyCardClick = (card: ArmyCardData) => {
    setSelectedArmyCard(card);
    setArmyCardDetailModal(card);
  };

  const handleArmyCardDelete = async (cardId: string) => {
    try {
      await deleteArmyCard(cardId);
      if (selectedArmyCard?.id === cardId) {
        setSelectedArmyCard(null);
      }
      setFeedbackMessage("军队卡片已删除。");
    } catch (error) {
      console.error('Error deleting army card:', error);
      setFeedbackMessage("删除军队卡片失败。");
    }
  };

  const handleArmyCardUpdate = async (updatedCard: ArmyCardData) => {
    try {
      console.log('App: 开始更新军队卡片', updatedCard);
      await saveArmyCard(updatedCard);
      console.log('App: 军队卡片保存完成');

      // 如果更新的是当前选中的卡片，更新选择
      if (selectedArmyCard?.id === updatedCard.id) {
        setSelectedArmyCard(updatedCard);
      }
      setFeedbackMessage("军队卡片已更新。");
    } catch (error) {
      console.error('Error updating army card:', error);
      setFeedbackMessage("更新军队卡片失败。");
    }
  };

  const handlePointsChange = async (newPoints: number) => {
    try {
      await updateUserPoints(Math.max(0, newPoints));
    } catch (error) {
      console.error('Error updating points:', error);
    }
  };

  const handleAttributeSchemaUpdate = async (schema: any) => {
    try {
      await saveAttributeSchema(schema);
      setFeedbackMessage('属性模式已保存');
    } catch (error) {
      console.error('Error saving attribute schema:', error);
      setFeedbackMessage('保存属性模式失败');
    }
  };

  const handleRefreshData = async () => {
    try {
      console.log('手动刷新数据...');
      // 重新加载用户数据
      window.location.reload(); // 简单粗暴的刷新方法
      setFeedbackMessage('数据已刷新');
    } catch (error) {
      console.error('Error refreshing data:', error);
      setFeedbackMessage('刷新数据失败');
    }
  };
  


  const tasksForSelectedDate = useMemo(() => getTasksForDate(userData.tasks, selectedDate), [userData.tasks, selectedDate]);
  const journalEntriesForSelectedDate = useMemo(() => getJournalEntriesForDate(userData.journalEntries, selectedDate), [userData.journalEntries, selectedDate]);
  
  const completedTasksTodayCount = useMemo(() => {
    // isTaskCompletedOnDate will now correctly interpret completedDates with ISO strings
    return tasksForSelectedDate.filter(task => isTaskCompletedOnDate(task, selectedDate)).length;
  }, [tasksForSelectedDate, selectedDate, userData.tasks]);


  const renderView = () => {
    const getViewContent = () => {
      switch (viewMode) {
      case 'calendar':
        return (
          <div className="grid grid-cols-1 xl:grid-cols-3 gap-8">
            {/* 左侧：日历 */}
            <div className="xl:col-span-1">
              <CalendarView selectedDate={selectedDate} onDateChange={handleSelectedDateChange} tasks={userData.tasks} />
            </div>

            {/* 右侧：选中日期的详情 */}
            <div className="xl:col-span-2 space-y-6">
              {/* 任务部分 */}
              <div>
                <h2 className="text-xl font-semibold text-sky-300 mb-4">
                    {getFormattedDate(selectedDate)} 的任务
                </h2>
                <TaskList tasks={userData.tasks} selectedDate={selectedDate} onToggleComplete={handleToggleComplete} onDeleteTask={handleDeleteTask} onToggleHighlight={handleToggleHighlight}/>
              </div>

              {/* 日志部分 */}
              <div>
                <h2 className="text-xl font-semibold text-pink-400 mb-4">
                    {getFormattedDate(selectedDate)} 的日志
                </h2>
                {journalEntriesForSelectedDate.length > 0 ? (
                  <div className="space-y-3">
                    {journalEntriesForSelectedDate.map(entry => (
                        <JournalEntryItem key={entry.id} entry={entry} onDelete={handleDeleteJournalEntry} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-slate-400 py-6 bg-slate-800/30 rounded-lg border-2 border-dashed border-slate-600">
                    <p>当天没有日志记录。</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        );

      case 'army':
        return (
          <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
            {/* 左上：抽卡系统 */}
            <div>
              <ArmyGachaSystem
                userPoints={userData.points}
                onPointsChange={handlePointsChange}
                onCardGenerated={handleArmyCardGenerated}
                onUnitDeployed={handleUnitDeployed}
                stylePrompt={userData.armyStylePrompt || ''}
                onStylePromptChange={handleArmyStylePromptChange}
                existingCards={userData.armyCards}
                onCustomClassChange={handleCustomClassChange}
                attributeSchema={userData.attributeSchema}
              />
            </div>

            {/* 右上：战力评估 */}
            <div>
              <PowerAssessment
                armyCards={userData.armyCards}
                userPoints={userData.points}
                onPointsChange={handlePointsChange}
              />
            </div>

            {/* 下方：军队画廊 - 跨两列 */}
            <div className="xl:col-span-2">
              <ArmyGallery
                armyCards={userData.armyCards}
                onCardClick={handleArmyCardClick}
                onCardDelete={handleArmyCardDelete}
                onCardUpdate={handleArmyCardUpdate}
                selectedCard={selectedArmyCard}
                refreshTrigger={customClassRefreshTrigger}
                onLoadMore={loadMoreArmyCards}
                hasMore={armyCardsHasMore}
                isLoadingMore={isLoadingMoreCards}
                totalCards={armyCardsTotal}
              />
            </div>
          </div>
        );

      case 'weight':
        return (
          <WeightManagement
            weightRecords={userData.weightRecords || []}
            onSaveWeight={handleSaveWeight}
            onDeleteWeight={handleDeleteWeight}
          />
        );
      case 'attributes':
        return (
          <AttributeManagement
            armyCards={userData.armyCards}
            onCardUpdate={handleArmyCardUpdate}
            userPoints={userData.points}
            onPointsChange={handlePointsChange}
            currentSchema={userData.attributeSchema}
            onSchemaUpdate={handleAttributeSchemaUpdate}
            onRefreshData={handleRefreshData}
          />
        );
      case 'timeline':
        return <TimelineView tasks={userData.tasks} journalEntries={userData.journalEntries} />;
      case 'statistics':
        return <StatisticsView tasks={userData.tasks} journalEntries={userData.journalEntries} armyCards={userData.armyCards} />;
      case 'settings':
        return (
          <SettingsView
            userData={userData}
            onExportData={handleExportData}
            onImportData={handleImportData}
            onClearDatabase={handleClearDatabase}
            isUsingDatabase={isUsingDatabase}
            onOpenApiConfig={() => setIsApiConfigOpen(true)}
          />
        );
      case 'todo':
      default:
        return (
          <>
            {/* 日期导航 */}
            <div className="mb-6">
              <DateNavigation
                selectedDate={selectedDate}
                onDateChange={handleSelectedDateChange}
                showCalendarButton={true}
                onCalendarClick={() => setViewMode('calendar')}
              />
            </div>

            {/* 顶部进度条和操作按钮 */}
            <div className="mb-6">
              <div className="flex justify-between items-center mb-4">
                <div className="flex-1 mr-4">
                  <ProgressBar current={completedTasksTodayCount} total={tasksForSelectedDate.length} />
                </div>
                <button
                    onClick={() => setIsAddJournalModalOpen(true)}
                    className="bg-purple-500 hover:bg-purple-600 text-white font-semibold py-2 px-4 rounded-lg shadow-md flex items-center transition-colors transform hover:scale-105"
                    aria-label="添加新日志"
                >
                    <PencilSquareIcon className="w-5 h-5 mr-2" /> 添加日志
                </button>
              </div>
            </div>

            {/* 双列布局：任务和日志 */}
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* 左列：今日待办 */}
              <div>
                <h2 className="text-2xl font-semibold text-sky-300 mb-4">今日待办</h2>
                <TaskList tasks={userData.tasks} selectedDate={selectedDate} onToggleComplete={handleToggleComplete} onDeleteTask={handleDeleteTask} onToggleHighlight={handleToggleHighlight} />
              </div>

              {/* 右列：今日日志 */}
              <div>
                <h2 className="text-2xl font-semibold text-pink-400 mb-4">今日日志</h2>
                {journalEntriesForSelectedDate.length > 0 ? (
                  <div className="space-y-3">
                    {journalEntriesForSelectedDate.map(entry => (
                      <JournalEntryItem key={entry.id} entry={entry} onDelete={handleDeleteJournalEntry} />
                    ))}
                  </div>
                ) : (
                  <div className="text-center text-slate-400 py-8 bg-slate-800/30 rounded-lg border-2 border-dashed border-slate-600">
                    <PencilSquareIcon className="w-12 h-12 mx-auto mb-3 text-slate-500" />
                    <p className="text-lg font-medium mb-2">还没有日志记录</p>
                    <p className="text-sm">点击右上角按钮开始记录今天的想法吧！</p>
                  </div>
                )}
              </div>
            </div>
          </>
        );
    }
  };

  return (
    <div className="relative overflow-hidden">
      <div className={`transition-all duration-300 transform ${
        isTransitioning
          ? 'opacity-0 translate-x-4 scale-95'
          : 'opacity-100 translate-x-0 scale-100'
      }`}>
        {getViewContent()}
      </div>
    </div>
  );
};

  const NavButton: React.FC<{targetView: ViewMode; icon: React.ReactNode; label: string}> = ({targetView, icon, label}) => (
    <button
        onClick={() => handleViewModeChange(targetView)}
        className={`group flex-1 flex flex-col items-center justify-center p-3 rounded-xl transition-all duration-300 ease-out transform relative overflow-hidden
            ${viewMode === targetView
              ? 'bg-gradient-to-br from-sky-500 to-sky-600 text-white shadow-lg shadow-sky-500/25 scale-105'
              : 'bg-slate-700/50 hover:bg-slate-600/70 text-slate-300 hover:text-white hover:scale-105 hover:shadow-lg'
            }`}
        aria-label={`切换到${label}视图`}
    >
        {/* 激活状态的光晕效果 */}
        {viewMode === targetView && (
          <div className="absolute inset-0 bg-gradient-to-br from-sky-400/20 to-transparent rounded-xl animate-pulse" />
        )}

        {/* 悬停时的光效 */}
        <div className="absolute inset-0 bg-gradient-to-br from-white/0 to-white/0 group-hover:from-white/10 group-hover:to-transparent rounded-xl transition-all duration-300" />

        <div className={`relative z-10 transition-transform duration-300 ${viewMode === targetView ? 'scale-110' : 'group-hover:scale-110'}`}>
          {icon}
        </div>
        <span className={`relative z-10 text-xs mt-1 font-medium transition-all duration-300 ${viewMode === targetView ? 'font-semibold' : ''}`}>
          {label}
        </span>
    </button>
  );

  const SideNavButton: React.FC<{targetView: ViewMode; icon: React.ReactNode; label: string}> = ({targetView, icon, label}) => (
    <button
        onClick={() => handleViewModeChange(targetView)}
        className={`group w-full flex items-center space-x-4 p-4 rounded-xl transition-all duration-300 ease-out text-left relative overflow-hidden transform
            ${viewMode === targetView
              ? 'bg-gradient-to-r from-sky-500 to-sky-600 text-white shadow-lg shadow-sky-500/25 translate-x-1'
              : 'text-slate-300 hover:bg-slate-700/70 hover:text-white hover:translate-x-2 hover:shadow-lg'
            }`}
        aria-label={`切换到${label}视图`}
    >
        {/* 激活状态的左侧指示条 */}
        <div className={`absolute left-0 top-0 bottom-0 w-1 bg-white rounded-r-full transition-all duration-300 ${
          viewMode === targetView ? 'opacity-100' : 'opacity-0'
        }`} />

        {/* 悬停时的背景光效 */}
        <div className="absolute inset-0 bg-gradient-to-r from-white/0 to-white/0 group-hover:from-white/5 group-hover:to-transparent rounded-xl transition-all duration-300" />

        {/* 激活状态的光晕 */}
        {viewMode === targetView && (
          <div className="absolute inset-0 bg-gradient-to-r from-sky-400/10 to-transparent rounded-xl animate-pulse" />
        )}

        <span className={`relative z-10 flex-shrink-0 transition-transform duration-300 ${
          viewMode === targetView ? 'scale-110' : 'group-hover:scale-110'
        }`}>
          {icon}
        </span>
        <span className={`relative z-10 font-medium transition-all duration-300 ${
          viewMode === targetView ? 'font-semibold tracking-wide' : 'group-hover:font-semibold'
        }`}>
          {label}
        </span>

        {/* 右侧箭头指示器 */}
        <div className={`relative z-10 ml-auto transition-all duration-300 ${
          viewMode === targetView ? 'opacity-100 translate-x-0' : 'opacity-0 -translate-x-2 group-hover:opacity-70 group-hover:translate-x-0'
        }`}>
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
          </svg>
        </div>
    </button>
  );

  const handleExportData = async () => {
    try {
      // 直接使用当前的userData（来自数据库）
      const dataToExport = {
        version: 'v4_army_system',
        exportDate: new Date().toISOString(),
        data: userData
      };

      const dataString = JSON.stringify(dataToExport, null, 2);

      // 确保window.electronAPI存在
      if (typeof window.electronAPI !== 'undefined') {
        const result = await window.electronAPI.exportData(dataString);

        if (result.success) {
          setFeedbackMessage(`数据已成功导出到: ${result.path}`);
        } else {
          setFeedbackMessage("导出数据失败");
        }
      } else {
        // 浏览器环境下的备选方案
        const blob = new Blob([dataString], { type: 'application/json' });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `habit-hero-backup-${getFormattedDate(new Date())}.json`;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
        setFeedbackMessage("数据已准备好下载");
      }
    } catch (error) {
      console.error("导出数据时出错:", error);
      setFeedbackMessage("导出数据时出错");
    }
  };

  const handleImportData = async () => {
    try {
      if (typeof window.electronAPI !== 'undefined') {
        const result = await window.electronAPI.importData();

        if (result.success && result.data) {
          try {
            const importedData = importUserData(result.data);

            // 验证数据格式
            if (!validateUserData(importedData)) {
              throw new Error('Invalid data structure after migration');
            }

            console.log('Importing data:', importedData);
            await importDataToDatabase(importedData);
            setFeedbackMessage("数据导入成功！");
          } catch (parseError) {
            console.error('Import error:', parseError);
            setFeedbackMessage("导入的文件格式无效: " + parseError.message);
          }
        } else if (result.error) {
          setFeedbackMessage(`导入失败: ${result.error}`);
        } else {
          setFeedbackMessage("导入取消或失败");
        }
      } else {
        // 浏览器环境下的备选方案
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';

        input.onchange = (e) => {
          const file = e.target?.files?.[0];
          if (!file) return;

          const reader = new FileReader();
          reader.onload = async (event) => {
            try {
              if (event.target?.result) {
                const importedData = importUserData(event.target.result as string);

                // 验证数据格式
                if (!validateUserData(importedData)) {
                  throw new Error('Invalid data structure after migration');
                }

                console.log('Importing data:', importedData);
                await importDataToDatabase(importedData);
                setFeedbackMessage("数据导入成功！");
              }
            } catch (error) {
              console.error('Import error:', error);
              setFeedbackMessage("导入的文件格式无效: " + error.message);
            }
          };
          reader.readAsText(file);
        };

        input.click();
      }
    } catch (error) {
      console.error("导入数据时出错:", error);
      setFeedbackMessage("导入数据时出错");
    }
  };

  const importDataToDatabase = async (importedData: UserData) => {
    try {
      console.log('Starting data import to database...');
      console.log('Tasks to import:', importedData.tasks.length);
      console.log('Journal entries to import:', importedData.journalEntries.length);
      console.log('Army cards to import:', importedData.armyCards.length);

      // 先更新积分
      console.log('Updating points to:', importedData.points);
      await updateUserPoints(importedData.points);

      // 更新总积分
      if (typeof importedData.totalPointsEarned === 'number') {
        console.log('Updating total points earned to:', importedData.totalPointsEarned);
        await setTotalPoints(importedData.totalPointsEarned);
      }

      // 更新军队风格提示
      if (importedData.armyStylePrompt) {
        console.log('Updating army style prompt:', importedData.armyStylePrompt);
        await updateArmyStylePrompt(importedData.armyStylePrompt);
      }



      // 批量保存任务
      console.log('Starting to save tasks...');
      for (let i = 0; i < importedData.tasks.length; i++) {
        const task = importedData.tasks[i];
        console.log(`Saving task ${i + 1}/${importedData.tasks.length}:`, task.title);
        try {
          await saveTask(task);
          console.log(`Task ${i + 1} saved successfully`);
        } catch (error) {
          console.error(`Failed to save task ${i + 1}:`, error);
          throw error;
        }
      }

      // 批量保存日志
      console.log('Starting to save journal entries...');
      for (let i = 0; i < importedData.journalEntries.length; i++) {
        const entry = importedData.journalEntries[i];
        console.log(`Saving journal entry ${i + 1}/${importedData.journalEntries.length}:`, entry.date);
        try {
          await saveJournalEntry(entry);
          console.log(`Journal entry ${i + 1} saved successfully`);
        } catch (error) {
          console.error(`Failed to save journal entry ${i + 1}:`, error);
          throw error;
        }
      }

      // 批量保存军队卡片
      console.log('Starting to save army cards...');
      for (let i = 0; i < importedData.armyCards.length; i++) {
        const card = importedData.armyCards[i];
        console.log(`Saving army card ${i + 1}/${importedData.armyCards.length}:`, card.name);
        try {
          await saveArmyCard(card);
          console.log(`Army card ${i + 1} saved successfully`);
        } catch (error) {
          console.error(`Failed to save army card ${i + 1}:`, error);
          throw error;
        }
      }



      console.log('All data imported successfully to database');

      // 等待一小段时间确保所有状态更新完成
      await new Promise(resolve => setTimeout(resolve, 100));

    } catch (error) {
      console.error('Error importing data to database:', error);
      throw error;
    }
  };

  const handleClearDatabase = async () => {
    try {
      // 清空所有数据
      const allTasks = userData.tasks;
      const allJournalEntries = userData.journalEntries;
      const allArmyCards = userData.armyCards;

      // 删除所有任务
      for (const task of allTasks) {
        await deleteTask(task.id);
      }

      // 删除所有日志
      for (const entry of allJournalEntries) {
        await deleteJournalEntry(entry.id);
      }

      // 删除所有军队卡片
      for (const card of allArmyCards) {
        await deleteArmyCard(card.id);
      }

      // 重置积分
      await updateUserPoints(100);

      // 清空军队风格提示
      await updateArmyStylePrompt('');

      setFeedbackMessage('数据库已清空');
    } catch (error) {
      console.error('Error clearing database:', error);
      setFeedbackMessage('清空数据库失败');
    }
  };

  // 跟踪是否从托盘打开的日志模态框
  const [isOpenedFromTray, setIsOpenedFromTray] = useState(false);

  // 在组件内部添加事件监听器
  useEffect(() => {
    const handleOpenJournalModal = () => {
      setIsAddJournalModalOpen(true);
      setIsOpenedFromTray(true); // 标记为从托盘打开
    };

    document.addEventListener('electron-open-journal-modal', handleOpenJournalModal);

    return () => {
      document.removeEventListener('electron-open-journal-modal', handleOpenJournalModal);
    };
  }, []);

  // Show loading state while data is being loaded
  if (!isLoaded) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 text-white flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-purple-400 mx-auto mb-4"></div>
          <h2 className="text-2xl font-semibold text-purple-400">加载中...</h2>
          <p className="text-slate-300 mt-2">
            {isUsingDatabase ? '正在连接数据库...' : '正在加载数据...'}
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-slate-900 text-slate-100 flex" style={{fontFamily: "'Inter', sans-serif"}}>
      {/* 桌面端侧边栏 */}
      <aside className="hidden lg:flex w-80 bg-gradient-to-b from-slate-800 via-slate-850 to-slate-900 border-r border-slate-700/50 flex-col desktop-sidebar relative overflow-hidden">
        {/* 背景装饰 */}
        <div className="absolute inset-0 bg-gradient-to-br from-sky-500/5 via-transparent to-purple-500/5" />
        <div className="absolute top-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-sky-400/50 to-transparent" />
        <div className="absolute bottom-0 left-0 w-full h-px bg-gradient-to-r from-transparent via-purple-400/30 to-transparent" />

        {/* 侧边栏头部 */}
        <div className="relative z-10 p-6 border-b border-slate-700/50">
          <div className="group cursor-default">
            <h1 className="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-sky-400 via-cyan-400 to-teal-500 transition-all duration-300 group-hover:from-sky-300 group-hover:via-cyan-300 group-hover:to-teal-400">
              习惯英雄
            </h1>
            <p className="text-slate-400 text-sm mt-2 font-medium transition-colors duration-300 group-hover:text-slate-300">
              征服你的任务，收集史诗卡片！
            </p>
          </div>
        </div>

        {/* 积分和统计显示 */}
        <div className="relative z-10 p-6 border-b border-slate-700/50 space-y-4">
          <div className="group relative flex items-center space-x-3 text-yellow-400 bg-gradient-to-r from-yellow-500/10 to-amber-500/10 border border-yellow-500/20 rounded-xl p-4 transition-all duration-500 hover:from-yellow-500/20 hover:to-amber-500/20 hover:border-yellow-500/40 hover:shadow-xl hover:shadow-yellow-500/20 hover:scale-105 cursor-pointer overflow-hidden">
            {/* 背景动画光效 */}
            <div className="absolute inset-0 bg-gradient-to-r from-yellow-400/0 via-yellow-400/5 to-yellow-400/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-1000" />

            {/* 积分图标 */}
            <div className="relative z-10">
              <SparklesIcon className="w-8 h-8 transition-all duration-500 group-hover:scale-125 group-hover:rotate-12 group-hover:text-yellow-300"/>
            </div>

            {/* 积分数值 */}
            <div className="relative z-10 flex-1">
              <div className="flex items-baseline space-x-2">
                <span className="text-3xl font-bold transition-all duration-300 group-hover:text-yellow-300" style={{fontFamily: "'Orbitron', sans-serif"}}>
                  {userData.points}
                </span>
                <span className="text-lg font-semibold transition-all duration-300 group-hover:text-yellow-300">积分</span>
              </div>
              <div className="text-xs text-yellow-500/70 mt-1 transition-all duration-300 group-hover:text-yellow-400/90">
                点击查看详情
              </div>
            </div>

            {/* 右侧装饰 */}
            <div className="relative z-10 opacity-30 group-hover:opacity-60 transition-opacity duration-300">
              <div className="w-2 h-2 bg-yellow-400 rounded-full animate-pulse" />
            </div>
          </div>

          {/* 今日统计 */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="group relative bg-gradient-to-br from-sky-500/10 to-blue-500/10 border border-sky-500/20 rounded-xl p-4 text-center transition-all duration-500 hover:from-sky-500/20 hover:to-blue-500/20 hover:border-sky-500/40 hover:shadow-xl hover:shadow-sky-500/20 hover:scale-110 cursor-pointer overflow-hidden">
              {/* 背景动画 */}
              <div className="absolute inset-0 bg-gradient-to-br from-sky-400/0 to-sky-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              {/* 数值显示 */}
              <div className="relative z-10">
                <div className="text-sky-400 font-bold text-2xl transition-all duration-500 group-hover:scale-125 group-hover:text-sky-300">
                  {completedTasksTodayCount}
                </div>
                <div className="text-slate-400 text-xs font-medium mt-2 transition-all duration-300 group-hover:text-slate-300">
                  已完成任务
                </div>
              </div>

              {/* 装饰元素 */}
              <div className="absolute top-2 right-2 w-1 h-1 bg-sky-400 rounded-full opacity-50 group-hover:opacity-100 transition-opacity duration-300" />
            </div>

            <div className="group relative bg-gradient-to-br from-pink-500/10 to-rose-500/10 border border-pink-500/20 rounded-xl p-4 text-center transition-all duration-500 hover:from-pink-500/20 hover:to-rose-500/20 hover:border-pink-500/40 hover:shadow-xl hover:shadow-pink-500/20 hover:scale-110 cursor-pointer overflow-hidden">
              {/* 背景动画 */}
              <div className="absolute inset-0 bg-gradient-to-br from-pink-400/0 to-pink-400/10 opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

              {/* 数值显示 */}
              <div className="relative z-10">
                <div className="text-pink-400 font-bold text-2xl transition-all duration-500 group-hover:scale-125 group-hover:text-pink-300">
                  {journalEntriesForSelectedDate.length}
                </div>
                <div className="text-slate-400 text-xs font-medium mt-2 transition-all duration-300 group-hover:text-slate-300">
                  今日日志
                </div>
              </div>

              {/* 装饰元素 */}
              <div className="absolute top-2 right-2 w-1 h-1 bg-pink-400 rounded-full opacity-50 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="relative z-10 flex-1 p-6">
          <div className="space-y-3">
            <SideNavButton targetView="todo" icon={<ListIcon className="w-5 h-5"/>} label="今日任务"/>
            <SideNavButton targetView="calendar" icon={<CalendarIcon className="w-5 h-5"/>} label="日历视图"/>
            <SideNavButton targetView="army" icon={<span className="text-lg">⚔️</span>} label="军队系统"/>
            <SideNavButton targetView="attributes" icon={<span className="text-lg">🎯</span>} label="属性管理"/>
            <SideNavButton targetView="weight" icon={<span className="text-lg">⚖️</span>} label="体重管理"/>
            <SideNavButton targetView="statistics" icon={<span className="text-lg">📊</span>} label="数据统计"/>
            <SideNavButton targetView="timeline" icon={<QueueListIcon className="w-5 h-5"/>} label="时间轴"/>
            <SideNavButton targetView="settings" icon={<CogIcon className="w-5 h-5"/>} label="设置"/>
          </div>
        </nav>

        {/* 侧边栏底部工具 */}
        <div className="p-4 border-t border-slate-700">
          <div className="space-y-2">
            <button
              onClick={handleExportData}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg flex items-center justify-center transition-colors text-sm"
              title="导出数据"
            >
              <DownloadIcon className="w-4 h-4 mr-2" />
              导出数据
            </button>
            <button
              onClick={handleImportData}
              className="w-full bg-green-600 hover:bg-green-700 text-white p-2 rounded-lg flex items-center justify-center transition-colors text-sm"
              title="导入数据"
            >
              <UploadIcon className="w-4 h-4 mr-2" />
              导入数据
            </button>
          </div>
        </div>
      </aside>

      {/* 主内容区域 */}
      <main className="flex-1 flex flex-col">
        {/* 顶部栏 */}
        <header className="bg-slate-800 border-b border-slate-700 p-4">
          <div className="flex justify-between items-center">
            <div>
              <h2 className="text-xl font-semibold text-slate-100">
                {viewMode === 'todo' && '今日任务'}
                {viewMode === 'calendar' && '日历视图'}
                {viewMode === 'army' && '军队系统'}
                {viewMode === 'attributes' && '属性管理'}
                {viewMode === 'weight' && '体重管理'}
                {viewMode === 'timeline' && '时间轴'}
                {viewMode === 'statistics' && '数据统计'}
                {viewMode === 'settings' && '设置'}
              </h2>
              <p className="text-slate-400 text-sm">
                {viewMode === 'todo' && '管理您的日常任务和目标'}
                {viewMode === 'calendar' && '查看任务和日志的日历视图'}
                {viewMode === 'army' && '招募和管理您的军队卡片'}
                {viewMode === 'camp' && '部署军队并管理您的营地'}
                {viewMode === 'attributes' && '为角色生成和管理属性数据'}
                {viewMode === 'weight' && '记录和追踪体重变化'}
                {viewMode === 'timeline' && '查看您的活动时间线'}
                {viewMode === 'statistics' && '查看详细的数据统计'}
                {viewMode === 'settings' && '应用设置和数据管理'}
              </p>
            </div>
            <div className="flex items-center space-x-3">
              <Clock />
            </div>
          </div>
        </header>

        {/* 内容区域 */}
        <div className="flex-1 p-4 lg:p-6 overflow-auto pb-20 lg:pb-6 pt-20 lg:pt-6">
          <div className="max-w-6xl mx-auto">
            {renderView()}
          </div>
        </div>
      </main>

      {/* 移动端底部导航栏 */}
      <nav className="lg:hidden fixed bottom-0 left-0 right-0 bg-slate-800 border-t border-slate-700 px-2 py-2 z-40">
        <div className="flex justify-around items-center space-x-1">
          <NavButton targetView="todo" icon={<ListIcon className="w-4 h-4"/>} label="任务"/>
          <NavButton targetView="calendar" icon={<CalendarIcon className="w-4 h-4"/>} label="日历"/>
          <NavButton targetView="army" icon={<span className="text-sm">⚔️</span>} label="军队"/>
          <NavButton targetView="camp" icon={<span className="text-sm">🏕️</span>} label="营地"/>
          <NavButton targetView="attributes" icon={<span className="text-sm">🎯</span>} label="属性"/>
          <NavButton targetView="weight" icon={<span className="text-sm">⚖️</span>} label="体重"/>
          <NavButton targetView="settings" icon={<CogIcon className="w-4 h-4"/>} label="设置"/>
        </div>
      </nav>

      {/* 移动端顶部栏（显示积分和工具） */}
      <div className="lg:hidden fixed top-0 left-0 right-0 bg-slate-800 border-b border-slate-700 p-3 z-40">
        <div className="flex justify-between items-center">
          <div>
            <h1 className="text-lg font-bold bg-clip-text text-transparent bg-gradient-to-r from-sky-400 via-cyan-400 to-teal-500">
              习惯英雄
            </h1>
            <div className="flex items-center space-x-3 mt-1">
              <div className="flex items-center space-x-1 text-yellow-400 text-xs">
                <SparklesIcon className="w-4 h-4"/>
                <span className="font-bold">{userData.points}</span>
              </div>
              <div className="flex items-center space-x-1 text-sky-400 text-xs">
                <span>✓</span>
                <span>{completedTasksTodayCount}</span>
              </div>
              <div className="flex items-center space-x-1 text-pink-400 text-xs">
                <span>📝</span>
                <span>{journalEntriesForSelectedDate.length}</span>
              </div>
            </div>
          </div>
          <div className="flex items-center space-x-2">
            <button
              onClick={handleExportData}
              className="bg-blue-600 hover:bg-blue-700 text-white p-2 rounded-lg transition-colors"
              title="导出数据"
            >
              <DownloadIcon className="w-4 h-4" />
            </button>
            <button
              onClick={handleImportData}
              className="bg-green-600 hover:bg-green-700 text-white p-2 rounded-lg transition-colors"
              title="导入数据"
            >
              <UploadIcon className="w-4 h-4" />
            </button>
            <Clock />
          </div>
        </div>
      </div>

      {viewMode === 'todo' && (
        <button
          onClick={() => setIsAddTaskModalOpen(true)}
          className="group fixed bottom-8 right-8 bg-gradient-to-r from-sky-500 via-blue-500 to-purple-500 text-white p-5 rounded-full shadow-2xl hover:from-sky-600 hover:via-blue-600 hover:to-purple-600 focus:outline-none focus:ring-4 focus:ring-sky-500/50 transform transition-all duration-500 hover:scale-125 active:scale-110 z-50 animate-pulse-glow overflow-hidden"
          aria-label="添加新任务"
        >
          {/* 背景动画光效 */}
          <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform rotate-45 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />

          {/* 按钮图标 */}
          <div className="relative z-10">
            <PlusIcon className="w-7 h-7 transition-all duration-300 group-hover:rotate-90 group-hover:scale-110" />
          </div>

          {/* 外圈装饰 */}
          <div className="absolute inset-0 rounded-full border-2 border-white/20 group-hover:border-white/40 transition-all duration-300" />

          {/* 脉冲效果 */}
          <div className="absolute inset-0 rounded-full bg-sky-400/30 animate-ping" />
        </button>
      )}

      <Modal 
        isOpen={isAddTaskModalOpen} 
        onClose={() => setIsAddTaskModalOpen(false)}
        title="添加新任务"
        size="lg"
      >
        <AddTaskForm onAddTask={handleAddTask} onClose={() => setIsAddTaskModalOpen(false)} />
      </Modal>

       <Modal
        isOpen={isAddJournalModalOpen}
        onClose={() => {
          setIsAddJournalModalOpen(false);
          setIsOpenedFromTray(false); // 重置托盘标记
        }}
        title="添加新日志条目"
        size="lg"
      >
        <AddJournalEntryForm
            onAddJournalEntry={handleAddJournalEntry}
            onClose={() => {
              setIsAddJournalModalOpen(false);
              setIsOpenedFromTray(false); // 重置托盘标记
            }}
            selectedDate={selectedDate}
        />
      </Modal>




      {/* Army Card Detail Modal - 响应式全屏 */}
      <Modal
        isOpen={armyCardDetailModal !== null}
        onClose={() => setArmyCardDetailModal(null)}
        title={armyCardDetailModal?.name || "军队卡片详情"}
        size="full"
      >
        {armyCardDetailModal && (
          <>
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8 h-full">
            {/* 左列：卡牌展示 */}
            <div className="lg:col-span-1 flex flex-col space-y-6">
              {/* 卡牌展示 */}
              <div className="bg-slate-700 p-6 rounded-lg h-fit">
                <div className="flex justify-center">
                  <div className="w-full max-w-80">
                    <ArmyCardView
                      card={armyCardDetailModal}
                      isRevealing={true}
                      isDetailsVisible={true}
                      onCardClick={() => {}}
                    />
                  </div>
                </div>
              </div>

              {/* 基本信息 */}
              <div className="bg-slate-700 p-6 rounded-lg h-fit">
                <h3 className="text-xl font-semibold text-white mb-6 text-center">基本信息</h3>
                <div className="space-y-4">
                  <div className="flex justify-between items-center py-2 border-b border-slate-600">
                    <span className="text-slate-300 text-lg">职业:</span>
                    <span className="text-white font-medium text-lg">{armyCardDetailModal.characterClass}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-slate-600">
                    <span className="text-slate-300 text-lg">稀有度:</span>
                    <span className="text-white font-medium text-lg">{armyCardDetailModal.rarity.level}</span>
                  </div>
                  <div className="flex justify-between items-center py-2 border-b border-slate-600">
                    <span className="text-slate-300 text-lg">获取概率:</span>
                    <span className="text-white font-medium text-lg">{(armyCardDetailModal.rarity.probability * 100).toFixed(0)}%</span>
                  </div>
                  <div className="flex justify-between items-center py-2">
                    <span className="text-slate-300 text-lg">技能数量:</span>
                    <span className="text-white font-medium text-lg">{armyCardDetailModal.skills.length}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* 中间列：技能信息 */}
            <div className="lg:col-span-1 flex flex-col space-y-6">
              <div className="bg-slate-700 p-6 rounded-lg h-full">
                <h3 className="text-xl font-semibold text-white mb-6 text-center">技能详情</h3>
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {armyCardDetailModal.skills.map((skill, index) => (
                    <div key={index} className="bg-slate-600 p-4 rounded-lg">
                      <h4 className="text-lg font-semibold text-white mb-2">{skill.name}</h4>
                      <p className="text-slate-300 text-sm leading-relaxed">{skill.description}</p>
                    </div>
                  ))}
                  {armyCardDetailModal.skills.length === 0 && (
                    <div className="text-center text-slate-400 py-8">
                      <div className="text-4xl mb-4">⚔️</div>
                      <p>该角色暂无技能信息</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* 右列：属性信息 */}
            <div className="lg:col-span-1 flex flex-col space-y-6">
              {armyCardDetailModal.attributes ? (
                <div className="bg-slate-700 p-6 rounded-lg h-full">
                  <h3 className="text-xl font-semibold text-white mb-6 text-center">角色属性</h3>

                  {/* 综合战力 */}
                  <div className="mb-8 text-center bg-slate-600 p-4 rounded-lg">
                    <div className="text-lg text-slate-300 mb-2">综合战力</div>
                    <div className="text-3xl font-bold text-yellow-400">
                      {Math.round(armyCardDetailModal.attributes.totalPower)}
                    </div>
                  </div>

                  {/* 基础属性 */}
                  <div className="mb-8">
                    <h4 className="text-lg font-semibold text-white mb-4">基础属性</h4>
                    <div className="grid grid-cols-1 gap-3">
                      {[
                        { key: 'attack', name: '攻击', icon: '⚔️', color: 'text-red-400' },
                        { key: 'defense', name: '防御', icon: '🛡️', color: 'text-blue-400' },
                        { key: 'health', name: '生命', icon: '❤️', color: 'text-green-400' },
                        { key: 'speed', name: '速度', icon: '⚡', color: 'text-yellow-400' },
                        { key: 'intelligence', name: '智力', icon: '🧠', color: 'text-purple-400' },
                        { key: 'leadership', name: '领导', icon: '👑', color: 'text-orange-400' }
                      ].map(attr => {
                        const value = armyCardDetailModal.attributes![attr.key as keyof typeof armyCardDetailModal.attributes] as number;
                        return (
                          <div key={attr.key} className="bg-slate-600 p-4 rounded-lg">
                            <div className="flex items-center gap-4 mb-2">
                              <span className="text-2xl">{attr.icon}</span>
                              <span className={`text-lg font-medium ${attr.color} flex-1`}>
                                {attr.name}
                              </span>
                              <span className="text-xl font-bold text-white">{value}/10</span>
                            </div>
                            {/* 进度条 */}
                            <div className="w-full bg-slate-700 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full transition-all duration-500 ${
                                  value >= 8 ? 'bg-gradient-to-r from-yellow-400 to-orange-500' :
                                  value >= 6 ? 'bg-gradient-to-r from-green-400 to-blue-500' :
                                  value >= 4 ? 'bg-gradient-to-r from-blue-400 to-purple-500' :
                                  'bg-gradient-to-r from-gray-400 to-gray-600'
                                }`}
                                style={{ width: `${(value / 10) * 100}%` }}
                              />
                            </div>
                          </div>
                        );
                      })}
                    </div>
                  </div>

                  {/* 特殊属性 */}
                  {(() => {
                    // 特殊属性英文到中文的映射
                    const specialAttributeNames: { [key: string]: string } = {
                      'corruption': '腐蚀度',
                      'magicResistance': '魔抗',
                      'healingPower': '治疗力',
                      'controlPower': '控制力',
                      'summonStrength': '召唤强度',
                      'armorPenetration': '护甲穿透',
                      'criticalRate': '暴击率',
                      'criticalDamage': '暴击伤害',
                      'lifeSteal': '生命偷取',
                      'magicPenetration': '魔法穿透',
                      'regeneration': '持续回复',
                      'accuracy': '精准度',
                      'dodgeRate': '闪避率',
                      'tenacity': '韧性',
                      'cooldownReduction': '技能冷却缩减',
                      'specialEffectTriggerRate': '特殊效果触发率',
                      'ammunitionReserve': '弹药储备',
                      'energyReserve': '能量储备',
                      'battleSpirit': '战意值',
                      'turretProficiency': '炮塔/投射物熟练度',
                      'shipDurability': '舰船耐久',
                      'airMobility': '空中机动性',
                      'lordCommand': '领主敕令',
                      'giantCaliber': '巨人口径',
                      'specialWeaponProficiency': '特殊武器熟练度',
                      'medicSkill': '医师技艺',
                      'bandRhythm': '军乐队节奏',
                      'fortressDefenseEfficiency': '堡垒防御效率',
                      'bigHandMark': '大手印记',
                      'digitalCalculation': '数字演算',
                      'likeEncouragement': '点赞激励',
                      'memeEffect': 'meme效果',
                      'mascotCharm': '吉祥物魅力',
                      'berserkerRage': '狂战士狂暴',
                      'suicideBomberExplosionDamage': '自爆兵爆炸伤害'
                    };

                    // 过滤出值大于0的特殊属性
                    const nonZeroSpecialAttributes = Object.entries(armyCardDetailModal.attributes.specialAttributes)
                      .filter(([key, value]) => value > 0);

                    return nonZeroSpecialAttributes.length > 0 ? (
                      <div>
                        <h4 className="text-lg font-semibold text-white mb-4">特殊属性</h4>
                        <div className="grid grid-cols-1 gap-3 max-h-64 overflow-y-auto">
                          {nonZeroSpecialAttributes.map(([key, value]) => (
                            <div key={key} className="bg-slate-600 p-4 rounded-lg">
                              <div className="flex items-center justify-between mb-2">
                                <span className="text-lg text-slate-300">
                                  {specialAttributeNames[key] || key}
                                </span>
                                <span className="text-xl font-bold text-cyan-400">{value}/10</span>
                              </div>
                              {/* 进度条 */}
                              <div className="w-full bg-slate-700 rounded-full h-2">
                                <div
                                  className={`h-2 rounded-full transition-all duration-500 ${
                                    value >= 8 ? 'bg-gradient-to-r from-cyan-400 to-blue-500' :
                                    value >= 6 ? 'bg-gradient-to-r from-purple-400 to-pink-500' :
                                    value >= 4 ? 'bg-gradient-to-r from-blue-400 to-cyan-500' :
                                    'bg-gradient-to-r from-gray-400 to-gray-600'
                                  }`}
                                  style={{ width: `${Math.min((value / 10) * 100, 100)}%` }}
                                />
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    ) : (
                      <div>
                        <h4 className="text-base font-semibold text-white mb-4">特殊属性</h4>
                        <div className="text-sm text-slate-400 text-center py-6 bg-slate-600 rounded">
                          暂无特殊属性
                        </div>
                      </div>
                    );
                  })()}
                </div>
              ) : (
                <div className="bg-slate-700 p-8 rounded-lg text-center">
                  <div className="text-5xl mb-4">⚡</div>
                  <div className="text-slate-300 mb-3 text-lg">该角色尚未生成属性</div>
                  <div className="text-slate-400">
                    前往属性管理页面为该角色生成属性
                  </div>
                </div>
              )}
            </div>
          </div>


          </>
        )}
      </Modal>


      {feedbackMessage && (
        <div
            className="fixed bottom-5 right-5 bg-green-600 text-white py-3 px-5 rounded-lg shadow-xl animate-slideInUp z-50"
            role="alert"
            aria-live="assertive"
        >
          {feedbackMessage}
        </div>
      )}

      {/* API配置管理器 */}
      <ApiConfigManager
        isOpen={isApiConfigOpen}
        onClose={() => setIsApiConfigOpen(false)}
      />
    </div>
  );
};

const App: React.FC = () => {
  return (
    <ThemeProvider>
      <AppContent />
    </ThemeProvider>
  );
};

export default App;






