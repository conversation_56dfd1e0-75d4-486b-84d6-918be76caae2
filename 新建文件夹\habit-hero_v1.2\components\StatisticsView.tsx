import React, { useMemo } from 'react';
import { Task, TaskType, JournalEntry, JournalType, ArmyCardData } from '../types';
import { getFormattedDate, isTaskCompletedOnDate } from '../utils/dateUtils';

interface StatisticsViewProps {
  tasks: Task[];
  journalEntries: JournalEntry[];
  armyCards: ArmyCardData[];
}

const StatisticsView: React.FC<StatisticsViewProps> = ({ tasks, journalEntries, armyCards }) => {
  // 计算统计数据
  const statistics = useMemo(() => {
    const now = new Date();
    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    
    // 每日任务数和完成数趋势（最近30天）
    const dailyStats = [];
    for (let i = 29; i >= 0; i--) {
      const date = new Date(now.getTime() - i * 24 * 60 * 60 * 1000);
      const dateStr = getFormattedDate(date);
      
      const tasksForDay = tasks.filter(task => {
        const createdAt = new Date(task.createdAt || 0);
        return createdAt <= date && (
          task.type === TaskType.DAILY ||
          (task.type === TaskType.WEEKLY && task.daysOfWeek?.includes(date.getDay())) ||
          (task.type === TaskType.ONCE && (!task.dueDate || new Date(task.dueDate) >= date))
        );
      });
      
      const completedTasks = tasksForDay.filter(task => isTaskCompletedOnDate(task, date));
      
      dailyStats.push({
        date: dateStr,
        totalTasks: tasksForDay.length,
        completedTasks: completedTasks.length,
        completionRate: tasksForDay.length > 0 ? (completedTasks.length / tasksForDay.length) * 100 : 0
      });
    }
    
    // 日志输入输出占比
    const journalStats = journalEntries.reduce((acc, entry) => {
      const type = entry.type || JournalType.OUTPUT;
      acc[type] = (acc[type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // 任务种类统计
    const taskTypeStats = tasks.reduce((acc, task) => {
      acc[task.type] = (acc[task.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // 任务分类统计
    const categoryStats = tasks.reduce((acc, task) => {
      const category = task.category || '未分类';
      acc[category] = (acc[category] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // 相同名字任务统计
    const taskNameStats = tasks.reduce((acc, task) => {
      acc[task.title] = (acc[task.title] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);
    
    // 重要任务统计
    const highlightedTasks = tasks.filter(task => task.isHighlighted).length;
    const totalTasks = tasks.length;

    // 军队风格统计
    const armyStyleStats = armyCards.reduce((acc, card) => {
      const faction = card.stylePrompt || '未分类';
      acc[faction] = (acc[faction] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      dailyStats,
      journalStats,
      taskTypeStats,
      categoryStats,
      taskNameStats,
      highlightedTasks,
      totalTasks,
      totalJournalEntries: journalEntries.length,
      armyStyleStats
    };
  }, [tasks, journalEntries, armyCards]);

  // 生成简单的柱状图
  const renderBarChart = (data: Record<string, number>, title: string, colors: string[]) => {
    const entries = Object.entries(data);
    const maxValue = Math.max(...entries.map(([, value]) => value));
    
    return (
      <div className="bg-slate-800 rounded-xl p-6 border border-slate-700">
        <h3 className="text-xl font-bold text-white mb-4">{title}</h3>
        <div className="space-y-3">
          {entries.map(([key, value], index) => (
            <div key={key} className="flex items-center space-x-3">
              <div className="w-20 text-sm text-slate-300 truncate">{key}</div>
              <div className="flex-1 bg-slate-700 rounded-full h-6 relative overflow-hidden">
                <div 
                  className={`h-full rounded-full transition-all duration-500 ${colors[index % colors.length]}`}
                  style={{ width: `${(value / maxValue) * 100}%` }}
                />
                <div className="absolute inset-0 flex items-center justify-center text-xs font-medium text-white">
                  {value}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  };

  // 生成真正的饼图
  const renderPieChart = (data: Record<string, number>, title: string, colors: string[]) => {
    const entries = Object.entries(data);
    const total = entries.reduce((sum, [, value]) => sum + value, 0);

    if (total === 0) {
      return (
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 className="text-xl font-bold text-white mb-4">{title}</h3>
          <div className="text-center text-slate-400 py-8">暂无数据</div>
        </div>
      );
    }

    // 计算每个扇形的角度
    let currentAngle = 0;
    const slices = entries.map(([key, value], index) => {
      const percentage = (value / total) * 100;
      const angle = (value / total) * 360;
      const startAngle = currentAngle;
      const endAngle = currentAngle + angle;
      currentAngle += angle;

      return {
        key,
        value,
        percentage,
        startAngle,
        endAngle,
        color: colors[index % colors.length]
      };
    });

    // 辅助函数：极坐标转笛卡尔坐标
    const polarToCartesian = (centerX: number, centerY: number, radius: number, angleInDegrees: number) => {
      const angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;
      return {
        x: centerX + (radius * Math.cos(angleInRadians)),
        y: centerY + (radius * Math.sin(angleInRadians))
      };
    };

    // 生成SVG路径
    const createPath = (centerX: number, centerY: number, radius: number, startAngle: number, endAngle: number) => {
      const start = polarToCartesian(centerX, centerY, radius, endAngle);
      const end = polarToCartesian(centerX, centerY, radius, startAngle);
      const largeArcFlag = endAngle - startAngle <= 180 ? "0" : "1";

      return [
        "M", centerX, centerY,
        "L", start.x, start.y,
        "A", radius, radius, 0, largeArcFlag, 0, end.x, end.y,
        "Z"
      ].join(" ");
    };

    const centerX = 120;
    const centerY = 120;
    const radius = 80;

    return (
      <div className="bg-slate-800 rounded-xl p-6 border border-slate-700">
        <h3 className="text-xl font-bold text-white mb-6">{title}</h3>
        <div className="flex flex-col items-center space-y-6">
          {/* 饼图 */}
          <div className="relative">
            <svg width="240" height="240" className="transform rotate-0">
              {slices.map((slice, index) => {
                const colorMap: Record<string, string> = {
                  'bg-blue-500': '#3B82F6',
                  'bg-green-500': '#10B981',
                  'bg-yellow-500': '#F59E0B',
                  'bg-purple-500': '#8B5CF6',
                  'bg-pink-500': '#EC4899',
                  'bg-indigo-500': '#6366F1',
                  'bg-red-500': '#EF4444',
                  'bg-orange-500': '#F97316',
                  'bg-teal-500': '#14B8A6',
                  'bg-cyan-500': '#06B6D4',
                  'bg-lime-500': '#84CC16',
                  'bg-emerald-500': '#10B981'
                };
                const fillColor = colorMap[slice.color] || '#6B7280';

                return (
                  <path
                    key={slice.key}
                    d={createPath(120, 120, 90, slice.startAngle, slice.endAngle)}
                    fill={fillColor}
                    stroke="#1E293B"
                    strokeWidth="2"
                    className="hover:opacity-80 transition-all duration-200"
                  />
                );
              })}
            </svg>
          </div>

          {/* 总数显示 */}
          <div className="text-center">
            <div className="text-2xl font-bold text-white">{total}</div>
            <div className="text-slate-400 text-sm">
              {title.includes('军队') ? '总人数' :
               title.includes('任务') ? '总任务数' :
               title.includes('日志') ? '总日志数' : '总数'}
            </div>
          </div>

          {/* 图例 */}
          <div className="w-full">
            <div className="flex flex-wrap gap-2 justify-center">
              {slices.map((slice) => (
                <div key={slice.key} className="flex items-center space-x-2 bg-slate-700/50 px-3 py-2 rounded-lg">
                  <div className={`w-3 h-3 rounded-full flex-shrink-0 ${slice.color}`} />
                  <span className="text-slate-300 text-sm">{slice.key} ({slice.value})</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };



  // 生成折线图（修复定位问题）
  const renderLineChart = () => {
    const data = statistics.dailyStats.slice(-7); // 最近7天

    if (data.length === 0) {
      return (
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700">
          <h3 className="text-xl font-bold text-white mb-4">最近7天任务趋势</h3>
          <div className="text-center text-slate-400 py-8">暂无数据</div>
        </div>
      );
    }

    const maxTasks = Math.max(...data.map(d => d.totalTasks), 1); // 至少为1，避免除零
    const maxCompleted = Math.max(...data.map(d => d.completedTasks), 1);
    const maxValue = Math.max(maxTasks, maxCompleted, 5); // 至少为5，确保有合理的比例

    // 图表区域设置 - 使用更大的尺寸占满容器
    const chartWidth = 800;
    const chartHeight = 400;
    const padding = { top: 30, right: 50, bottom: 80, left: 80 };
    const innerWidth = chartWidth - padding.left - padding.right;
    const innerHeight = chartHeight - padding.top - padding.bottom;

    // 计算点的位置
    const getX = (index: number) => padding.left + (index / Math.max(data.length - 1, 1)) * innerWidth;
    const getY = (value: number) => padding.top + (1 - value / maxValue) * innerHeight;

    return (
      <div className="bg-slate-800 rounded-xl p-6 border border-slate-700">
        <h3 className="text-xl font-bold text-white mb-6">最近7天任务趋势</h3>
        <div className="w-full flex justify-center">
          <div className="w-full max-w-5xl">
            <svg
              width="100%"
              height={chartHeight}
              viewBox={`0 0 ${chartWidth} ${chartHeight}`}
              className="w-full h-auto"
              preserveAspectRatio="xMidYMid meet"
            >
              {/* 背景网格线 */}
              {[0, 1, 2, 3, 4].map(i => {
                const y = padding.top + (i / 4) * innerHeight;
                return (
                  <line
                    key={`grid-${i}`}
                    x1={padding.left}
                    y1={y}
                    x2={chartWidth - padding.right}
                    y2={y}
                    stroke="#374151"
                    strokeWidth="1"
                    strokeDasharray="2,2"
                  />
                );
              })}

              {/* Y轴标签 */}
              {[0, 1, 2, 3, 4].map(i => {
                const value = Math.round((maxValue * (4 - i)) / 4);
                const y = padding.top + (i / 4) * innerHeight;
                return (
                  <text
                    key={`y-label-${i}`}
                    x={padding.left - 15}
                    y={y + 5}
                    fill="#9CA3AF"
                    fontSize="14"
                    textAnchor="end"
                  >
                    {value}
                  </text>
                );
              })}

              {/* X轴标签 */}
              {data.map((d, i) => {
                const x = getX(i);
                return (
                  <text
                    key={`x-label-${i}`}
                    x={x}
                    y={chartHeight - padding.bottom + 25}
                    fill="#9CA3AF"
                    fontSize="12"
                    textAnchor="middle"
                  >
                    {d.date.slice(-5)} {/* 显示MM-DD */}
                  </text>
                );
              })}

              {/* 总任务数线 */}
              {data.length > 1 && (
                <polyline
                  fill="none"
                  stroke="#0EA5E9"
                  strokeWidth="4"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  points={data.map((d, i) => `${getX(i)},${getY(d.totalTasks)}`).join(' ')}
                />
              )}

              {/* 完成任务数线 */}
              {data.length > 1 && (
                <polyline
                  fill="none"
                  stroke="#10B981"
                  strokeWidth="4"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  points={data.map((d, i) => `${getX(i)},${getY(d.completedTasks)}`).join(' ')}
                />
              )}

              {/* 数据点 */}
              {data.map((d, i) => (
                <g key={`points-${i}`}>
                  <circle
                    cx={getX(i)}
                    cy={getY(d.totalTasks)}
                    r="6"
                    fill="#0EA5E9"
                    stroke="#1E293B"
                    strokeWidth="3"
                  />
                  <circle
                    cx={getX(i)}
                    cy={getY(d.completedTasks)}
                    r="6"
                    fill="#10B981"
                    stroke="#1E293B"
                    strokeWidth="3"
                  />
                </g>
              ))}
            </svg>

            {/* 图例 */}
            <div className="mt-6 flex justify-center space-x-8 text-sm">
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-sky-500 rounded-full" />
                <span className="text-slate-300">总任务数</span>
              </div>
              <div className="flex items-center space-x-3">
                <div className="w-4 h-4 bg-green-500 rounded-full" />
                <span className="text-slate-300">完成任务数</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  };

  const barColors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', 'bg-pink-500', 'bg-indigo-500'];
  const pieColors = ['bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', 'bg-pink-500', 'bg-indigo-500'];

  return (
    <div className="space-y-8">
      <div className="text-center">
        <h1 className="text-3xl font-bold text-white mb-2">📊 数据统计</h1>
        <p className="text-slate-400">深入了解你的习惯养成数据</p>
      </div>

      {/* 概览卡片 */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700 text-center">
          <div className="text-3xl font-bold text-blue-400">{statistics.totalTasks}</div>
          <div className="text-slate-400 mt-1">总任务数</div>
        </div>
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700 text-center">
          <div className="text-3xl font-bold text-green-400">{statistics.highlightedTasks}</div>
          <div className="text-slate-400 mt-1">重要任务</div>
        </div>
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700 text-center">
          <div className="text-3xl font-bold text-purple-400">{statistics.totalJournalEntries}</div>
          <div className="text-slate-400 mt-1">日志条目</div>
        </div>
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700 text-center">
          <div className="text-3xl font-bold text-amber-400">{statistics.totalArmyUnits}</div>
          <div className="text-slate-400 mt-1">军队人数</div>
        </div>
        <div className="bg-slate-800 rounded-xl p-6 border border-slate-700 text-center">
          <div className="text-3xl font-bold text-yellow-400">
            {statistics.dailyStats.length > 0
              ? (statistics.dailyStats.reduce((sum, d) => sum + d.completionRate, 0) / statistics.dailyStats.length).toFixed(1)
              : '0'
            }%
          </div>
          <div className="text-slate-400 mt-1">平均完成率</div>
        </div>
      </div>

      {/* 图表区域 */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 折线图 */}
        <div className="lg:col-span-2">
          {renderLineChart()}
        </div>
        
        {/* 任务类型统计 */}
        {renderPieChart(statistics.taskTypeStats, '任务类型分布', pieColors)}
        
        {/* 日志类型统计 */}
        {Object.keys(statistics.journalStats).length > 0 &&
          renderPieChart(statistics.journalStats, '日志类型分布', pieColors)
        }

        {/* 军队风格统计 */}
        {Object.keys(statistics.armyStyleStats).length > 0 &&
          renderPieChart(statistics.armyStyleStats, '军队风格分布', pieColors)
        }

        {/* 任务分类统计 */}
        {Object.keys(statistics.categoryStats).length > 1 && 
          renderBarChart(statistics.categoryStats, '任务分类统计', barColors)
        }
        
        {/* 重复任务统计 */}
        {Object.entries(statistics.taskNameStats).filter(([, count]) => count > 1).length > 0 && 
          renderBarChart(
            Object.fromEntries(Object.entries(statistics.taskNameStats).filter(([, count]) => count > 1)),
            '重复任务统计',
            barColors
          )
        }
      </div>
    </div>
  );
};

export default StatisticsView;
