
import { Rarity, Card, CollectedCardEntry } from '../types';
import { CARD_POOL, RARITY_PROBABILITIES, MAX_UNIQUE_CARDS_PER_RARITY } from '../constants';

export const drawCard = (
  collectedCards: Record<string, CollectedCardEntry>
): Card | null => {
  // 1. Determine Rarity
  let random = Math.random();
  let selectedRarity: Rarity | null = null;
  let cumulativeProbability = 0;

  for (const rarity of Object.keys(RARITY_PROBABILITIES) as Rarity[]) {
    cumulativeProbability += RARITY_PROBABILITIES[rarity];
    if (random <= cumulativeProbability) {
      selectedRarity = rarity;
      break;
    }
  }

  if (!selectedRarity) {
    // Should not happen if probabilities sum to 1
    selectedRarity = Rarity.COMMON; 
  }

  // 2. Get cards of the selected rarity from the pool
  const cardsOfSelectedRarity = CARD_POOL.filter(card => card.rarity === selectedRarity);

  // 3. Filter out already collected cards of this rarity
  const collectedCardIdsOfSelectedRarity = Object.values(collectedCards)
    .map(entry => CARD_POOL.find(c => c.id === entry.cardId))
    .filter(card => card && card.rarity === selectedRarity)
    .map(card => card!.id);
  
  const uncollectedCardsOfSelectedRarity = cardsOfSelectedRarity.filter(
    card => !collectedCardIdsOfSelectedRarity.includes(card.id)
  );

  // 4. Check if this rarity category is full for unique cards
  const maxUniqueForRarity = MAX_UNIQUE_CARDS_PER_RARITY[selectedRarity];
  if (collectedCardIdsOfSelectedRarity.length >= maxUniqueForRarity || uncollectedCardsOfSelectedRarity.length === 0) {
    // All unique cards of this rarity are collected, or no more available in pool for this rarity
    // Potentially give a small point refund or a message, handled in App.tsx
    return null; 
  }
  
  // 5. Pick a random card from the uncollected ones
  const randomIndex = Math.floor(Math.random() * uncollectedCardsOfSelectedRarity.length);
  return uncollectedCardsOfSelectedRarity[randomIndex];
};
