
import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';
import { v4 as uuidv4 } from 'uuid'; // Ensure uuid is available globally if needed, or imported where used.

// Example: If you need to make uuid available more broadly, this isn't standard.
// (window as any).uuidv4 = uuidv4; 
// It's better to import it directly in files that need it, which is already done in AddTaskForm.tsx.

const rootElement = document.getElementById('root');
if (!rootElement) {
  throw new Error("Could not find root element to mount to");
}

const root = ReactDOM.createRoot(rootElement);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);
