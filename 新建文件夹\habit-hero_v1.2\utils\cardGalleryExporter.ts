import { ArmyCardData } from '../types';

// 职业图标映射 - 基于实际的CHARACTER_CLASSES
const classIcons: Record<string, string> = {
  '战士': '⚔️',
  '弓箭手': '🏹',
  '攻城器械': '🏰',
  '指挥官': '👑',
  '盾兵': '🛡️',
  '暗法师': '🌙',
  '召唤师': '🌟',
  '工程师': '⚙️',
  '枪兵': '🔱',
  '巨人': '🗿',
  '骑兵': '🐎',
  '领主': '👑',
  '国王': '👑',
  '火枪手': '🔫',
  '特殊武器': '💥',
  '堡垒': '🏯',
  '军乐队': '🎺',
  '近卫队': '🛡️',
  '空中单位': '✈️',
  '防空单位': '🚀',
  '舰船': '⚓',
  '法师': '🔮',
  '医师': '💊'
};

// 获取职业图标
function getClassIcon(characterClass: string): string {
  return classIcons[characterClass] || '🛡️'; // 默认图标
}

export function generateCardGalleryHTML(cards: ArmyCardData[], campName?: string): string {
  const title = campName ? `${campName} - 军队卡牌收藏` : 'Habit Hero - 军队卡牌收藏';
  const totalCards = cards.length;
  
  // 按稀有度分组统计
  const rarityStats = cards.reduce((acc, card) => {
    const rarity = card.rarity.level;
    acc[rarity] = (acc[rarity] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // 按阵营分组统计
  const factionStats = cards.reduce((acc, card) => {
    const faction = card.stylePrompt || '未知阵营';
    acc[faction] = (acc[faction] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // 生成卡牌HTML
  const cardsHTML = cards.map((card, index) => `
    <div class="card" data-rarity="${card.rarity.level}" data-faction="${card.stylePrompt || '未知阵营'}" data-class="${card.characterClass}">
      <div class="card-inner">
        <div class="card-front">
          <div class="card-header" style="background: linear-gradient(135deg, ${card.rarity.glowColor}40, ${card.rarity.glowColor}20);">
            <div class="card-rarity ${card.rarity.level}" style="color: ${card.rarity.color};">
              ${card.rarity.level}
            </div>
            <div class="card-faction">
              ⚔️ ${card.stylePrompt || '未知阵营'}
            </div>
          </div>
          <div class="card-image">
            <img src="${card.imageUrl}" alt="${card.name}" loading="lazy" />
          </div>
          <div class="card-info">
            <h3 class="card-name">${card.name}</h3>
            <p class="card-class">${getClassIcon(card.characterClass)} ${card.characterClass}</p>
          </div>
          <div class="card-flip-hint">点击查看详情</div>
        </div>
        <div class="card-back">
          <div class="card-back-header">
            <h3>${card.name}</h3>
            <div class="card-back-rarity" style="color: ${card.rarity.color};">
              ${card.rarity.level}
            </div>
          </div>
          <div class="card-back-content">
            <div class="card-class-info">
              <h4>${getClassIcon(card.characterClass)} 职业</h4>
              <p>${card.characterClass}</p>
            </div>
            <div class="card-description">
              <h4>📜 描述</h4>
              <p>${card.description}</p>
            </div>
            ${card.skills && card.skills.length > 0 ? `
            <div class="card-skills">
              <h4>⚡ 技能</h4>
              <ul>
                ${card.skills.map(skill => `<li><strong>${skill.name}:</strong> ${skill.description}</li>`).join('')}
              </ul>
            </div>
            ` : ''}
            ${card.story ? `
            <div class="card-story">
              <h4>📖 背景故事</h4>
              <p>${card.story}</p>
            </div>
            ` : ''}
          </div>
          <div class="card-flip-hint">点击返回</div>
        </div>
      </div>
    </div>
  `).join('');

  return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${title}</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
            color: #ffffff;
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
            padding: 30px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 20px;
            backdrop-filter: blur(10px);
        }

        .header h1 {
            font-size: 3rem;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2rem;
            opacity: 0.8;
            margin-bottom: 20px;
        }

        .stats {
            display: flex;
            justify-content: center;
            gap: 30px;
            flex-wrap: wrap;
            margin-top: 20px;
        }

        .stat-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 15px 25px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(5px);
        }

        .stat-number {
            font-size: 2rem;
            font-weight: bold;
            color: #4ecdc4;
        }

        .stat-label {
            font-size: 0.9rem;
            opacity: 0.8;
            margin-top: 5px;
        }

        .filters {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .filter-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
        }

        .filter-btn:hover, .filter-btn.active {
            background: rgba(78, 205, 196, 0.3);
            transform: translateY(-2px);
        }

        .sub-filters {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-bottom: 20px;
            flex-wrap: wrap;
            min-height: 50px;
            align-items: center;
        }

        .sub-filter-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.05);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            font-size: 0.9rem;
        }

        .sub-filter-btn:hover, .sub-filter-btn.active {
            background: rgba(69, 183, 209, 0.3);
            transform: translateY(-1px);
            border-color: rgba(69, 183, 209, 0.5);
        }

        .cards-grid {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 30px;
            padding: 20px 0;
        }

        .card {
            perspective: 1000px;
            height: 450px;
        }

        .card-inner {
            position: relative;
            width: 100%;
            height: 100%;
            text-align: center;
            transition: transform 0.8s;
            transform-style: preserve-3d;
            cursor: pointer;
        }

        .card.flipped .card-inner {
            transform: rotateY(180deg);
        }

        .card-front, .card-back {
            position: absolute;
            width: 100%;
            height: 100%;
            backface-visibility: hidden;
            border-radius: 20px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card-back {
            transform: rotateY(180deg);
            padding: 20px;
            display: flex;
            flex-direction: column;
        }

        .card-header {
            padding: 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .card-rarity {
            font-weight: bold;
            font-size: 1.1rem;
            text-shadow: 0 0 10px currentColor;
        }

        .card-faction {
            font-size: 0.9rem;
            opacity: 0.9;
        }

        .card-image {
            height: 250px;
            overflow: hidden;
            position: relative;
        }

        .card-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transition: transform 0.3s ease;
        }

        .card:hover .card-image img {
            transform: scale(1.05);
        }

        .card-info {
            padding: 20px;
            flex-grow: 1;
        }

        .card-name {
            font-size: 1.3rem;
            margin-bottom: 8px;
            color: #ffffff;
        }

        .card-class {
            color: #4ecdc4;
            font-size: 1rem;
        }

        .card-flip-hint {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            font-size: 0.8rem;
            opacity: 0.6;
            transition: opacity 0.3s ease;
        }

        .card:hover .card-flip-hint {
            opacity: 1;
        }

        .card-back-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        .card-back-content {
            flex-grow: 1;
            overflow-y: auto;
            text-align: left;
        }

        .card-back-content h4 {
            color: #4ecdc4;
            margin-bottom: 10px;
            font-size: 1.1rem;
        }

        .card-back-content p {
            line-height: 1.6;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .card-class-info {
            margin-bottom: 15px;
            padding: 10px;
            background: rgba(78, 205, 196, 0.1);
            border-radius: 8px;
            border-left: 3px solid #4ecdc4;
        }

        .card-class-info h4 {
            margin-bottom: 5px;
            font-size: 1rem;
        }

        .card-class-info p {
            margin-bottom: 0;
            font-weight: 500;
            color: #ffffff;
        }

        .card-skills ul {
            list-style: none;
            padding-left: 0;
        }

        .card-skills li {
            margin-bottom: 8px;
            padding: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            font-size: 0.9rem;
        }

        .hidden {
            display: none !important;
        }

        @media (max-width: 768px) {
            .cards-grid {
                grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
                gap: 20px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats {
                gap: 15px;
            }
            
            .filters {
                gap: 10px;
            }
            
            .filter-btn {
                padding: 8px 16px;
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>${title}</h1>
            <p>共收集 ${totalCards} 张卡牌</p>
            <div class="stats">
                <div class="stat-item">
                    <div class="stat-number">${totalCards}</div>
                    <div class="stat-label">总卡牌</div>
                </div>
                ${Object.entries(rarityStats).map(([rarity, count]) => `
                <div class="stat-item">
                    <div class="stat-number">${count}</div>
                    <div class="stat-label">${rarity}</div>
                </div>
                `).join('')}
            </div>
        </div>

        <div class="filters">
            <button class="filter-btn active" onclick="filterCards('all')">全部</button>
            <button class="filter-btn" onclick="filterCards('rarity')">按稀有度</button>
            <button class="filter-btn" onclick="filterCards('faction')">按阵营</button>
            <button class="filter-btn" onclick="filterCards('class')">按职业</button>
        </div>

        <div class="sub-filters" id="subFilters">
            <!-- 子筛选按钮将在这里动态生成 -->
        </div>

        <div class="cards-grid" id="cardsGrid">
            ${cardsHTML}
        </div>
    </div>

    <script>
        // 卡牌翻转功能
        document.querySelectorAll('.card').forEach(card => {
            card.addEventListener('click', () => {
                card.classList.toggle('flipped');
            });
        });

        // 筛选功能
        function filterCards(type) {
            const cards = document.querySelectorAll('.card');
            const buttons = document.querySelectorAll('.filter-btn');
            const subFiltersDiv = document.getElementById('subFilters');

            // 更新按钮状态
            buttons.forEach(btn => btn.classList.remove('active'));
            event.target.classList.add('active');

            // 清空子筛选按钮
            subFiltersDiv.innerHTML = '';

            if (type === 'all') {
                cards.forEach(card => card.classList.remove('hidden'));
                return;
            }

            // 获取所有可能的值
            const values = new Set();
            cards.forEach(card => {
                let value;
                switch(type) {
                    case 'rarity':
                        value = card.dataset.rarity;
                        break;
                    case 'faction':
                        value = card.dataset.faction;
                        break;
                    case 'class':
                        value = card.dataset.class;
                        break;
                }
                if (value) values.add(value);
            });

            // 创建子筛选按钮
            Array.from(values).sort().forEach(value => {
                const btn = document.createElement('button');
                btn.className = 'sub-filter-btn';
                btn.textContent = value;
                btn.onclick = () => filterByValue(type, value, btn);
                subFiltersDiv.appendChild(btn);
            });
        }
        
        function filterByValue(type, value, clickedBtn) {
            const cards = document.querySelectorAll('.card');
            const subFilterBtns = document.querySelectorAll('.sub-filter-btn');

            // 更新子按钮状态
            subFilterBtns.forEach(btn => btn.classList.remove('active'));
            clickedBtn.classList.add('active');

            cards.forEach(card => {
                let cardValue;
                switch(type) {
                    case 'rarity':
                        cardValue = card.dataset.rarity;
                        break;
                    case 'faction':
                        cardValue = card.dataset.faction;
                        break;
                    case 'class':
                        cardValue = card.dataset.class;
                        break;
                }

                if (cardValue === value) {
                    card.classList.remove('hidden');
                } else {
                    card.classList.add('hidden');
                }
            });
        }

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') {
                // ESC键关闭所有翻转的卡牌
                document.querySelectorAll('.card.flipped').forEach(card => {
                    card.classList.remove('flipped');
                });
            }
        });

        // 添加加载动画
        window.addEventListener('load', () => {
            const cards = document.querySelectorAll('.card');
            cards.forEach((card, index) => {
                setTimeout(() => {
                    card.style.opacity = '0';
                    card.style.transform = 'translateY(50px)';
                    card.style.transition = 'all 0.6s ease';
                    
                    setTimeout(() => {
                        card.style.opacity = '1';
                        card.style.transform = 'translateY(0)';
                    }, 100);
                }, index * 50);
            });
        });
    </script>
</body>
</html>`;
}
