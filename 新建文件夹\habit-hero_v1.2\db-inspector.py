import sqlite3
import os

def get_database_path():
    """获取数据库路径"""
    print('📁 数据库路径选择:')
    print('1. 当前桌面路径: C:\\Users\\<USER>\\Desktop\\habit-hero.db')
    print('2. 默认应用数据路径: %APPDATA%\\habit-hero\\habit-hero.db')
    print('3. 自定义路径')
    print()

    while True:
        choice = input('请选择 (1/2/3): ').strip()

        if choice == '1':
            return r'C:\Users\<USER>\Desktop\habit-hero.db'
        elif choice == '2':
            appdata = os.environ.get('APPDATA', '')
            return os.path.join(appdata, 'habit-hero', 'habit-hero.db')
        elif choice == '3':
            custom_path = input('请输入完整的数据库文件路径: ').strip()
            if custom_path:
                return custom_path
            else:
                print('❌ 路径不能为空，请重新选择')
        else:
            print('❌ 无效选择，请输入 1、2 或 3')

def inspect_database(db_path=None):
    if db_path is None:
        db_path = get_database_path()
    
    print('=' * 60)
    print('Habit Hero 数据库检查工具')
    print('=' * 60)
    print(f'数据库路径: {db_path}')
    
    if not os.path.exists(db_path):
        print('❌ 数据库文件不存在！')
        return
    
    print('✅ 数据库文件存在')
    file_size = os.path.getsize(db_path) / 1024
    print(f'文件大小: {file_size:.2f} KB')
    print()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查所有表
        print('📋 数据库表列表:')
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        for table in tables:
            print(f'  - {table[0]}')
        print()

        # 检查 army_cards 表结构
        print('🔍 army_cards 表结构:')
        cursor.execute("PRAGMA table_info(army_cards)")
        columns = cursor.fetchall()
        for col in columns:
            cid, name, col_type, notnull, default, pk = col
            nullable = 'NOT NULL' if notnull else 'NULL'
            default_str = f'DEFAULT {default}' if default else ''
            print(f'  {cid}: {name} ({col_type}) {nullable} {default_str}')
        print()

        # 检查是否有 style_prompt 列
        column_names = [col[1] for col in columns]
        has_style_prompt = 'style_prompt' in column_names
        if has_style_prompt:
            print('✅ style_prompt 列存在')
        else:
            print('❌ style_prompt 列不存在！')
        print()

        # 检查数据库版本
        print('📊 数据库版本信息:')
        try:
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='db_version'")
            version_table = cursor.fetchone()
            if version_table:
                cursor.execute("SELECT * FROM db_version ORDER BY version")
                versions = cursor.fetchall()
                if versions:
                    for version, applied_at in versions:
                        print(f'  版本 {version}: {applied_at}')
                else:
                    print('  版本表存在但为空')
            else:
                print('  版本表不存在')
        except Exception as e:
            print(f'  无法读取版本信息: {e}')
        print()

        # 统计数据
        print('📈 数据统计:')
        try:
            cursor.execute("SELECT COUNT(*) FROM army_cards")
            army_count = cursor.fetchone()[0]
            print(f'  军队卡片: {army_count} 张')
            
            if has_style_prompt:
                cursor.execute("SELECT COUNT(*) FROM army_cards WHERE style_prompt IS NOT NULL AND style_prompt != ''")
                with_style = cursor.fetchone()[0]
                cursor.execute("SELECT COUNT(*) FROM army_cards WHERE style_prompt IS NULL OR style_prompt = ''")
                without_style = cursor.fetchone()[0]
                print(f'  有阵营标签: {with_style} 张')
                print(f'  无阵营标签: {without_style} 张')
        except Exception as e:
            print(f'  无法读取统计信息: {e}')
        print()

        # 显示一些示例数据
        if has_style_prompt:
            print('📝 阵营标签示例:')
            try:
                cursor.execute("SELECT name, style_prompt FROM army_cards WHERE style_prompt IS NOT NULL AND style_prompt != '' LIMIT 5")
                samples = cursor.fetchall()
                if samples:
                    for name, style_prompt in samples:
                        print(f'  {name}: "{style_prompt}"')
                else:
                    print('  没有找到有阵营标签的卡片')
            except Exception as e:
                print(f'  无法读取示例数据: {e}')
        print()

        # 显示一些没有阵营标签的卡片
        if has_style_prompt:
            print('❓ 缺少阵营标签的卡片示例:')
            try:
                cursor.execute("SELECT name FROM army_cards WHERE style_prompt IS NULL OR style_prompt = '' LIMIT 5")
                samples = cursor.fetchall()
                if samples:
                    for name, in samples:
                        print(f'  {name}')
                else:
                    print('  所有卡片都有阵营标签')
            except Exception as e:
                print(f'  无法读取示例数据: {e}')

        conn.close()
        print()
        print('✅ 数据库检查完成')
        
    except Exception as e:
        print(f'❌ 数据库检查失败: {e}')

if __name__ == '__main__':
    inspect_database()
