
import { Task, TaskType, JournalEntry } from '../types';

export const getFormattedDate = (date: Date): string => {
  const year = date.getFullYear();
  const month = (date.getMonth() + 1).toString().padStart(2, '0'); // Months are 0-indexed
  const day = date.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`; // YYYY-MM-DD for local date
};

export const getWeekNumber = (d: Date): number => {
  // Copy date so don't modify original
  d = new Date(Date.UTC(d.getFullYear(), d.getMonth(), d.getDate()));
  // Set to nearest Thursday: current date + 4 - current day number
  // Make Sunday's day number 7
  d.setUTCDate(d.getUTCDate() + 4 - (d.getUTCDay() || 7));
  // Get first day of year
  const yearStart = new Date(Date.UTC(d.getUTCFullYear(), 0, 1));
  // Calculate full weeks to nearest Thursday
  const weekNo = Math.ceil((((d.valueOf() - yearStart.valueOf()) / 86400000) + 1) / 7);
  return weekNo;
};

export const getStartOfWeek = (date: Date, startDay: number = 0 /* 0 for Sunday, 1 for Monday */): Date => {
  const d = new Date(date);
  const day = d.getDay();
  const diff = (day < startDay ? day + 7 : day) - startDay;
  d.setDate(d.getDate() - diff);
  d.setHours(0, 0, 0, 0);
  return d;
};


export const isTaskCompletedOnDate = (task: Task, date: Date): boolean => {
  const targetDateStr = getFormattedDate(date); // YYYY-MM-DD of the date we are checking against
  const taskCreationDate = new Date(task.createdAt);
  taskCreationDate.setHours(0,0,0,0);

  const normalizedDate = new Date(date);
  normalizedDate.setHours(0,0,0,0);

  if (normalizedDate < taskCreationDate) {
    return false;
  }

  if (task.type === TaskType.ONCE) {
    // 一次性任务：只有在实际完成日期匹配时才显示为已完成
    if (task.isCompleted && task.actualCompletionDate) {
      const completionDateStr = getFormattedDate(new Date(task.actualCompletionDate));
      return completionDateStr === targetDateStr;
    }
    return false;
  }
  if (task.type === TaskType.DAILY) {
    // 每日任务：检查是否在指定日期完成
    return task.completedDates?.some(isoStr => getFormattedDate(new Date(isoStr)) === targetDateStr) || false;
  }
  if (task.type === TaskType.WEEKLY) {
    // 每周任务：检查是否在指定日期完成
    return task.completedDates?.some(isoStr => getFormattedDate(new Date(isoStr)) === targetDateStr) || false;
  }
  return false;
};

export const getTasksForDate = (tasks: Task[], date: Date): Task[] => {
  const normalizedSelectedDate = new Date(date);
  normalizedSelectedDate.setHours(0,0,0,0); 

  const selectedDateStr = getFormattedDate(normalizedSelectedDate); 

  return tasks.filter(task => {
    const taskCreationDate = new Date(task.createdAt);
    taskCreationDate.setHours(0,0,0,0);

    if (normalizedSelectedDate < taskCreationDate) {
      return false; 
    }

    if (task.type === TaskType.ONCE) {
      // 一次性任务逻辑：
      // 1. 如果已完成，只在完成日期显示
      // 2. 如果未完成，在创建日期到截止日期之间显示
      if (task.isCompleted && task.actualCompletionDate) {
        const completionDateStr = getFormattedDate(new Date(task.actualCompletionDate));
        return completionDateStr === selectedDateStr;
      }

      // 未完成的一次性任务
      if (!task.isCompleted) {
        const dueDate = task.dueDate ? new Date(task.dueDate) : null;
        if (dueDate) {
          dueDate.setHours(0,0,0,0);
          // 在创建日期到截止日期之间显示
          return normalizedSelectedDate >= taskCreationDate && normalizedSelectedDate <= dueDate;
        } else {
          // 没有截止日期的一次性任务，从创建日期开始一直显示直到完成
          return normalizedSelectedDate >= taskCreationDate;
        }
      }

      return false;
    }
    if (task.type === TaskType.DAILY) {
      // 每日任务只在创建日期当天及之后显示
      return normalizedSelectedDate >= taskCreationDate;
    }
    if (task.type === TaskType.WEEKLY) {
      // 每周任务：从创建日期开始，只在指定的星期几显示
      if (normalizedSelectedDate < taskCreationDate) {
        return false;
      }

      // 检查是否是指定的星期几
      const dayOfWeek = normalizedSelectedDate.getDay();
      const isScheduledDay = task.daysOfWeek?.includes(dayOfWeek) || false;

      if (!isScheduledDay) {
        return false;
      }

      // 检查本周是否已经完成了足够的次数
      const weekStart = getStartOfWeek(normalizedSelectedDate, 1); // 周一开始
      const weekEnd = new Date(weekStart);
      weekEnd.setDate(weekEnd.getDate() + 6); // 周日结束

      const completionsThisWeek = task.completedDates?.filter(isoStr => {
        const completionDate = new Date(isoStr);
        completionDate.setHours(0,0,0,0);
        return completionDate >= weekStart && completionDate <= weekEnd;
      }).length || 0;

      const timesPerWeek = task.timesPerWeek || 1;

      // 如果本周已完成次数达标，只在已完成的日期显示
      if (completionsThisWeek >= timesPerWeek) {
        return task.completedDates?.some(isoStr =>
          getFormattedDate(new Date(isoStr)) === selectedDateStr
        ) || false;
      }

      // 否则在所有计划的日期显示
      return true;
    }
    return false;
  });
};

export const getCompletionsInCurrentWeek = (task: Task, date: Date): number => {
    if (task.type !== TaskType.WEEKLY) return 0;
    
    const currentWeekNumber = getWeekNumber(date);
    const currentYear = date.getFullYear();

    // completedDates are now ISO strings
    return task.completedDates?.filter(isoStr => {
        const completedDate = new Date(isoStr); 
        return getWeekNumber(completedDate) === currentWeekNumber && completedDate.getFullYear() === currentYear;
    }).length || 0;
};

export const getJournalEntriesForDate = (journalEntries: JournalEntry[], date: Date): JournalEntry[] => {
  const targetDateStr = getFormattedDate(date);
  return journalEntries.filter(entry => entry.date === targetDateStr)
                      .sort((a,b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime());
};
