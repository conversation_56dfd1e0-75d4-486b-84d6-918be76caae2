import React, { useState } from 'react';
import { Card, Rarity, CollectedCardEntry } from '../types';
import { CARD_POOL } from '../constants';
import CardItem from './CardItem';
import FilterControls from './FilterControls';
import CollectionProgress from './CollectionProgress';

interface CardGalleryProps {
  collectedCardsMap: Record<string, CollectedCardEntry>;
  onCardClick: (card: Card) => void; // Added prop to handle card clicks
}

const CardGallery: React.FC<CardGalleryProps> = ({ collectedCardsMap, onCardClick }) => {
  const [selectedRarity, setSelectedRarity] = useState<Rarity | 'All'>('All');

  const sortedCardPool = [...CARD_POOL].sort((a, b) => {
    const rarityOrder = Object.values(Rarity);
    if (a.rarity !== b.rarity) return rarityOrder.indexOf(a.rarity) - rarityOrder.indexOf(b.rarity);
    return a.id.localeCompare(b.id);
  });

  const filteredCardsFromPool = selectedRarity === 'All'
    ? sortedCardPool
    : sortedCardPool.filter(card => card.rarity === selectedRarity);

  return (
    <div className="w-full">
      <CollectionProgress collectedCards={collectedCardsMap} />
      <FilterControls selectedRarity={selectedRarity} onRarityChange={setSelectedRarity} />
      {filteredCardsFromPool.length === 0 ? (
        <p className="text-center text-slate-400 py-8 text-lg">
          卡池为空或该稀有度下暂无卡片。
        </p>
      ) : (
        <div className="grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 sm:gap-5">
          {filteredCardsFromPool.map(card => (
            <CardItem 
                key={card.id} 
                card={card} 
                isCollected={!!collectedCardsMap[card.id]}
                onClick={() => onCardClick(card)} // Pass the click handler
            />
          ))}
        </div>
      )}
       {Object.keys(collectedCardsMap).length === 0 && selectedRarity === 'All' && (
         <p className="text-center text-slate-400 py-8 text-lg mt-4">
           你的卡片收藏还是空的。完成任务获得积分来召唤卡片吧！
         </p>
       )}
    </div>
  );
};

export default CardGallery;