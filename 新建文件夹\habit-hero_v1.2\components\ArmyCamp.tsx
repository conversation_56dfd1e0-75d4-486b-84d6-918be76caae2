import React, { useState, useEffect, useRef } from 'react';
import { ArmyCardData } from '../types';
import { generateParadeDescription, generateCampFlag, generateFlagImage as generateFlagImageAPI } from '../services/geminiService';

// 职业emoji映射
const classEmojis: Record<string, string> = {
  '战士': '⚔️',
  '弓箭手': '🏹',
  '攻城器械': '🏰',
  '指挥官': '👑',
  '盾兵': '🛡️',
  '暗法师': '🌙',
  '召唤师': '🌟',
  '工程师': '⚙️',
  '枪兵': '🔱',
  '巨人': '🗿',
  '骑兵': '🐎',
  '领主': '👑',
  '国王': '👑',
  '火枪手': '🔫',
  '特殊武器': '💥',
  '堡垒': '🏯',
  '军乐队': '🎺',
  '近卫队': '🛡️',
  '空中单位': '✈️',
  '防空单位': '🚀',
  '舰船': '⚓',
  '法师': '🔮',
  '医师': '💊'
};

// 单位位置和移动状态接口
interface UnitPosition {
  id: string;
  x: number;
  y: number;
  targetX: number;
  targetY: number;
  nextMoveTime: number;
  moveDuration: number;
}

interface ArmyCampProps {
  armyCards: ArmyCardData[];
  onUnitClick?: (unit: ArmyCardData) => void;
  userPoints: number;
  onPointsChange: (points: number) => void;
  campName?: string;
  flagUrl?: string;
  onCampInfoChange: (campName: string, flagUrl: string) => void;
  onParadeRecord: (description: string) => void;
}

export const ArmyCamp: React.FC<ArmyCampProps> = ({
  armyCards,
  onUnitClick,
  userPoints,
  onPointsChange,
  campName: initialCampName,
  flagUrl: initialFlagUrl,
  onCampInfoChange,
  onParadeRecord
}) => {
  const [isParading, setIsParading] = useState(false);
  const [paradeDescription, setParadeDescription] = useState('');
  const [flagUrl, setFlagUrl] = useState(initialFlagUrl || '');
  const [campName, setCampName] = useState(initialCampName || '');
  const [flagPrompt, setFlagPrompt] = useState('');
  const [isGeneratingFlag, setIsGeneratingFlag] = useState(false);
  const [showFlagPrompt, setShowFlagPrompt] = useState(false);
  const [isFlagVisible, setIsFlagVisible] = useState(true);

  // 位置管理
  const [unitPositions, setUnitPositions] = useState<Map<string, UnitPosition>>(new Map());
  const animationFrameRef = useRef<number>();
  const campRef = useRef<HTMLDivElement>(null);

  // 获取职业emoji
  const getClassEmoji = (characterClass: string): string => {
    return classEmojis[characterClass] || '🛡️';
  };

  // 当props中的flagUrl变化时，更新本地状态
  useEffect(() => {
    setFlagUrl(initialFlagUrl || '');
    setCampName(initialCampName || '');
    setIsFlagVisible(true);
  }, [initialFlagUrl, initialCampName]);

  // 初始化单位位置
  useEffect(() => {
    const campWidth = 800;
    const campHeight = 400;

    const newPositions = new Map<string, UnitPosition>();

    armyCards.forEach(card => {
      if (!unitPositions.has(card.id)) {
        // 为新单位生成随机位置
        const x = Math.random() * (campWidth - 40);
        const y = Math.random() * (campHeight - 40);
        const targetX = Math.random() * (campWidth - 40);
        const targetY = Math.random() * (campHeight - 40);

        newPositions.set(card.id, {
          id: card.id,
          x,
          y,
          targetX,
          targetY,
          nextMoveTime: Date.now() + Math.random() * 5000 + 2000, // 2-7秒后开始移动
          moveDuration: Math.random() * 3000 + 2000 // 2-5秒移动时间
        });
      } else {
        // 保留现有位置
        newPositions.set(card.id, unitPositions.get(card.id)!);
      }
    });

    setUnitPositions(newPositions);
  }, [armyCards.length]);

  // 动画循环
  useEffect(() => {
    const animate = () => {
      const now = Date.now();
      const campWidth = 800;
      const campHeight = 400;

      setUnitPositions(prevPositions => {
        const newPositions = new Map(prevPositions);
        let hasChanges = false;

        newPositions.forEach((position, id) => {
          // 检查是否需要开始新的移动
          if (now >= position.nextMoveTime) {
            // 生成新的目标位置
            const newTargetX = Math.random() * (campWidth - 40);
            const newTargetY = Math.random() * (campHeight - 40);

            newPositions.set(id, {
              ...position,
              targetX: newTargetX,
              targetY: newTargetY,
              nextMoveTime: now + position.moveDuration + Math.random() * 5000 + 2000,
              moveDuration: Math.random() * 3000 + 2000
            });
            hasChanges = true;
          }

          // 平滑移动到目标位置
          const dx = position.targetX - position.x;
          const dy = position.targetY - position.y;
          const distance = Math.sqrt(dx * dx + dy * dy);

          if (distance > 1) {
            const speed = 0.02; // 移动速度
            const newX = position.x + dx * speed;
            const newY = position.y + dy * speed;

            newPositions.set(id, {
              ...position,
              x: newX,
              y: newY
            });
            hasChanges = true;
          }
        });

        return hasChanges ? newPositions : prevPositions;
      });

      animationFrameRef.current = requestAnimationFrame(animate);
    };

    animationFrameRef.current = requestAnimationFrame(animate);

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [armyCards.length]);

  // 阅兵功能
  const handleParade = async () => {
    if (userPoints < 100) {
      alert('积分不足！需要100积分进行阅兵。');
      return;
    }

    if (armyCards.length === 0) {
      alert('没有士兵，无法进行阅兵！');
      return;
    }

    setIsParading(true);
    try {
      // 先扣除积分
      const newPoints = userPoints - 100;
      onPointsChange(newPoints);

      // 转换armyCards为DeployedCard格式用于阅兵
      const deployedCards = armyCards.map(card => ({
        ...card,
        deploymentId: `deployed-${card.id}`,
        position: { top: '0px', left: '0px' }
      }));

      const description = await generateParadeDescription(deployedCards);
      setParadeDescription(description);

      // 记录阅兵活动到时间轴
      const paradeRecord = `🎖️ 阅兵仪式：${description}`;
      onParadeRecord(paradeRecord);

    } catch (error) {
      console.error('阅兵失败:', error);
      alert('阅兵失败，请重试');
    } finally {
      setIsParading(false);
    }
  };

  // 生成旗帜
  const handleGenerateFlag = async () => {
    if (userPoints < 100) {
      alert('积分不足！需要100积分生成旗帜。');
      return;
    }

    setIsGeneratingFlag(true);
    try {
      // 先扣除积分
      const newPoints = userPoints - 100;
      onPointsChange(newPoints);

      let prompt = flagPrompt.trim();
      let name = campName;

      // 如果没有输入提示词，让AI根据军队生成
      if (!prompt && armyCards.length > 0) {
        const deployedCards = armyCards.map(card => ({
          ...card,
          deploymentId: `deployed-${card.id}`,
          position: { top: '0px', left: '0px' }
        }));
        const result = await generateCampFlag(deployedCards);
        prompt = result.flagPrompt;
        name = result.campName;
        setCampName(name);
      } else if (!prompt) {
        prompt = '一面威严的军旗，象征着荣耀与力量';
      }

      const imageUrl = await generateFlagImageAPI(prompt);
      setFlagUrl(imageUrl);
      setShowFlagPrompt(false);
      setFlagPrompt('');
      setIsFlagVisible(true);

      // 通知父组件更新
      onCampInfoChange(name, imageUrl);

    } catch (error) {
      console.error('生成旗帜失败:', error);
      alert('生成旗帜失败，请重试');
    } finally {
      setIsGeneratingFlag(false);
    }
  };

  const handleFlagClick = () => {
    setIsFlagVisible(!isFlagVisible);
  };

  const progressPercentage = Math.min((armyCards.length / 1000) * 100, 100);

  return (
    <div className="w-full space-y-4">
      {/* 进度条和控制面板 */}
      <div className="bg-slate-800 rounded-lg p-4 border border-slate-600">
        <div className="flex items-center justify-between">
          {/* 左侧：进度信息 */}
          <div className="flex-1">
            <div className="flex items-center justify-between mb-2">
              <h3 className="text-lg font-bold text-white">
                {campName || '军营'}
                <span className="text-sm text-gray-400 ml-2">({armyCards.length}/1000 士兵)</span>
              </h3>
              <span className="text-sm text-gray-400">{progressPercentage.toFixed(1)}%</span>
            </div>
            <div className="w-full bg-gray-700 rounded-full h-3 overflow-hidden">
              <div
                className="h-full bg-gradient-to-r from-blue-500 to-purple-600 transition-all duration-500 ease-out"
                style={{ width: `${progressPercentage}%` }}
              />
            </div>
          </div>

          {/* 右侧：控制按钮 */}
          <div className="flex gap-2 flex-wrap ml-4">
            <button
              onClick={handleParade}
              disabled={isParading || armyCards.length === 0 || userPoints < 100}
              className="px-3 py-2 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
            >
              {isParading ? '阅兵中...' : '🎖️ 阅兵 (100积分)'}
            </button>

            <button
              onClick={() => setShowFlagPrompt(!showFlagPrompt)}
              disabled={isGeneratingFlag || userPoints < 100}
              className="px-3 py-2 bg-amber-600 hover:bg-amber-700 disabled:bg-gray-600 disabled:cursor-not-allowed text-white rounded-lg transition-colors text-sm font-medium"
            >
              {isGeneratingFlag ? '生成中...' : '🏴 生成旗帜 (100积分)'}
            </button>
          </div>
        </div>

        {/* 旗帜生成提示框 */}
        {showFlagPrompt && (
          <div className="mt-4 p-3 bg-slate-700 rounded-lg border border-slate-600">
            <div className="flex gap-2">
              <input
                type="text"
                value={flagPrompt}
                onChange={(e) => setFlagPrompt(e.target.value)}
                placeholder="描述你想要的旗帜样式（可选，留空将根据军队自动生成）"
                className="flex-1 p-2 bg-slate-600 text-white rounded border border-slate-500 focus:outline-none focus:ring-2 focus:ring-amber-500"
              />
              <button
                onClick={handleGenerateFlag}
                disabled={isGeneratingFlag}
                className="px-4 py-2 bg-amber-600 hover:bg-amber-700 disabled:bg-gray-600 text-white rounded transition-colors"
              >
                生成
              </button>
            </div>
          </div>
        )}

        {/* 阅兵描述显示 */}
        {paradeDescription && (
          <div className="mt-4 p-3 bg-purple-900/30 rounded-lg border border-purple-500/50">
            <h4 className="text-purple-300 font-semibold mb-2">🎖️ 阅兵报告</h4>
            <p className="text-gray-300 text-sm leading-relaxed">{paradeDescription}</p>
          </div>
        )}
      </div>

      {/* 营地区域 - 自由移动显示 */}
      <div
        ref={campRef}
        className="relative bg-gradient-to-b from-green-900/20 to-green-800/30 rounded-lg border-2 border-green-600/30 h-[400px] overflow-hidden"
      >
        {/* 旗帜显示 */}
        {flagUrl && isFlagVisible && (
          <div className="absolute top-4 right-4 z-10">
            <img
              src={flagUrl}
              alt="军营旗帜"
              className="w-16 h-12 object-cover rounded border-2 border-yellow-400/50 cursor-pointer hover:border-yellow-400 transition-colors shadow-lg"
              onClick={handleFlagClick}
              title="点击隐藏/显示旗帜"
            />
          </div>
        )}

        {/* 无旗帜时的占位符 */}
        {!flagUrl && (
          <div className="absolute top-4 right-4 z-10 cursor-pointer" onClick={() => setShowFlagPrompt(true)}>
            <div className="bg-black/50 rounded-lg p-2 border border-gray-500/50 hover:border-yellow-400/50 transition-colors">
              <span className="text-yellow-400 text-lg">🏴</span>
            </div>
          </div>
        )}

        {armyCards.length === 0 ? (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="text-center text-gray-500">
              <div className="text-6xl mb-4">🏕️</div>
              <p className="text-lg">营地空无一人</p>
              <p className="text-sm mt-2">召唤你的第一名士兵吧！</p>
            </div>
          </div>
        ) : (
          <>
            {armyCards.map((card) => {
              const position = unitPositions.get(card.id);
              if (!position) return null;

              return (
                <div
                  key={card.id}
                  className="absolute cursor-pointer hover:scale-110 transition-transform duration-200 select-none"
                  onClick={() => onUnitClick?.(card)}
                  style={{
                    left: `${position.x}px`,
                    top: `${position.y}px`,
                    transform: 'translate(-50%, -50%)',
                    transition: 'transform 0.2s ease'
                  }}
                  title={`${card.name} (${card.rarity.level}) - ${card.characterClass}`}
                >
                  {/* 职业emoji显示 */}
                  <div
                    className="text-2xl drop-shadow-lg relative"
                    style={{
                      filter: `drop-shadow(0 0 4px ${card.rarity.glowColor})`
                    }}
                  >
                    {getClassEmoji(card.characterClass)}

                    {/* 稀有度指示器 */}
                    <div
                      className="absolute -top-1 -right-1 w-3 h-3 rounded-full text-xs flex items-center justify-center font-bold"
                      style={{
                        backgroundColor: card.rarity.glowColor,
                        color: '#000',
                        fontSize: '8px'
                      }}
                    >
                      {card.rarity.level.charAt(0)}
                    </div>
                  </div>
                </div>
              );
            })}
          </>
        )}
      </div>
      
      {armyCards.length > 0 && (
        <div className="mt-4 text-center">
          <div className="flex justify-center flex-wrap gap-2 text-xs text-gray-400">
            <span>稀有度分布:</span>
            {Object.entries(
              armyCards.reduce((acc, card) => {
                acc[card.rarity.level] = (acc[card.rarity.level] || 0) + 1;
                return acc;
              }, {} as Record<string, number>)
            ).map(([rarity, count]) => (
              <span key={rarity} className="bg-gray-700 px-2 py-1 rounded">
                {rarity}: {count}
              </span>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};
