
import React from 'react';
import { GACHA_COST } from '../constants';
import { GiftIcon } from './icons';

interface GachaSystemProps {
  userPoints: number;
  onDrawCard: () => void;
  canDrawMore: boolean; // Indicates if there are any cards left to draw at all
}

// 获取召唤消耗积分的函数
const getSummonCost = (): number => {
  const saved = localStorage.getItem('app-settings');
  if (saved) {
    try {
      const settings = JSON.parse(saved);
      return settings.summonCost || GACHA_COST; // 使用设置中的值，如果没有则使用默认值
    } catch {
      return GACHA_COST;
    }
  }
  return GACHA_COST;
};

const GachaSystem: React.FC<GachaSystemProps> = ({ userPoints, onDrawCard, canDrawMore }) => {
  const summonCost = getSummonCost();
  const canAfford = userPoints >= summonCost;

  return (
    <div className="p-6 bg-slate-800 rounded-xl shadow-2xl text-center">
      <h3 className="text-2xl font-semibold text-sky-400 mb-2">卡片召唤</h3>
      <p className="text-slate-300 mb-4">费用: {summonCost} 积分</p>
      {!canDrawMore ? (
         <p className="text-amber-400 font-semibold mb-4">恭喜！您已集齐所有可获得的卡片！</p>
      ) : (
        <button
        onClick={onDrawCard}
        disabled={!canAfford}
        className={`w-full py-3 px-6 rounded-lg font-semibold text-white transition-all duration-200 ease-in-out transform hover:scale-105
          ${canAfford 
            ? 'bg-gradient-to-r from-purple-600 to-indigo-600 hover:from-purple-700 hover:to-indigo-700 shadow-lg' 
            : 'bg-slate-600 cursor-not-allowed opacity-70'}
        `}
        aria-live="polite"
      >
        <GiftIcon className="w-5 h-5 inline-block mr-2" />
        {canAfford ? '召唤一张卡片！' : '积分不足'}
      </button>
      )}
      {!canAfford && canDrawMore && (
        <p className="text-sm text-red-400 mt-3">还差 {summonCost - userPoints} 积分才能召唤。</p>
      )}
    </div>
  );
};

export default GachaSystem;
