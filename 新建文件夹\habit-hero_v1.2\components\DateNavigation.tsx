import React from 'react';
import { ChevronLeftIcon, ChevronRightIcon, CalendarIcon } from './icons';

interface DateNavigationProps {
  selectedDate: Date;
  onDateChange: (date: Date) => void;
  showCalendarButton?: boolean;
  onCalendarClick?: () => void;
}

const DateNavigation: React.FC<DateNavigationProps> = ({
  selectedDate,
  onDateChange,
  showCalendarButton = false,
  onCalendarClick
}) => {
  const formatDate = (date: Date): string => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    // 检查是否是今天、昨天或明天
    if (date.toDateString() === today.toDateString()) {
      return '今天';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return '昨天';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return '明天';
    }

    // 否则显示完整日期
    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      weekday: 'long'
    });
  };

  const formatShortDate = (date: Date): string => {
    return date.toLocaleDateString('zh-CN', {
      month: '2-digit',
      day: '2-digit'
    });
  };

  const goToPreviousDay = () => {
    const previousDay = new Date(selectedDate);
    previousDay.setDate(selectedDate.getDate() - 1);
    onDateChange(previousDay);
  };

  const goToNextDay = () => {
    const nextDay = new Date(selectedDate);
    nextDay.setDate(selectedDate.getDate() + 1);
    onDateChange(nextDay);
  };

  const goToToday = () => {
    onDateChange(new Date());
  };

  const isToday = selectedDate.toDateString() === new Date().toDateString();

  return (
    <div className="flex items-center justify-between bg-slate-800/50 rounded-lg p-4 border border-slate-700/50">
      {/* 左侧：前一天按钮 */}
      <button
        onClick={goToPreviousDay}
        className="flex items-center justify-center w-10 h-10 rounded-lg bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white transition-all duration-200 group"
        title="前一天"
      >
        <ChevronLeftIcon className="w-5 h-5 group-hover:scale-110 transition-transform" />
      </button>

      {/* 中间：日期显示 */}
      <div className="flex-1 text-center">
        <div className="flex items-center justify-center space-x-3">
          {/* 主要日期显示 */}
          <div className="text-lg font-semibold text-white">
            {formatDate(selectedDate)}
          </div>
          
          {/* 如果不是今天，显示回到今天的按钮 */}
          {!isToday && (
            <button
              onClick={goToToday}
              className="px-3 py-1 text-xs bg-sky-600 hover:bg-sky-700 text-white rounded-full transition-colors"
            >
              回到今天
            </button>
          )}
          
          {/* 日历按钮 */}
          {showCalendarButton && onCalendarClick && (
            <button
              onClick={onCalendarClick}
              className="flex items-center justify-center w-8 h-8 rounded-lg bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white transition-all duration-200"
              title="打开日历"
            >
              <CalendarIcon className="w-4 h-4" />
            </button>
          )}
        </div>
        
        {/* 副标题：显示具体日期 */}
        <div className="text-sm text-slate-400 mt-1">
          {selectedDate.toLocaleDateString('zh-CN', {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit'
          })} • {selectedDate.toLocaleDateString('zh-CN', { weekday: 'long' })}
        </div>
      </div>

      {/* 右侧：后一天按钮 */}
      <button
        onClick={goToNextDay}
        className="flex items-center justify-center w-10 h-10 rounded-lg bg-slate-700 hover:bg-slate-600 text-slate-300 hover:text-white transition-all duration-200 group"
        title="后一天"
      >
        <ChevronRightIcon className="w-5 h-5 group-hover:scale-110 transition-transform" />
      </button>
    </div>
  );
};

export default DateNavigation;
