import sqlite3
import os
import sys

def get_database_path():
    """获取数据库路径"""
    print('📁 数据库路径选择:')
    print('1. 当前桌面路径: C:\\Users\\<USER>\\Desktop\\habit-hero.db')
    print('2. 默认应用数据路径: %APPDATA%\\habit-hero\\habit-hero.db')
    print('3. 自定义路径')
    print()

    while True:
        choice = input('请选择 (1/2/3): ').strip()

        if choice == '1':
            return r'C:\Users\<USER>\Desktop\habit-hero.db'
        elif choice == '2':
            appdata = os.environ.get('APPDATA', '')
            return os.path.join(appdata, 'habit-hero', 'habit-hero.db')
        elif choice == '3':
            custom_path = input('请输入完整的数据库文件路径: ').strip()
            if custom_path:
                return custom_path
            else:
                print('❌ 路径不能为空，请重新选择')
        else:
            print('❌ 无效选择，请输入 1、2 或 3')

def fix_database(db_path=None):
    if db_path is None:
        db_path = get_database_path()
    
    print('=' * 60)
    print('Habit Hero 数据库修复工具')
    print('=' * 60)
    print(f'数据库路径: {db_path}')
    
    if not os.path.exists(db_path):
        print('❌ 数据库文件不存在！')
        return
    
    print('✅ 数据库文件存在')
    print()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 检查当前表结构
        print('🔍 检查当前 army_cards 表结构...')
        cursor.execute("PRAGMA table_info(army_cards)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        # 检查 style_prompt 列
        has_style_prompt = 'style_prompt' in column_names
        print(f'style_prompt 列存在: {has_style_prompt}')

        if not has_style_prompt:
            print('📝 添加 style_prompt 列...')
            cursor.execute("ALTER TABLE army_cards ADD COLUMN style_prompt TEXT")
            print('✅ 成功添加 style_prompt 列')
        else:
            print('✅ style_prompt 列已存在')

        # 检查 attributes 列（新增）
        has_attributes = 'attributes' in column_names
        print(f'attributes 列存在: {has_attributes}')

        if not has_attributes:
            print('📝 添加 attributes 列（属性系统支持）...')
            cursor.execute("ALTER TABLE army_cards ADD COLUMN attributes TEXT")
            print('✅ 成功添加 attributes 列')
        else:
            print('✅ attributes 列已存在')

        # 检查 user_settings 表的 attribute_schema 列
        print('🔍 检查 user_settings 表结构...')
        cursor.execute("PRAGMA table_info(user_settings)")
        user_columns = cursor.fetchall()
        user_column_names = [col[1] for col in user_columns]

        has_attribute_schema = 'attribute_schema' in user_column_names
        print(f'attribute_schema 列存在: {has_attribute_schema}')

        if not has_attribute_schema:
            print('📝 添加 attribute_schema 列（属性模式存储）...')
            cursor.execute("ALTER TABLE user_settings ADD COLUMN attribute_schema TEXT")
            print('✅ 成功添加 attribute_schema 列')
        else:
            print('✅ attribute_schema 列已存在')

        # 检查自定义职业表
        print('🔍 检查自定义职业表...')
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_character_classes'")
        custom_class_table_exists = cursor.fetchone() is not None
        print(f'custom_character_classes 表存在: {custom_class_table_exists}')

        if not custom_class_table_exists:
            print('📝 创建自定义职业表...')
            cursor.execute("""
                CREATE TABLE custom_character_classes (
                    id TEXT PRIMARY KEY,
                    name TEXT NOT NULL,
                    description TEXT,
                    faction TEXT,
                    cost INTEGER DEFAULT 10,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
                )
            """)
            print('✅ 成功创建 custom_character_classes 表')
        else:
            print('✅ custom_character_classes 表已存在')
        
        # 创建版本表
        print('📊 创建版本管理表...')
        cursor.execute("""
            CREATE TABLE IF NOT EXISTS db_version (
                version INTEGER PRIMARY KEY,
                applied_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        """)
        
        # 记录迁移版本
        cursor.execute("INSERT OR IGNORE INTO db_version (version) VALUES (1)")
        cursor.execute("INSERT OR IGNORE INTO db_version (version) VALUES (2)")  # 自定义职业功能版本
        cursor.execute("INSERT OR IGNORE INTO db_version (version) VALUES (3)")  # 属性系统版本

        # 提交更改
        conn.commit()
        print('✅ 数据库结构更新完成')

        # 验证更改
        print()
        print('🔍 验证更改...')
        cursor.execute("PRAGMA table_info(army_cards)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        if 'style_prompt' in column_names:
            print('✅ style_prompt 列验证成功')
        else:
            print('❌ style_prompt 列验证失败')

        if 'attributes' in column_names:
            print('✅ attributes 列验证成功')
        else:
            print('❌ attributes 列验证失败')

        # 验证 user_settings 表
        cursor.execute("PRAGMA table_info(user_settings)")
        user_columns = cursor.fetchall()
        user_column_names = [col[1] for col in user_columns]

        if 'attribute_schema' in user_column_names:
            print('✅ attribute_schema 列验证成功')
        else:
            print('❌ attribute_schema 列验证失败')

        # 验证自定义职业表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='custom_character_classes'")
        if cursor.fetchone():
            print('✅ custom_character_classes 表验证成功')
        else:
            print('❌ custom_character_classes 表验证失败')
        
        # 检查版本表
        cursor.execute("SELECT * FROM db_version")
        versions = cursor.fetchall()
        print(f'数据库版本: {[v[0] for v in versions]}')
        
        # 统计数据
        print()
        print('📈 更新后统计:')
        cursor.execute("SELECT COUNT(*) FROM army_cards")
        total_cards = cursor.fetchone()[0]
        print(f'总卡片数: {total_cards}')

        cursor.execute("SELECT COUNT(*) FROM army_cards WHERE style_prompt IS NOT NULL AND style_prompt != ''")
        with_style = cursor.fetchone()[0]
        print(f'有阵营标签: {with_style}')
        print(f'无阵营标签: {total_cards - with_style}')

        # 统计属性数据
        cursor.execute("SELECT COUNT(*) FROM army_cards WHERE attributes IS NOT NULL AND attributes != ''")
        with_attributes = cursor.fetchone()[0]
        print(f'有属性数据: {with_attributes}')
        print(f'无属性数据: {total_cards - with_attributes}')

        # 检查属性模式
        cursor.execute("SELECT attribute_schema FROM user_settings WHERE id = 1")
        schema_result = cursor.fetchone()
        has_schema = schema_result and schema_result[0] is not None
        print(f'属性模式已配置: {has_schema}')

        # 统计自定义职业
        cursor.execute("SELECT COUNT(*) FROM custom_character_classes")
        custom_classes = cursor.fetchone()[0]
        print(f'自定义职业数: {custom_classes}')

        if custom_classes > 0:
            cursor.execute("SELECT name, faction FROM custom_character_classes ORDER BY created_at DESC LIMIT 5")
            recent_classes = cursor.fetchall()
            print('最近创建的自定义职业:')
            for name, faction in recent_classes:
                print(f'  - {name} ({faction})')

        conn.close()
        print()
        print('🎉 数据库修复完成！现在可以使用以下功能：')
        print('   ✅ 阵营标签系统')
        print('   ✅ 自定义职业功能')
        print('   ✅ 角色属性系统（新增）')
        print('   ✅ 属性模式存储（新增）')
        print()
        print('💡 建议重新启动应用以确保更改生效。')
        print('🎯 现在可以在"属性管理"页面为角色生成属性数据了！')
        
    except Exception as e:
        print(f'❌ 数据库修复失败: {e}')

def check_database_status(db_path=None):
    """检查数据库状态，不进行修改"""
    if db_path is None:
        db_path = get_database_path()

    print('=' * 60)
    print('Habit Hero 数据库状态检查')
    print('=' * 60)
    print(f'数据库路径: {db_path}')

    if not os.path.exists(db_path):
        print('❌ 数据库文件不存在！')
        return

    print('✅ 数据库文件存在')
    print()

    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()

        # 检查表结构
        print('🔍 检查表结构...')

        # army_cards 表
        cursor.execute("PRAGMA table_info(army_cards)")
        columns = cursor.fetchall()
        column_names = [col[1] for col in columns]

        print('army_cards 表字段:')
        required_fields = ['style_prompt', 'attributes']
        for field in required_fields:
            status = '✅' if field in column_names else '❌'
            print(f'  {status} {field}')

        # user_settings 表
        cursor.execute("PRAGMA table_info(user_settings)")
        user_columns = cursor.fetchall()
        user_column_names = [col[1] for col in user_columns]

        print('user_settings 表字段:')
        user_required_fields = ['attribute_schema']
        for field in user_required_fields:
            status = '✅' if field in user_column_names else '❌'
            print(f'  {status} {field}')

        # 检查表存在性
        print('数据表:')
        tables_to_check = ['custom_character_classes', 'db_version']
        for table in tables_to_check:
            cursor.execute(f"SELECT name FROM sqlite_master WHERE type='table' AND name='{table}'")
            exists = cursor.fetchone() is not None
            status = '✅' if exists else '❌'
            print(f'  {status} {table}')

        # 统计数据
        print()
        print('📊 数据统计:')
        cursor.execute("SELECT COUNT(*) FROM army_cards")
        total_cards = cursor.fetchone()[0]
        print(f'总卡片数: {total_cards}')

        if 'attributes' in column_names:
            cursor.execute("SELECT COUNT(*) FROM army_cards WHERE attributes IS NOT NULL AND attributes != ''")
            with_attributes = cursor.fetchone()[0]
            print(f'有属性数据: {with_attributes} ({with_attributes/total_cards*100:.1f}%)')

        if 'attribute_schema' in user_column_names:
            cursor.execute("SELECT attribute_schema FROM user_settings WHERE id = 1")
            schema_result = cursor.fetchone()
            has_schema = schema_result and schema_result[0] is not None
            print(f'属性模式配置: {"✅ 已配置" if has_schema else "❌ 未配置"}')

        # 检查版本
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name='db_version'")
        if cursor.fetchone():
            cursor.execute("SELECT * FROM db_version ORDER BY version")
            versions = cursor.fetchall()
            print(f'数据库版本: {[v[0] for v in versions]}')

            latest_version = max([v[0] for v in versions]) if versions else 0
            if latest_version >= 3:
                print('✅ 数据库已支持属性系统')
            else:
                print('⚠️  数据库需要升级以支持属性系统')
        else:
            print('⚠️  未找到版本信息，建议运行数据库修复')

        conn.close()

    except Exception as e:
        print(f'❌ 检查失败: {e}')

def batch_fix_databases():
    """批量修复多个数据库"""
    print('🔧 批量数据库修复模式')
    print('请输入要修复的数据库路径，每行一个，输入空行结束:')

    paths = []
    while True:
        path = input('数据库路径: ').strip()
        if not path:
            break
        if os.path.exists(path):
            paths.append(path)
            print(f'✅ 已添加: {path}')
        else:
            print(f'❌ 文件不存在: {path}')

    if not paths:
        print('❌ 没有有效的数据库路径')
        return

    print(f'\n📋 将修复 {len(paths)} 个数据库:')
    for i, path in enumerate(paths, 1):
        print(f'  {i}. {path}')

    confirm = input('\n确认批量修复？(输入 yes 继续): ')
    if confirm.lower() == 'yes':
        for i, path in enumerate(paths, 1):
            print(f'\n🔧 修复数据库 {i}/{len(paths)}: {path}')
            try:
                fix_database(path)
            except Exception as e:
                print(f'❌ 修复失败: {e}')
        print('\n🎉 批量修复完成！')
    else:
        print('操作已取消')

def main():
    print('=' * 60)
    print('🛠️  Habit Hero 数据库修复工具 v3.0 - 属性系统支持')
    print('=' * 60)
    print()

    print('选择操作模式:')
    print('1. 检查数据库状态（只读，不修改）')
    print('2. 单个数据库修复')
    print('3. 批量数据库修复')
    print('4. 退出')
    print()

    while True:
        choice = input('请选择 (1/2/3/4): ').strip()

        if choice == '1':
            print('\n📊 检查数据库状态（只读模式）')
            check_database_status()
            break

        elif choice == '2':
            print('\n⚠️  这将修改您的数据库结构，添加属性系统支持。')
            print('📋 操作内容:')
            print('   1. 为 army_cards 表添加 style_prompt 列（阵营标签）')
            print('   2. 为 army_cards 表添加 attributes 列（角色属性）')
            print('   3. 为 user_settings 表添加 attribute_schema 列（属性模式）')
            print('   4. 创建 custom_character_classes 表（自定义职业）')
            print('   5. 创建数据库版本管理表')
            print('   6. 记录迁移版本（v3.0 - 属性系统）')
            print()

            confirm = input('确认执行修复？(输入 yes 继续): ')
            if confirm.lower() == 'yes':
                fix_database()
            else:
                print('操作已取消')
            break

        elif choice == '3':
            batch_fix_databases()
            break

        elif choice == '4':
            print('👋 再见！')
            sys.exit(0)

        else:
            print('❌ 无效选择，请输入 1、2、3 或 4')

if __name__ == '__main__':
    main()
