/**
 * SQLite Database Service for Habit Hero Army System
 * Handles all database operations including army cards, images, and user data
 */

import Database from 'better-sqlite3';
import path from 'path';
import { ArmyCardData, DeployedCard, Task, JournalEntry, WeightRecord, UserData } from '../types';

interface DatabaseConfig {
  dbPath: string;
  enableWAL?: boolean;
  enableForeignKeys?: boolean;
}

class SQLiteService {
  private db: Database.Database | null = null;
  private isInitialized = false;

  constructor(private config: DatabaseConfig) {}

  /**
   * Initialize the database and create tables
   */
  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      this.db = new Database(this.config.dbPath);
      
      // Enable WAL mode for better performance
      if (this.config.enableWAL) {
        this.db.pragma('journal_mode = WAL');
      }
      
      // Enable foreign keys
      if (this.config.enableForeignKeys) {
        this.db.pragma('foreign_keys = ON');
      }

      // Create tables
      this.createTables();
      this.isInitialized = true;
      
      console.log('SQLite database initialized successfully');
    } catch (error) {
      console.error('Failed to initialize SQLite database:', error);
      throw error;
    }
  }

  /**
   * Create all necessary tables
   */
  private createTables(): void {
    if (!this.db) throw new Error('Database not initialized');

    // User settings table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS user_settings (
        id INTEGER PRIMARY KEY,
        points INTEGER DEFAULT 0,
        army_style_prompt TEXT DEFAULT '',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Tasks table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS tasks (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL,
        description TEXT,
        emoji TEXT,
        points INTEGER DEFAULT 10,
        type TEXT DEFAULT 'daily',
        category TEXT,
        is_highlighted INTEGER DEFAULT 0,
        days_of_week TEXT, -- JSON array for weekly tasks
        completed_dates TEXT, -- JSON array of completion dates
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Add missing columns to existing tasks table
    try {
      this.db.exec(`ALTER TABLE tasks ADD COLUMN is_highlighted INTEGER DEFAULT 0`);
    } catch (e) {
      // Column already exists, ignore
    }

    // Journal entries table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS journal_entries (
        id TEXT PRIMARY KEY,
        date TEXT NOT NULL,
        content TEXT NOT NULL,
        icon TEXT,
        type TEXT DEFAULT 'output',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Add missing columns to existing journal_entries table
    try {
      this.db.exec(`ALTER TABLE journal_entries ADD COLUMN icon TEXT`);
    } catch (e) {
      // Column already exists, ignore
    }
    try {
      this.db.exec(`ALTER TABLE journal_entries ADD COLUMN type TEXT DEFAULT 'output'`);
    } catch (e) {
      // Column already exists, ignore
    }

    // Weight records table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS weight_records (
        id TEXT PRIMARY KEY,
        weight REAL NOT NULL,
        date TEXT NOT NULL,
        note TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Army cards table
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS army_cards (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        character_class TEXT NOT NULL,
        description TEXT NOT NULL,
        rarity_level TEXT NOT NULL,
        rarity_color TEXT NOT NULL,
        rarity_glow_color TEXT NOT NULL,
        rarity_probability REAL NOT NULL,
        image_data BLOB, -- Store image as BLOB
        image_url TEXT, -- Fallback URL
        skills TEXT, -- JSON array of skills
        story TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Camp units table (deployed cards)
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS camp_units (
        id TEXT PRIMARY KEY,
        army_card_id TEXT NOT NULL,
        deployment_id TEXT NOT NULL,
        position_top TEXT NOT NULL,
        position_left TEXT NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (army_card_id) REFERENCES army_cards (id) ON DELETE CASCADE
      )
    `);

    // Initialize user settings if not exists
    const userExists = this.db.prepare('SELECT COUNT(*) as count FROM user_settings').get() as { count: number };
    if (userExists.count === 0) {
      this.db.prepare('INSERT INTO user_settings (points) VALUES (100)').run();
    }
  }

  /**
   * Get user data
   */
  async getUserData(): Promise<UserData> {
    if (!this.db) throw new Error('Database not initialized');

    const userSettings = this.db.prepare('SELECT * FROM user_settings WHERE id = 1').get() as any;
    const tasks = this.db.prepare('SELECT * FROM tasks').all() as any[];
    const journalEntries = this.db.prepare('SELECT * FROM journal_entries ORDER BY date DESC').all() as any[];
    const weightRecords = this.db.prepare('SELECT * FROM weight_records ORDER BY date DESC').all() as any[];
    const armyCards = this.db.prepare('SELECT * FROM army_cards ORDER BY created_at DESC').all() as any[];
    const campUnits = this.db.prepare(`
      SELECT cu.*, ac.* FROM camp_units cu
      JOIN army_cards ac ON cu.army_card_id = ac.id
      ORDER BY cu.created_at DESC
    `).all() as any[];

    return {
      points: userSettings?.points || 100,
      tasks: tasks.map(this.mapTaskFromDB),
      journalEntries: journalEntries.map(this.mapJournalEntryFromDB),
      weightRecords: weightRecords.map(this.mapWeightRecordFromDB),
      armyCards: armyCards.map(this.mapArmyCardFromDB),
      campUnits: campUnits.map(this.mapCampUnitFromDB),
      armyStylePrompt: userSettings?.army_style_prompt || '',
    };
  }

  /**
   * Save army card
   */
  async saveArmyCard(card: ArmyCardData): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO army_cards (
        id, name, character_class, description,
        rarity_level, rarity_color, rarity_glow_color, rarity_probability,
        image_url, skills, story
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      card.id,
      card.name,
      card.characterClass,
      card.description,
      card.rarity.level,
      card.rarity.color,
      card.rarity.glowColor,
      card.rarity.probability,
      card.imageUrl,
      JSON.stringify(card.skills),
      card.story
    );
  }

  /**
   * Save camp unit
   */
  async saveCampUnit(unit: DeployedCard): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    // First ensure the army card exists
    await this.saveArmyCard(unit);

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO camp_units (
        id, army_card_id, deployment_id, position_top, position_left
      ) VALUES (?, ?, ?, ?, ?)
    `);

    stmt.run(
      unit.deploymentId,
      unit.id,
      unit.deploymentId,
      unit.position.top,
      unit.position.left
    );
  }

  /**
   * Update user points
   */
  async updateUserPoints(points: number): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare('UPDATE user_settings SET points = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1');
    stmt.run(points);
  }

  /**
   * Update army style prompt
   */
  async updateArmyStylePrompt(prompt: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare('UPDATE user_settings SET army_style_prompt = ?, updated_at = CURRENT_TIMESTAMP WHERE id = 1');
    stmt.run(prompt);
  }

  /**
   * Delete army card
   */
  async deleteArmyCard(cardId: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare('DELETE FROM army_cards WHERE id = ?');
    stmt.run(cardId);
  }

  /**
   * Save task
   */
  async saveTask(task: Task): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO tasks (
        id, title, description, emoji, points, type, category, is_highlighted, days_of_week, completed_dates
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      task.id,
      task.title,
      task.description || '',
      task.emoji || '',
      task.points || 10,
      task.type,
      task.category || '',
      task.isHighlighted ? 1 : 0,
      JSON.stringify(task.daysOfWeek || []),
      JSON.stringify(task.completedDates || [])
    );
  }

  /**
   * Save journal entry
   */
  async saveJournalEntry(entry: JournalEntry): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO journal_entries (id, date, content, icon, type, created_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `);

    stmt.run(
      entry.id,
      entry.date,
      entry.content,
      entry.icon || null,
      entry.type || 'output',
      entry.createdAt || new Date().toISOString()
    );
  }

  // Mapping functions
  private mapTaskFromDB(row: any): Task {
    return {
      id: row.id,
      title: row.title,
      description: row.description,
      emoji: row.emoji,
      points: row.points,
      type: row.type,
      category: row.category,
      isHighlighted: row.is_highlighted === 1,
      daysOfWeek: row.days_of_week ? JSON.parse(row.days_of_week) : [],
      completedDates: row.completed_dates ? JSON.parse(row.completed_dates) : [],
    };
  }

  private mapJournalEntryFromDB(row: any): JournalEntry {
    return {
      id: row.id,
      date: row.date,
      content: row.content,
      icon: row.icon,
      type: row.type || 'output',
      createdAt: row.created_at,
    };
  }

  private mapArmyCardFromDB(row: any): ArmyCardData {
    return {
      id: row.id,
      name: row.name,
      characterClass: row.character_class,
      description: row.description,
      rarity: {
        level: row.rarity_level,
        color: row.rarity_color,
        glowColor: row.rarity_glow_color,
        probability: row.rarity_probability,
      },
      imageUrl: row.image_url,
      skills: row.skills ? JSON.parse(row.skills) : [],
      story: row.story,
    };
  }

  private mapCampUnitFromDB(row: any): DeployedCard {
    const armyCard = this.mapArmyCardFromDB(row);

    // 确保位置格式正确（像素值）
    let top = row.position_top || '0px';
    let left = row.position_left || '0px';

    // 如果没有单位，添加px
    if (!top.includes('px') && !top.includes('%')) {
      top = `${top}px`;
    }
    if (!left.includes('px') && !left.includes('%')) {
      left = `${left}px`;
    }

    return {
      ...armyCard,
      deploymentId: row.deployment_id,
      position: {
        top,
        left,
      },
    };
  }

  private mapWeightRecordFromDB(row: any): WeightRecord {
    return {
      id: row.id,
      weight: row.weight,
      date: row.date,
      note: row.note,
      createdAt: row.created_at,
    };
  }

  /**
   * Save weight record
   */
  async saveWeightRecord(record: WeightRecord): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare(`
      INSERT OR REPLACE INTO weight_records (id, weight, date, note)
      VALUES (?, ?, ?, ?)
    `);

    stmt.run(record.id, record.weight, record.date, record.note || null);
  }

  /**
   * Delete weight record
   */
  async deleteWeightRecord(id: string): Promise<void> {
    if (!this.db) throw new Error('Database not initialized');

    const stmt = this.db.prepare('DELETE FROM weight_records WHERE id = ?');
    stmt.run(id);
  }

  /**
   * Close database connection
   */
  close(): void {
    if (this.db) {
      this.db.close();
      this.db = null;
      this.isInitialized = false;
    }
  }

  /**
   * Get database statistics
   */
  async getStats(): Promise<{
    armyCards: number;
    campUnits: number;
    tasks: number;
    journalEntries: number;
    weightRecords: number;
    dbSize: number;
  }> {
    if (!this.db) throw new Error('Database not initialized');

    const armyCards = this.db.prepare('SELECT COUNT(*) as count FROM army_cards').get() as { count: number };
    const campUnits = this.db.prepare('SELECT COUNT(*) as count FROM camp_units').get() as { count: number };
    const tasks = this.db.prepare('SELECT COUNT(*) as count FROM tasks').get() as { count: number };
    const journalEntries = this.db.prepare('SELECT COUNT(*) as count FROM journal_entries').get() as { count: number };
    const weightRecords = this.db.prepare('SELECT COUNT(*) as count FROM weight_records').get() as { count: number };

    // Get database file size (this would need to be implemented differently in browser environment)
    const dbSize = 0; // Placeholder

    return {
      armyCards: armyCards.count,
      campUnits: campUnits.count,
      tasks: tasks.count,
      journalEntries: journalEntries.count,
      weightRecords: weightRecords.count,
      dbSize,
    };
  }
}

// Create and export singleton instance
const dbPath = path.join(process.cwd(), 'habit-hero.db');
export const sqliteService = new SQLiteService({
  dbPath,
  enableWAL: true,
  enableForeignKeys: true,
});

export default sqliteService;
