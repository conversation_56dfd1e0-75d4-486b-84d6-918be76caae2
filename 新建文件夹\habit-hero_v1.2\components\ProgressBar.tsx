
import React from 'react';

interface ProgressBarProps {
  current: number;
  total: number;
}

const ProgressBar: React.FC<ProgressBarProps> = ({ current, total }) => {
  const percentage = total > 0 ? Math.min((current / total) * 100, 100) : 0;

  return (
    <div className="w-full">
      <div className="flex justify-between mb-1 text-sm">
        <span className="text-slate-300 font-medium">每日进度</span>
        <span className="text-sky-400 font-semibold">{current} / {total} 个任务</span>
      </div>
      <div className="w-full bg-slate-700 rounded-full h-4 overflow-hidden shadow-inner">
        <div
          className="bg-gradient-to-r from-sky-500 to-cyan-400 h-4 rounded-full transition-all duration-500 ease-out"
          style={{ width: `${percentage}%` }}
          role="progressbar"
          aria-valuenow={percentage}
          aria-valuemin={0}
          aria-valuemax={100}
          aria-label="每日任务进度"
        ></div>
      </div>
       {percentage === 100 && total > 0 && (
        <p className="text-center text-green-400 mt-2 text-sm font-medium">今日任务全部完成！🎉</p>
      )}
    </div>
  );
};

export default ProgressBar;
